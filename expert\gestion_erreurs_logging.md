# Gestion d'Erreurs et Logging - Bonnes Pratiques 2024

## 🚨 Gestion d'Exceptions Avancée

### Hiérarchie d'Exceptions Personnalisées
```python
# Base exception pour l'application
class AppError(Exception):
    """Exception de base pour l'application."""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> dict:
        """Convertir l'exception en dictionnaire."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "type": self.__class__.__name__
        }

# Exceptions spécifiques par domaine
class ValidationError(AppError):
    """Erreur de validation des données."""
    pass

class BusinessLogicError(AppError):
    """Erreur de logique métier."""
    pass

class ExternalServiceError(AppError):
    """Erreur de service externe."""
    
    def __init__(self, message: str, service_name: str, status_code: int = None, **kwargs):
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.status_code = status_code
        self.details.update({
            "service_name": service_name,
            "status_code": status_code
        })

class DatabaseError(AppError):
    """Erreur de base de données."""
    
    def __init__(self, message: str, query: str = None, **kwargs):
        super().__init__(message, **kwargs)
        self.query = query
        if query:
            self.details["query"] = query

# Exceptions de ressources
class ResourceNotFoundError(AppError):
    """Ressource non trouvée."""
    
    def __init__(self, resource_type: str, resource_id: str, **kwargs):
        message = f"{resource_type} with ID '{resource_id}' not found"
        super().__init__(message, **kwargs)
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.details.update({
            "resource_type": resource_type,
            "resource_id": resource_id
        })

class ResourceAlreadyExistsError(AppError):
    """Ressource déjà existante."""
    pass

class InsufficientPermissionsError(AppError):
    """Permissions insuffisantes."""
    
    def __init__(self, required_permission: str, user_id: str = None, **kwargs):
        message = f"Permission '{required_permission}' required"
        super().__init__(message, **kwargs)
        self.required_permission = required_permission
        self.user_id = user_id
        self.details.update({
            "required_permission": required_permission,
            "user_id": user_id
        })
```

### Patterns de Gestion d'Erreurs
```python
import functools
import traceback
from typing import TypeVar, Callable, Any, Optional, Union
from contextlib import contextmanager

T = TypeVar('T')

def retry_on_exception(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """Décorateur pour retry automatique avec backoff exponentiel."""
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Dernière tentative, re-lever l'exception
                        raise
                    
                    # Calculer le délai avec backoff exponentiel
                    wait_time = delay * (backoff_factor ** attempt)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {wait_time:.2f}s"
                    )
                    
                    time.sleep(wait_time)
            
            # Ne devrait jamais arriver, mais pour la sécurité des types
            raise last_exception
        
        return wrapper
    return decorator

def handle_exceptions(
    default_return: Any = None,
    log_level: str = "error",
    reraise: bool = False
):
    """Décorateur pour gérer les exceptions de manière uniforme."""
    
    def decorator(func: Callable[..., T]) -> Callable[..., Union[T, Any]]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Union[T, Any]:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Logger l'exception
                log_method = getattr(logger, log_level.lower())
                log_method(
                    f"Exception in {func.__name__}: {e}",
                    extra={
                        "function": func.__name__,
                        "args": str(args)[:200],  # Limiter la taille
                        "kwargs": str(kwargs)[:200],
                        "exception_type": type(e).__name__,
                        "traceback": traceback.format_exc()
                    }
                )
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator

@contextmanager
def error_context(operation: str, **context_data):
    """Context manager pour enrichir les erreurs avec du contexte."""
    try:
        yield
    except Exception as e:
        # Enrichir l'exception avec le contexte
        if isinstance(e, AppError):
            e.details.update({
                "operation": operation,
                **context_data
            })
        else:
            # Wrapper l'exception dans une AppError
            raise AppError(
                f"Error during {operation}: {str(e)}",
                details={
                    "operation": operation,
                    "original_exception": type(e).__name__,
                    **context_data
                }
            ) from e
        raise

# Exemple d'utilisation
class UserService:
    @retry_on_exception(max_attempts=3, exceptions=(DatabaseError, ExternalServiceError))
    @handle_exceptions(default_return=None, log_level="error")
    def get_user(self, user_id: str) -> Optional[User]:
        """Récupérer un utilisateur avec gestion d'erreurs robuste."""
        
        with error_context("get_user", user_id=user_id):
            # Validation
            if not user_id:
                raise ValidationError("User ID cannot be empty")
            
            # Recherche en base
            user = self.repository.find_by_id(user_id)
            if not user:
                raise ResourceNotFoundError("User", user_id)
            
            return user
    
    def create_user(self, user_data: dict) -> User:
        """Créer un utilisateur avec gestion d'erreurs complète."""
        
        try:
            with error_context("create_user", user_data=user_data):
                # Validation des données
                self._validate_user_data(user_data)
                
                # Vérifier l'unicité
                if self.repository.exists_by_email(user_data["email"]):
                    raise ResourceAlreadyExistsError(
                        f"User with email '{user_data['email']}' already exists"
                    )
                
                # Créer l'utilisateur
                user = User(**user_data)
                saved_user = self.repository.save(user)
                
                # Envoyer email de bienvenue
                try:
                    self.email_service.send_welcome_email(saved_user.email)
                except ExternalServiceError as e:
                    # Log l'erreur mais ne pas faire échouer la création
                    logger.warning(f"Failed to send welcome email: {e}")
                
                return saved_user
                
        except ValidationError:
            # Re-lever les erreurs de validation telles quelles
            raise
        except ResourceAlreadyExistsError:
            # Re-lever les erreurs métier telles quelles
            raise
        except Exception as e:
            # Wrapper les autres exceptions
            raise BusinessLogicError(
                "Failed to create user",
                details={"user_data": user_data}
            ) from e
```

## 📊 Logging Professionnel avec Structlog

### Configuration Structlog Avancée
```python
import structlog
import logging
import sys
from typing import Any, Dict
from datetime import datetime
import json

def setup_logging(
    level: str = "INFO",
    format_type: str = "json",  # "json" ou "console"
    service_name: str = "my-service",
    version: str = "1.0.0"
) -> None:
    """Configurer le logging structuré."""
    
    # Configuration du logging standard
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, level.upper())
    )
    
    # Processeurs communs
    shared_processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        add_service_context(service_name, version),
        structlog.processors.StackInfoRenderer(),
    ]
    
    if format_type == "json":
        # Format JSON pour la production
        processors = shared_processors + [
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer()
        ]
    else:
        # Format console pour le développement
        processors = shared_processors + [
            structlog.processors.ExceptionPrettyPrinter(),
            structlog.dev.ConsoleRenderer(colors=True)
        ]
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, level.upper())
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

def add_service_context(service_name: str, version: str):
    """Ajouter le contexte du service à tous les logs."""
    def processor(logger, method_name, event_dict):
        event_dict.update({
            "service": service_name,
            "version": version,
            "environment": os.getenv("ENVIRONMENT", "development")
        })
        return event_dict
    return processor

# Logger structuré global
logger = structlog.get_logger()

class RequestLoggingMiddleware:
    """Middleware pour logger les requêtes HTTP."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        # Contexte de la requête
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(
            request_id=request_id,
            method=scope["method"],
            path=scope["path"],
            client_ip=scope.get("client", ["unknown"])[0]
        )
        
        # Logger le début de la requête
        logger.info(
            "Request started",
            user_agent=dict(scope.get("headers", {})).get(b"user-agent", b"").decode()
        )
        
        # Wrapper pour capturer la réponse
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                status_code = message["status"]
                process_time = time.time() - start_time
                
                # Logger la fin de la requête
                log_method = logger.info if status_code < 400 else logger.error
                log_method(
                    "Request completed",
                    status_code=status_code,
                    process_time_ms=round(process_time * 1000, 2)
                )
            
            await send(message)
        
        try:
            await self.app(scope, receive, send_wrapper)
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                "Request failed",
                exception=str(e),
                exception_type=type(e).__name__,
                process_time_ms=round(process_time * 1000, 2),
                exc_info=True
            )
            raise

class DatabaseLoggingMixin:
    """Mixin pour logger les opérations de base de données."""
    
    def log_query(self, operation: str, table: str, **kwargs):
        """Logger une opération de base de données."""
        logger.info(
            "Database operation",
            operation=operation,
            table=table,
            **kwargs
        )
    
    def log_query_error(self, operation: str, table: str, error: Exception, **kwargs):
        """Logger une erreur de base de données."""
        logger.error(
            "Database operation failed",
            operation=operation,
            table=table,
            error=str(error),
            error_type=type(error).__name__,
            **kwargs
        )

# Exemple d'utilisation dans un service
class UserService(DatabaseLoggingMixin):
    def create_user(self, user_data: dict) -> User:
        """Créer un utilisateur avec logging complet."""
        
        # Contexte spécifique à l'opération
        with structlog.contextvars.bound_contextvars(
            operation="create_user",
            email=user_data.get("email", "unknown")
        ):
            logger.info("Starting user creation", user_data_keys=list(user_data.keys()))
            
            try:
                # Validation
                self._validate_user_data(user_data)
                logger.debug("User data validation passed")
                
                # Vérification d'unicité
                if self.repository.exists_by_email(user_data["email"]):
                    logger.warning("User creation failed: email already exists")
                    raise ResourceAlreadyExistsError(
                        f"User with email '{user_data['email']}' already exists"
                    )
                
                # Création
                self.log_query("INSERT", "users", email=user_data["email"])
                user = User(**user_data)
                saved_user = self.repository.save(user)
                
                logger.info(
                    "User created successfully",
                    user_id=saved_user.id,
                    created_at=saved_user.created_at.isoformat()
                )
                
                return saved_user
                
            except Exception as e:
                logger.error(
                    "User creation failed",
                    error=str(e),
                    error_type=type(e).__name__,
                    exc_info=True
                )
                raise

# Configuration pour différents environnements
def configure_production_logging():
    """Configuration pour la production."""
    setup_logging(
        level="INFO",
        format_type="json",
        service_name=os.getenv("SERVICE_NAME", "my-service"),
        version=os.getenv("SERVICE_VERSION", "unknown")
    )

def configure_development_logging():
    """Configuration pour le développement."""
    setup_logging(
        level="DEBUG",
        format_type="console",
        service_name="my-service-dev",
        version="dev"
    )

# Métriques et monitoring
class MetricsLogger:
    """Logger pour les métriques applicatives."""
    
    def __init__(self):
        self.metrics_logger = structlog.get_logger("metrics")
    
    def log_business_metric(self, metric_name: str, value: float, **tags):
        """Logger une métrique métier."""
        self.metrics_logger.info(
            "business_metric",
            metric_name=metric_name,
            value=value,
            timestamp=datetime.utcnow().isoformat(),
            **tags
        )
    
    def log_performance_metric(self, operation: str, duration_ms: float, **tags):
        """Logger une métrique de performance."""
        self.metrics_logger.info(
            "performance_metric",
            operation=operation,
            duration_ms=duration_ms,
            timestamp=datetime.utcnow().isoformat(),
            **tags
        )

# Décorateur pour mesurer les performances
def measure_performance(operation_name: str = None):
    """Décorateur pour mesurer et logger les performances."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                MetricsLogger().log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    status="success"
                )
                
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                MetricsLogger().log_performance_metric(
                    operation=op_name,
                    duration_ms=duration_ms,
                    status="error",
                    error_type=type(e).__name__
                )
                
                raise
        
        return wrapper
    return decorator
```

Ces pratiques de gestion d'erreurs et de logging permettent de construire des applications robustes et facilement debuggables en production.
