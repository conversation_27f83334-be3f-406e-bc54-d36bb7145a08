# Outils et Environnement - Niveau Expert

## 🔧 Configuration d'Environnement de Développement

### Configuration VS Code Avancée
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.linting.banditEnabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "python.sortImports.args": ["--profile", "black"],
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": [
        "tests",
        "--verbose",
        "--cov=src",
        "--cov-report=html"
    ],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true,
        "source.fixAll": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true,
        "**/htmlcov": true
    },
    "python.analysis.typeCheckingMode": "strict",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.completeFunctionParens": true
}

// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src"
            }
        },
        {
            "name": "Python: Flask App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/app.py",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "args": ["run", "--host", "0.0.0.0", "--port", "5000"],
            "jinja": true
        },
        {
            "name": "Python: Django",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/manage.py",
            "args": ["runserver", "0.0.0.0:8000"],
            "django": true
        },
        {
            "name": "Python: Pytest",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": ["tests/", "-v", "--tb=short"],
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ]
}

// .vscode/tasks.json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Run Tests",
            "type": "shell",
            "command": "python",
            "args": ["-m", "pytest", "tests/", "-v", "--cov=src"],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "Format Code",
            "type": "shell",
            "command": "black",
            "args": ["src/", "tests/"],
            "group": "build"
        },
        {
            "label": "Lint Code",
            "type": "shell",
            "command": "flake8",
            "args": ["src/", "tests/"],
            "group": "build"
        },
        {
            "label": "Type Check",
            "type": "shell",
            "command": "mypy",
            "args": ["src/"],
            "group": "build"
        },
        {
            "label": "Security Check",
            "type": "shell",
            "command": "bandit",
            "args": ["-r", "src/"],
            "group": "build"
        }
    ]
}
```

### Configuration Git Avancée
```bash
# .gitconfig global
[user]
    name = Your Name
    email = <EMAIL>

[core]
    editor = code --wait
    autocrlf = input
    excludesfile = ~/.gitignore_global

[init]
    defaultBranch = main

[pull]
    rebase = true

[push]
    default = current
    autoSetupRemote = true

[merge]
    tool = vscode
    conflictstyle = diff3

[mergetool "vscode"]
    cmd = code --wait $MERGED

[diff]
    tool = vscode

[difftool "vscode"]
    cmd = code --wait --diff $LOCAL $REMOTE

[alias]
    # Aliases utiles
    st = status
    co = checkout
    br = branch
    ci = commit
    unstage = reset HEAD --
    last = log -1 HEAD
    visual = !gitk
    
    # Logs formatés
    lg = log --oneline --graph --decorate --all
    lol = log --graph --decorate --pretty=oneline --abbrev-commit
    lola = log --graph --decorate --pretty=oneline --abbrev-commit --all
    
    # Branches
    branches = branch -a
    remotes = remote -v
    
    # Stash
    stash-all = stash save --include-untracked
    
    # Rebase interactif
    ri = rebase --interactive
    
    # Amend sans changer le message
    amend = commit --amend --no-edit

[color]
    ui = auto

[color "branch"]
    current = yellow reverse
    local = yellow
    remote = green

[color "diff"]
    meta = yellow bold
    frag = magenta bold
    old = red bold
    new = green bold

[color "status"]
    added = yellow
    changed = green
    untracked = cyan
```

### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements
      - id: check-docstring-first

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings, flake8-import-order]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-PyYAML]

  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]

  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.7.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]

  - repo: https://github.com/adamchainz/django-upgrade
    rev: 1.14.0
    hooks:
      - id: django-upgrade
        args: [--target-version, "4.2"]

  - repo: local
    hooks:
      - id: pytest-check
        name: pytest-check
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
        args: [tests/, --tb=short]
```

## 🐍 Gestion d'Environnements Python

### Configuration pyproject.toml Complète
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "myproject"
version = "1.0.0"
description = "Description du projet"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
maintainers = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["python", "example"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "pydantic>=2.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.11.0",
    "redis>=4.6.0",
    "celery>=5.3.0",
    "httpx>=0.24.0",
    "structlog>=23.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "bandit>=1.7.5",
    "pre-commit>=3.3.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "factory-boy>=3.3.0",
    "faker>=19.3.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]
prod = [
    "gunicorn>=21.2.0",
    "psycopg2-binary>=2.9.0",
]

[project.urls]
Homepage = "https://github.com/username/myproject"
Documentation = "https://myproject.readthedocs.io/"
Repository = "https://github.com/username/myproject.git"
"Bug Tracker" = "https://github.com/username/myproject/issues"

[project.scripts]
myproject = "myproject.cli:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
myproject = ["py.typed", "*.pyi"]

# Configuration Black
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Configuration isort
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["myproject"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy"]

# Configuration mypy
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

# Configuration pytest
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/myproject",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=90",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Configuration coverage
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Configuration bandit
[tool.bandit]
exclude_dirs = ["tests", "venv"]
skips = ["B101", "B601"]

# Configuration ruff (alternative à flake8)
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]
```

### Makefile pour Automatisation
```makefile
# Makefile pour automatiser les tâches de développement

.PHONY: help install install-dev test lint format type-check security clean build docs serve

# Variables
PYTHON := python3.11
VENV := venv
PIP := $(VENV)/bin/pip
PYTHON_VENV := $(VENV)/bin/python

# Couleurs pour l'affichage
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

help: ## Afficher l'aide
	@echo "$(BLUE)Commandes disponibles:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

install: ## Installer les dépendances de production
	@echo "$(YELLOW)Installation des dépendances de production...$(NC)"
	$(PYTHON) -m venv $(VENV)
	$(PIP) install --upgrade pip setuptools wheel
	$(PIP) install -e .

install-dev: ## Installer les dépendances de développement
	@echo "$(YELLOW)Installation des dépendances de développement...$(NC)"
	$(PYTHON) -m venv $(VENV)
	$(PIP) install --upgrade pip setuptools wheel
	$(PIP) install -e ".[dev,test,docs]"
	$(VENV)/bin/pre-commit install

test: ## Exécuter les tests
	@echo "$(YELLOW)Exécution des tests...$(NC)"
	$(PYTHON_VENV) -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing

test-fast: ## Exécuter les tests rapides seulement
	@echo "$(YELLOW)Exécution des tests rapides...$(NC)"
	$(PYTHON_VENV) -m pytest tests/ -v -m "not slow"

lint: ## Vérifier le style de code
	@echo "$(YELLOW)Vérification du style de code...$(NC)"
	$(VENV)/bin/flake8 src/ tests/
	$(VENV)/bin/isort --check-only src/ tests/
	$(VENV)/bin/black --check src/ tests/

format: ## Formater le code
	@echo "$(YELLOW)Formatage du code...$(NC)"
	$(VENV)/bin/isort src/ tests/
	$(VENV)/bin/black src/ tests/

type-check: ## Vérifier les types
	@echo "$(YELLOW)Vérification des types...$(NC)"
	$(VENV)/bin/mypy src/

security: ## Vérifier la sécurité
	@echo "$(YELLOW)Vérification de la sécurité...$(NC)"
	$(VENV)/bin/bandit -r src/
	$(VENV)/bin/safety check

quality: lint type-check security ## Exécuter toutes les vérifications de qualité

clean: ## Nettoyer les fichiers temporaires
	@echo "$(YELLOW)Nettoyage...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

build: clean ## Construire le package
	@echo "$(YELLOW)Construction du package...$(NC)"
	$(PYTHON_VENV) -m build

docs: ## Générer la documentation
	@echo "$(YELLOW)Génération de la documentation...$(NC)"
	cd docs && $(VENV)/bin/sphinx-build -b html . _build/html

serve-docs: docs ## Servir la documentation localement
	@echo "$(YELLOW)Serveur de documentation démarré sur http://localhost:8000$(NC)"
	cd docs/_build/html && $(PYTHON) -m http.server 8000

serve: ## Démarrer le serveur de développement
	@echo "$(YELLOW)Démarrage du serveur de développement...$(NC)"
	$(PYTHON_VENV) -m uvicorn myproject.main:app --reload --host 0.0.0.0 --port 8000

docker-build: ## Construire l'image Docker
	@echo "$(YELLOW)Construction de l'image Docker...$(NC)"
	docker build -t myproject:latest .

docker-run: ## Exécuter le conteneur Docker
	@echo "$(YELLOW)Exécution du conteneur Docker...$(NC)"
	docker run -p 8000:8000 myproject:latest

ci: quality test ## Exécuter le pipeline CI complet
	@echo "$(GREEN)Pipeline CI terminé avec succès!$(NC)"

# Tâches de base de données (exemple avec Alembic)
db-upgrade: ## Appliquer les migrations de base de données
	@echo "$(YELLOW)Application des migrations...$(NC)"
	$(PYTHON_VENV) -m alembic upgrade head

db-downgrade: ## Annuler la dernière migration
	@echo "$(YELLOW)Annulation de la dernière migration...$(NC)"
	$(PYTHON_VENV) -m alembic downgrade -1

db-migration: ## Créer une nouvelle migration
	@echo "$(YELLOW)Création d'une nouvelle migration...$(NC)"
	@read -p "Nom de la migration: " name; \
	$(PYTHON_VENV) -m alembic revision --autogenerate -m "$$name"

# Tâches de déploiement
deploy-staging: ## Déployer en staging
	@echo "$(YELLOW)Déploiement en staging...$(NC)"
	# Commandes de déploiement staging

deploy-prod: ## Déployer en production
	@echo "$(RED)Déploiement en production...$(NC)"
	@read -p "Êtes-vous sûr de vouloir déployer en production? [y/N] " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		echo "Déploiement en cours..."; \
		# Commandes de déploiement production \
	else \
		echo "Déploiement annulé."; \
	fi
```

Ces configurations et outils sont essentiels pour maintenir un environnement de développement Python professionnel et efficace au niveau expert.
