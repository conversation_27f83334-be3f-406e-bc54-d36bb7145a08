================================================================================
ANALYSE GRANULAIRE DIFF(n) → PATTERN(n+1)
400 STRATIFICATIONS DE 0.01 POUR ANALYSE PRÉCISE
================================================================================

MÉTHODOLOGIE
----------------------------------------
• Pour chaque main n, on associe DIFF(n) avec le Pattern(n+1)
• 400 stratifications de 0.01 : DIFF 0.00 à 3.99
• Pour chaque valeur DIFF précise, comptage exact S/O à n+1
• Analyse granulaire pour identifier correspondances exactes

RÉSUMÉ GLOBAL
----------------------------------------
Total observations prédictives: 543,748
Nombre de stratifications avec données: 52
Plage DIFF analysée: 0.00 à 3.99 par pas de 0.01

ANALYSE STATISTIQUE AVANCÉE GLOBALE
================================================================================

📊 DISTRIBUTION DIFF POUR PATTERNS S:
--------------------------------------------------
Nombre d'observations: 271,714
Moyenne: 0.141583
Écart-type: 0.057300
Variance: 0.003283
Médiane (Q2): 0.124916
Q1 (25e percentile): 0.112308
Q3 (75e percentile): 0.153384
IQR (Q3-Q1): 0.041076
Skewness (asymétrie): 2.322647
Kurtosis (aplatissement): 12.399747
Min: 0.000000
Max: 1.325191

📊 DISTRIBUTION DIFF POUR PATTERNS O:
--------------------------------------------------
Nombre d'observations: 272,034
Moyenne: 0.141806
Écart-type: 0.057740
Variance: 0.003334
Médiane (Q2): 0.124916
Q1 (25e percentile): 0.112308
Q3 (75e percentile): 0.153384
IQR (Q3-Q1): 0.041076
Skewness (asymétrie): 2.540439
Kurtosis (aplatissement): 17.641603
Min: 0.000000
Max: 1.325191

🔍 ANALYSE COMPARATIVE S vs O:
--------------------------------------------------
Différence absolue moyennes: 0.000223
Différence relative: 0.2%
Ratio O/S: 1.002
Test t de Student: t=1.430, p-value=1.53e-01
❌ Différence non significative

ANALYSE GRANULAIRE PAR VALEURS DIFF PRÉCISES
================================================================================
Format: DIFF_valeur | Observations | S_count | O_count | %S | %O
--------------------------------------------------------------------------------
DIFF_0.00 |  231 obs | S:126 | O:105 |  54.5% |  45.5%
DIFF_0.02 | 13175 obs | S:6593 | O:6582 |  50.0% |  50.0%
DIFF_0.03 | 3547 obs | S:1795 | O:1752 |  50.6% |  49.4%
DIFF_0.04 | 1296 obs | S:648 | O:648 |  50.0% |  50.0%
DIFF_0.05 |  377 obs | S:188 | O:189 |  49.9% |  50.1%
DIFF_0.06 |  335 obs | S:166 | O:169 |  49.6% |  50.4%
DIFF_0.07 |   21 obs | S: 12 | O:  9 |  57.1% |  42.9% ⭐ S
DIFF_0.08 |  323 obs | S:151 | O:172 |  46.7% |  53.3%
DIFF_0.10 | 80409 obs | S:40197 | O:40212 |  50.0% |  50.0%
DIFF_0.11 | 128974 obs | S:64324 | O:64650 |  49.9% |  50.1%
DIFF_0.12 | 80761 obs | S:40639 | O:40122 |  50.3% |  49.7%
DIFF_0.13 | 51263 obs | S:25667 | O:25596 |  50.1% |  49.9%
DIFF_0.14 | 36325 obs | S:18151 | O:18174 |  50.0% |  50.0%
DIFF_0.15 | 30198 obs | S:15105 | O:15093 |  50.0% |  50.0%
DIFF_0.16 | 21066 obs | S:10506 | O:10560 |  49.9% |  50.1%
DIFF_0.17 | 18547 obs | S:9249 | O:9298 |  49.9% |  50.1%
DIFF_0.18 | 10540 obs | S:5234 | O:5306 |  49.7% |  50.3%
DIFF_0.19 | 9682 obs | S:4831 | O:4851 |  49.9% |  50.1%
DIFF_0.20 | 9185 obs | S:4496 | O:4689 |  48.9% |  51.1%
DIFF_0.21 | 1214 obs | S:604 | O:610 |  49.8% |  50.2%
DIFF_0.22 | 9225 obs | S:4598 | O:4627 |  49.8% |  50.2%
DIFF_0.23 |  497 obs | S:262 | O:235 |  52.7% |  47.3%
DIFF_0.24 | 8912 obs | S:4435 | O:4477 |  49.8% |  50.2%
DIFF_0.25 |  386 obs | S:190 | O:196 |  49.2% |  50.8%
DIFF_0.26 | 8696 obs | S:4324 | O:4372 |  49.7% |  50.3%
DIFF_0.27 |  446 obs | S:208 | O:238 |  46.6% |  53.4%
DIFF_0.28 |   42 obs | S: 20 | O: 22 |  47.6% |  52.4%
DIFF_0.29 |  334 obs | S:160 | O:174 |  47.9% |  52.1%
DIFF_0.30 |   25 obs | S: 16 | O:  9 |  64.0% |  36.0% 🎯 FORT S
DIFF_0.31 | 8652 obs | S:4283 | O:4369 |  49.5% |  50.5%
DIFF_0.32 |   73 obs | S: 36 | O: 37 |  49.3% |  50.7%
DIFF_0.33 |  306 obs | S:151 | O:155 |  49.3% |  50.7%
DIFF_0.34 |   28 obs | S:  8 | O: 20 |  28.6% |  71.4% 🎯 FORT O
DIFF_0.35 |   24 obs | S:  8 | O: 16 |  33.3% |  66.7% 🎯 FORT O
DIFF_0.37 |  322 obs | S:173 | O:149 |  53.7% |  46.3%
DIFF_0.38 |   20 obs | S: 10 | O: 10 |  50.0% |  50.0%
DIFF_0.39 | 7614 obs | S:3811 | O:3803 |  50.1% |  49.9%
DIFF_0.41 |   15 obs | S:  6 | O:  9 |  40.0% |  60.0% ⭐ O
DIFF_0.43 |  256 obs | S:132 | O:124 |  51.6% |  48.4%
DIFF_0.44 |   17 obs | S:  7 | O: 10 |  41.2% |  58.8% ⭐ O
DIFF_0.48 |   22 obs | S: 12 | O: 10 |  54.5% |  45.5%
DIFF_0.54 |   15 obs | S:  7 | O:  8 |  46.7% |  53.3%
DIFF_0.55 |  278 obs | S:143 | O:135 |  51.4% |  48.6%
DIFF_0.63 |   18 obs | S:  8 | O: 10 |  44.4% |  55.6% ⭐ O
DIFF_0.81 |   19 obs | S: 10 | O:  9 |  52.6% |  47.4%
DIFF_1.32 |   23 obs | S:  7 | O: 16 |  30.4% |  69.6% 🎯 FORT O


SYNTHÈSE DES MEILLEURES CORRESPONDANCES
================================================================================
🎯 MEILLEURES VALEURS DIFF POUR PRÉDIRE S:
--------------------------------------------------
  DIFF 0.30 → 64.0% S (25 obs)
  DIFF 0.07 → 57.1% S (21 obs)

🎯 MEILLEURES VALEURS DIFF POUR PRÉDIRE O:
--------------------------------------------------
  DIFF 0.34 → 71.4% O (28 obs)
  DIFF 1.32 → 69.6% O (23 obs)
  DIFF 0.35 → 66.7% O (24 obs)
  DIFF 0.41 → 60.0% O (15 obs)
  DIFF 0.44 → 58.8% O (17 obs)
  DIFF 0.63 → 55.6% O (18 obs)

🎲 RECOMMANDATIONS STRATÉGIQUES:
--------------------------------------------------
• Analyse granulaire de 52 valeurs DIFF précises
• Rechercher les valeurs DIFF avec déséquilibres >55% ou >60%
• Privilégier les valeurs avec minimum 10+ observations
• Utiliser les correspondances exactes DIFF(n) → Pattern(n+1)
• Meilleure prédiction S: DIFF 0.30 → 64.0%
• Meilleure prédiction O: DIFF 0.34 → 71.4%
