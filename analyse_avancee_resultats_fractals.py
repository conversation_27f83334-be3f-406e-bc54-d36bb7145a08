#!/usr/bin/env python3
"""
ANALYSE AVANCÉE DES RÉSULTATS FRACTALS
Exploitation des données d'analyse fractale pour insights stratégiques
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class AnalyseurResultatsFractals:
    """
    Analyseur avancé des résultats d'analyse fractale
    Génère des insights stratégiques et des visualisations
    """
    
    def __init__(self, fichier_csv: str):
        """
        Initialise l'analyseur avec les résultats CSV
        
        Args:
            fichier_csv: Chemin vers le fichier CSV des résultats
        """
        self.fichier_csv = fichier_csv
        self.df = None
        self.charger_donnees()
    
    def charger_donnees(self):
        """Charge les données depuis le fichier CSV"""
        try:
            self.df = pd.read_csv(self.fichier_csv)
            print(f"✅ Données chargées: {len(self.df)} parties analysées")
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            raise
    
    def analyser_distribution_hurst(self):
        """Analyse la distribution des exposants de Hurst"""
        print("\n📊 ANALYSE DISTRIBUTION EXPOSANTS DE HURST")
        print("=" * 50)
        
        hurst_data = self.df['hurst_resultats'].dropna()
        
        # Statistiques descriptives
        print(f"Nombre d'observations: {len(hurst_data)}")
        print(f"Moyenne: {hurst_data.mean():.4f}")
        print(f"Médiane: {hurst_data.median():.4f}")
        print(f"Écart-type: {hurst_data.std():.4f}")
        print(f"Asymétrie (skewness): {hurst_data.skew():.4f}")
        print(f"Aplatissement (kurtosis): {hurst_data.kurtosis():.4f}")
        
        # Percentiles
        percentiles = [5, 10, 25, 50, 75, 90, 95]
        print(f"\nPercentiles:")
        for p in percentiles:
            val = np.percentile(hurst_data, p)
            print(f"  P{p}: {val:.4f}")
        
        # Classification selon les seuils théoriques
        persistant = (hurst_data > 0.55).sum()
        aleatoire = ((hurst_data >= 0.45) & (hurst_data <= 0.55)).sum()
        anti_persistant = (hurst_data < 0.45).sum()
        
        print(f"\nClassification théorique:")
        print(f"  Persistant (H > 0.55): {persistant} ({persistant/len(hurst_data)*100:.1f}%)")
        print(f"  Aléatoire (0.45 ≤ H ≤ 0.55): {aleatoire} ({aleatoire/len(hurst_data)*100:.1f}%)")
        print(f"  Anti-persistant (H < 0.45): {anti_persistant} ({anti_persistant/len(hurst_data)*100:.1f}%)")
    
    def analyser_correlation_variables(self):
        """Analyse les corrélations entre variables"""
        print("\n🔗 ANALYSE DES CORRÉLATIONS")
        print("=" * 40)
        
        # Variables numériques d'intérêt
        variables = [
            'nb_mains_valides', 'hurst_resultats', 'hurst_index5', 
            'hurst_index1', 'hurst_index2', 'confiance_prediction',
            'probabilite_prediction', 'moyenne_resultats', 
            'variance_resultats', 'entropie_shannon'
        ]
        
        # Matrice de corrélation
        corr_matrix = self.df[variables].corr()
        
        # Afficher les corrélations les plus fortes
        print("Corrélations les plus fortes (|r| > 0.3):")
        for i in range(len(variables)):
            for j in range(i+1, len(variables)):
                corr = corr_matrix.iloc[i, j]
                if abs(corr) > 0.3:
                    print(f"  {variables[i]} ↔ {variables[j]}: {corr:.3f}")
        
        return corr_matrix
    
    def analyser_performance_predictions(self):
        """Analyse la performance des prédictions"""
        print("\n🎯 ANALYSE PERFORMANCE PRÉDICTIONS")
        print("=" * 45)
        
        # Distribution des prédictions
        pred_counts = self.df['prediction_main_suivante'].value_counts()
        print("Distribution des prédictions:")
        for pred, count in pred_counts.items():
            pct = count / len(self.df) * 100
            print(f"  {pred}: {count} ({pct:.1f}%)")
        
        # Confiance moyenne par type de prédiction
        print(f"\nConfiance moyenne par type:")
        for pred in pred_counts.index:
            subset = self.df[self.df['prediction_main_suivante'] == pred]
            conf_moy = subset['confiance_prediction'].mean()
            prob_moy = subset['probabilite_prediction'].mean()
            print(f"  {pred}: Confiance={conf_moy:.3f}, Probabilité={prob_moy:.3f}")
        
        # Relation entre Hurst et confiance
        print(f"\nRelation Hurst ↔ Confiance:")
        corr_hurst_conf = self.df['hurst_resultats'].corr(self.df['confiance_prediction'])
        print(f"  Corrélation: {corr_hurst_conf:.3f}")
        
        # Parties avec haute confiance
        haute_confiance = self.df[self.df['confiance_prediction'] > 0.5]
        print(f"\nParties haute confiance (>0.5): {len(haute_confiance)} ({len(haute_confiance)/len(self.df)*100:.1f}%)")
        if len(haute_confiance) > 0:
            print(f"  Hurst moyen: {haute_confiance['hurst_resultats'].mean():.4f}")
            print(f"  Prédictions: {haute_confiance['prediction_main_suivante'].value_counts().to_dict()}")
    
    def identifier_parties_optimales(self):
        """Identifie les parties optimales pour différentes stratégies"""
        print("\n⭐ IDENTIFICATION PARTIES OPTIMALES")
        print("=" * 45)
        
        # Stratégie 1: Parties très persistantes avec haute confiance
        persistantes_fiables = self.df[
            (self.df['type_persistance_resultats'] == 'persistant') &
            (self.df['hurst_resultats'] > 0.7) &
            (self.df['confiance_prediction'] > 0.4)
        ]
        
        print(f"🔥 Parties persistantes fiables (H>0.7, Conf>0.4): {len(persistantes_fiables)}")
        if len(persistantes_fiables) > 0:
            print(f"   Hurst moyen: {persistantes_fiables['hurst_resultats'].mean():.4f}")
            print(f"   Confiance moyenne: {persistantes_fiables['confiance_prediction'].mean():.3f}")
            print(f"   Prédictions: {persistantes_fiables['prediction_main_suivante'].value_counts().to_dict()}")
        
        # Stratégie 2: Parties anti-persistantes
        anti_persistantes = self.df[self.df['type_persistance_resultats'] == 'anti-persistant']
        print(f"\n🔄 Parties anti-persistantes: {len(anti_persistantes)}")
        if len(anti_persistantes) > 0:
            print(f"   Hurst moyen: {anti_persistantes['hurst_resultats'].mean():.4f}")
            print(f"   Confiance moyenne: {anti_persistantes['confiance_prediction'].mean():.3f}")
            print(f"   Prédictions: {anti_persistantes['prediction_main_suivante'].value_counts().to_dict()}")
        
        # Stratégie 3: Parties avec entropie faible (plus prévisibles)
        entropie_faible = self.df[self.df['entropie_shannon'] < self.df['entropie_shannon'].quantile(0.25)]
        print(f"\n🎯 Parties faible entropie (Q1): {len(entropie_faible)}")
        if len(entropie_faible) > 0:
            print(f"   Entropie moyenne: {entropie_faible['entropie_shannon'].mean():.3f}")
            print(f"   Hurst moyen: {entropie_faible['hurst_resultats'].mean():.4f}")
            print(f"   Confiance moyenne: {entropie_faible['confiance_prediction'].mean():.3f}")
        
        return {
            'persistantes_fiables': persistantes_fiables,
            'anti_persistantes': anti_persistantes,
            'entropie_faible': entropie_faible
        }
    
    def analyser_patterns_temporels(self):
        """Analyse les patterns selon le nombre de mains"""
        print("\n⏱️  ANALYSE PATTERNS TEMPORELS")
        print("=" * 40)
        
        # Grouper par quartiles de nombre de mains
        self.df['quartile_mains'] = pd.qcut(self.df['nb_mains_valides'], 4, labels=['Q1', 'Q2', 'Q3', 'Q4'])
        
        print("Analyse par quartile de nombre de mains:")
        for quartile in ['Q1', 'Q2', 'Q3', 'Q4']:
            subset = self.df[self.df['quartile_mains'] == quartile]
            print(f"\n{quartile} ({subset['nb_mains_valides'].min()}-{subset['nb_mains_valides'].max()} mains):")
            print(f"  Hurst moyen: {subset['hurst_resultats'].mean():.4f}")
            print(f"  Confiance moyenne: {subset['confiance_prediction'].mean():.3f}")
            print(f"  % Persistant: {(subset['type_persistance_resultats'] == 'persistant').mean()*100:.1f}%")
    
    def generer_recommandations_strategiques(self):
        """Génère des recommandations stratégiques basées sur l'analyse"""
        print("\n💡 RECOMMANDATIONS STRATÉGIQUES")
        print("=" * 45)
        
        hurst_moyen = self.df['hurst_resultats'].mean()
        pct_persistant = (self.df['type_persistance_resultats'] == 'persistant').mean() * 100
        confiance_moyenne = self.df['confiance_prediction'].mean()
        
        print(f"Basé sur l'analyse de {len(self.df)} parties:")
        print(f"  - Hurst moyen: {hurst_moyen:.4f}")
        print(f"  - {pct_persistant:.1f}% de parties persistantes")
        print(f"  - Confiance moyenne: {confiance_moyenne:.3f}")
        
        print(f"\n🎯 STRATÉGIES RECOMMANDÉES:")
        
        if pct_persistant > 70:
            print("1. STRATÉGIE DE SUIVI DE TENDANCE (prioritaire)")
            print("   - Identifier les parties avec H > 0.65")
            print("   - Suivre la direction de la dernière main")
            print("   - Confiance élevée pour les prédictions BANKER/PLAYER")
        
        if hurst_moyen > 0.6:
            print("2. STRATÉGIE DE MOMENTUM")
            print("   - Exploiter la persistance générale du système")
            print("   - Augmenter les mises sur les tendances confirmées")
            print("   - Éviter les changements de direction fréquents")
        
        parties_haute_conf = (self.df['confiance_prediction'] > 0.4).sum()
        if parties_haute_conf > len(self.df) * 0.2:
            print("3. STRATÉGIE SÉLECTIVE")
            print(f"   - {parties_haute_conf} parties avec confiance > 0.4")
            print("   - Jouer uniquement les prédictions haute confiance")
            print("   - Éviter les parties 'ALEATOIRE'")
        
        print(f"\n⚠️  RISQUES À ÉVITER:")
        print("- Ne pas ignorer les parties anti-persistantes (retour à la moyenne)")
        print("- Adapter la stratégie selon le type de persistance détecté")
        print("- Surveiller l'entropie pour détecter les changements de régime")
    
    def sauvegarder_analyse_complete(self, nom_fichier: str = None):
        """Sauvegarde une analyse complète"""
        if nom_fichier is None:
            nom_fichier = self.fichier_csv.replace('.csv', '_analyse_avancee.txt')
        
        # Rediriger la sortie vers un fichier
        import sys
        from io import StringIO
        
        old_stdout = sys.stdout
        sys.stdout = buffer = StringIO()
        
        # Exécuter toutes les analyses
        self.analyser_distribution_hurst()
        self.analyser_correlation_variables()
        self.analyser_performance_predictions()
        self.identifier_parties_optimales()
        self.analyser_patterns_temporels()
        self.generer_recommandations_strategiques()
        
        # Récupérer le contenu
        contenu = buffer.getvalue()
        sys.stdout = old_stdout
        
        # Sauvegarder
        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write(f"ANALYSE AVANCÉE RÉSULTATS FRACTALS\n")
            f.write(f"Généré le: {pd.Timestamp.now()}\n")
            f.write(f"Source: {self.fichier_csv}\n")
            f.write("=" * 60 + "\n\n")
            f.write(contenu)
        
        print(f"💾 Analyse complète sauvegardée: {nom_fichier}")
        return nom_fichier

def main():
    """Fonction principale"""
    print("📊 ANALYSEUR AVANCÉ RÉSULTATS FRACTALS")
    print("=" * 50)
    
    # Chercher le fichier CSV le plus récent
    import glob
    import os
    
    fichiers_csv = glob.glob("analyse_fractale_baccarat_*.csv")
    if not fichiers_csv:
        print("❌ Aucun fichier de résultats trouvé")
        print("   Exécutez d'abord analyseur_fractal_baccarat.py")
        return
    
    # Prendre le plus récent
    fichier_recent = max(fichiers_csv, key=os.path.getctime)
    print(f"📂 Fichier analysé: {fichier_recent}")
    
    try:
        # Créer l'analyseur
        analyseur = AnalyseurResultatsFractals(fichier_recent)
        
        # Exécuter toutes les analyses
        print("\n🚀 Démarrage analyse avancée...")
        analyseur.analyser_distribution_hurst()
        analyseur.analyser_correlation_variables()
        analyseur.analyser_performance_predictions()
        parties_optimales = analyseur.identifier_parties_optimales()
        analyseur.analyser_patterns_temporels()
        analyseur.generer_recommandations_strategiques()
        
        # Sauvegarder l'analyse
        nom_rapport = analyseur.sauvegarder_analyse_complete()
        
        print(f"\n✅ ANALYSE AVANCÉE TERMINÉE")
        print(f"📄 Rapport sauvegardé: {nom_rapport}")
        
        return analyseur, parties_optimales
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
