# Frameworks et Technologies - Niveau Expert

## 🌐 Django Expert Level

### Optimisation des Requêtes et Performance
```python
from django.db import models
from django.db.models import Prefetch, Q, F, Case, When, Value
from django.core.cache import cache

# Optimisation avec select_related et prefetch_related
def get_posts_optimized():
    """Récupérer les posts avec optimisation des requêtes"""
    return Post.objects.select_related('author').prefetch_related(
        'tags',
        'comments__author',
        Prefetch(
            'comments',
            queryset=Comment.objects.select_related('author').filter(is_approved=True),
            to_attr='approved_comments'
        )
    ).annotate(
        comment_count=Count('comments'),
        avg_rating=Avg('ratings__score')
    ).filter(status='published')

# Requêtes conditionnelles avec Case/When
def get_user_status_summary():
    """Résumé du statut des utilisateurs avec requête conditionnelle"""
    return User.objects.aggregate(
        total_users=Count('id'),
        active_users=Count(Case(
            When(is_active=True, then=Value(1)),
            output_field=models.IntegerField()
        )),
        premium_users=Count(Case(
            When(subscription__plan='premium', then=Value(1)),
            output_field=models.IntegerField()
        )),
        recent_users=Count(Case(
            When(date_joined__gte=timezone.now() - timedelta(days=30), then=Value(1)),
            output_field=models.IntegerField()
        ))
    )

# Requêtes raw pour performance critique
def get_complex_analytics():
    """Requête SQL complexe pour analytics"""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT
                DATE_TRUNC('month', created_at) as month,
                COUNT(*) as post_count,
                AVG(rating) as avg_rating,
                COUNT(DISTINCT author_id) as unique_authors
            FROM blog_post
            WHERE status = 'published'
                AND created_at >= %s
            GROUP BY DATE_TRUNC('month', created_at)
            ORDER BY month DESC
        """, [timezone.now() - timedelta(days=365)])

        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]
```

## 🌐 Django Expert Level

### Custom Model Managers et QuerySets
```python
from django.db import models
from django.db.models import Q, F, Count, Avg
from django.contrib.auth.models import AbstractUser

class ActiveUserManager(models.Manager):
    """Manager personnalisé pour les utilisateurs actifs"""
    
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)
    
    def with_recent_activity(self, days=30):
        from django.utils import timezone
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=days)
        return self.get_queryset().filter(last_login__gte=cutoff_date)
    
    def top_contributors(self, limit=10):
        return self.get_queryset().annotate(
            post_count=Count('posts'),
            avg_rating=Avg('posts__rating')
        ).filter(post_count__gt=0).order_by('-post_count')[:limit]

class User(AbstractUser):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    bio = models.TextField(blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True)
    
    # Managers
    objects = models.Manager()  # Manager par défaut
    active = ActiveUserManager()  # Manager personnalisé
    
    class Meta:
        indexes = [
            models.Index(fields=['email', 'is_active']),
            models.Index(fields=['last_login']),
        ]

class PostQuerySet(models.QuerySet):
    """QuerySet personnalisé pour les posts"""
    
    def published(self):
        return self.filter(status='published')
    
    def by_author(self, author):
        return self.filter(author=author)
    
    def with_high_rating(self, min_rating=4.0):
        return self.filter(rating__gte=min_rating)
    
    def recent(self, days=7):
        from django.utils import timezone
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=days)
        return self.filter(created_at__gte=cutoff_date)

class Post(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')
    status = models.CharField(max_length=20, choices=[
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('archived', 'Archived')
    ], default='draft')
    rating = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    objects = PostQuerySet.as_manager()
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['author', 'status']),
        ]
```

### Middleware Avancé
```python
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class PerformanceMiddleware(MiddlewareMixin):
    """Middleware pour mesurer les performances"""
    
    def process_request(self, request):
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            response['X-Response-Time'] = f"{duration:.3f}s"
            
            # Log des requêtes lentes
            if duration > 1.0:  # Plus d'1 seconde
                logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.3f}s"
                )
        
        return response

class RateLimitMiddleware(MiddlewareMixin):
    """Middleware de limitation de taux"""
    
    def process_request(self, request):
        if not settings.ENABLE_RATE_LIMITING:
            return None
        
        # Identifier l'utilisateur (IP ou user_id)
        identifier = self.get_identifier(request)
        
        # Clé de cache pour le rate limiting
        cache_key = f"rate_limit:{identifier}"
        
        # Obtenir le nombre de requêtes actuelles
        current_requests = cache.get(cache_key, 0)
        
        # Limite: 100 requêtes par minute
        if current_requests >= 100:
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'retry_after': 60
            }, status=429)
        
        # Incrémenter le compteur
        cache.set(cache_key, current_requests + 1, 60)  # TTL de 60 secondes
        
        return None
    
    def get_identifier(self, request):
        if request.user.is_authenticated:
            return f"user:{request.user.id}"
        else:
            return f"ip:{self.get_client_ip(request)}"
    
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

class SecurityHeadersMiddleware(MiddlewareMixin):
    """Middleware pour ajouter des headers de sécurité"""
    
    def process_response(self, request, response):
        # Headers de sécurité
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Content Security Policy
        if not response.get('Content-Security-Policy'):
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self';"
            )
            response['Content-Security-Policy'] = csp
        
        return response
```

### Signals et Cache Avancé
```python
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.contrib.auth import get_user_model

User = get_user_model()

@receiver(post_save, sender=Post)
def invalidate_post_cache(sender, instance, created, **kwargs):
    """Invalider le cache quand un post est modifié"""
    
    # Invalider le cache de la liste des posts
    cache.delete('posts:recent')
    cache.delete(f'posts:author:{instance.author.id}')
    
    # Invalider le cache du template fragment
    cache_key = make_template_fragment_key('post_detail', [instance.id])
    cache.delete(cache_key)
    
    # Invalider le cache de l'auteur
    cache.delete(f'user:stats:{instance.author.id}')

@receiver(post_save, sender=User)
def update_user_cache(sender, instance, created, **kwargs):
    """Mettre à jour le cache utilisateur"""
    
    if created:
        # Nouvel utilisateur - invalider les statistiques globales
        cache.delete('site:user_count')
    
    # Mettre à jour le cache de l'utilisateur
    cache.set(f'user:{instance.id}', {
        'id': instance.id,
        'username': instance.username,
        'email': instance.email,
        'is_active': instance.is_active,
    }, timeout=3600)  # 1 heure

# Décorateur de cache personnalisé
from functools import wraps
import hashlib
import json

def cache_result(timeout=300, key_prefix=''):
    """Décorateur pour mettre en cache le résultat d'une fonction"""
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Créer une clé de cache unique
            cache_data = {
                'func_name': func.__name__,
                'args': args,
                'kwargs': kwargs
            }
            cache_key = hashlib.md5(
                json.dumps(cache_data, sort_keys=True, default=str).encode()
            ).hexdigest()
            
            if key_prefix:
                cache_key = f"{key_prefix}:{cache_key}"
            
            # Vérifier le cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Calculer et mettre en cache
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            return result
        
        return wrapper
    return decorator

# Usage du décorateur
@cache_result(timeout=600, key_prefix='user_stats')
def get_user_statistics(user_id):
    """Obtenir les statistiques d'un utilisateur"""
    user = User.objects.get(id=user_id)
    return {
        'post_count': user.posts.count(),
        'avg_rating': user.posts.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0,
        'recent_posts': user.posts.recent(days=30).count(),
    }
```

## ⚡ FastAPI Expert Level

### Dependency Injection Avancé
```python
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional, Generator
import jwt
from datetime import datetime, timedelta

app = FastAPI()

# Configuration
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

security = HTTPBearer()

# Database dependency
def get_db() -> Generator[Session, None, None]:
    """Dépendance pour obtenir une session de base de données"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Obtenir l'utilisateur actuel à partir du token JWT"""
    
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Vérifier que l'utilisateur est actif"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def require_permissions(*permissions: str):
    """Décorateur pour vérifier les permissions"""
    def dependency(current_user: User = Depends(get_current_active_user)):
        user_permissions = set(p.name for p in current_user.permissions)
        required_permissions = set(permissions)
        
        if not required_permissions.issubset(user_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        return current_user
    
    return dependency

# Cache dependency
from functools import lru_cache
import redis

@lru_cache()
def get_redis_client():
    """Singleton Redis client"""
    return redis.Redis(host='localhost', port=6379, db=0)

async def get_cache_service() -> CacheService:
    """Dépendance pour le service de cache"""
    redis_client = get_redis_client()
    return CacheService(redis_client)

# Usage des dépendances
@app.get("/users/me")
async def read_users_me(
    current_user: User = Depends(get_current_active_user)
):
    return current_user

@app.post("/admin/users")
async def create_user_admin(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    admin_user: User = Depends(require_permissions("user:create"))
):
    # Seuls les utilisateurs avec permission "user:create" peuvent accéder
    return create_user(db, user_data)
```

### Background Tasks et WebSockets
```python
from fastapi import BackgroundTasks, WebSocket, WebSocketDisconnect
from typing import List
import asyncio
import json

# Background tasks
async def send_email_notification(email: str, message: str):
    """Tâche en arrière-plan pour envoyer un email"""
    # Simulation d'envoi d'email
    await asyncio.sleep(2)
    print(f"Email sent to {email}: {message}")

@app.post("/send-notification/")
async def send_notification(
    email: str,
    message: str,
    background_tasks: BackgroundTasks
):
    background_tasks.add_task(send_email_notification, email, message)
    return {"message": "Notification will be sent in background"}

# WebSocket Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: dict = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.user_connections[user_id] = websocket
    
    def disconnect(self, websocket: WebSocket, user_id: str):
        self.active_connections.remove(websocket)
        if user_id in self.user_connections:
            del self.user_connections[user_id]
    
    async def send_personal_message(self, message: str, user_id: str):
        if user_id in self.user_connections:
            websocket = self.user_connections[user_id]
            await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Connexion fermée, la retirer de la liste
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await manager.connect(websocket, user_id)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data["type"] == "broadcast":
                await manager.broadcast(f"User {user_id}: {message_data['message']}")
            elif message_data["type"] == "private":
                target_user = message_data["target_user"]
                await manager.send_personal_message(
                    f"Private from {user_id}: {message_data['message']}", 
                    target_user
                )
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        await manager.broadcast(f"User {user_id} left the chat")
```

## 🌶️ Flask Expert Level

### Application Factory Pattern
```python
from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import os

# Extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
cors = CORS()
limiter = Limiter(key_func=get_remote_address)

def create_app(config_name=None):
    """Application factory"""
    app = Flask(__name__)
    
    # Configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app.config.from_object(f'config.{config_name.title()}Config')
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    cors.init_app(app)
    limiter.init_app(app)
    
    # Register blueprints
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # Error handlers
    register_error_handlers(app)
    
    # Request hooks
    register_request_hooks(app)
    
    return app

def register_error_handlers(app):
    """Enregistrer les gestionnaires d'erreur"""
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
    
    @app.errorhandler(429)
    def rate_limit_handler(e):
        return jsonify({
            'error': 'Rate limit exceeded',
            'message': str(e.description)
        }), 429

def register_request_hooks(app):
    """Enregistrer les hooks de requête"""
    
    @app.before_request
    def before_request():
        # Log de la requête
        app.logger.info(f'{request.method} {request.path} from {request.remote_addr}')
    
    @app.after_request
    def after_request(response):
        # Headers de sécurité
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
```

### Custom Decorators et Middleware
```python
from functools import wraps
from flask import request, jsonify, current_app, g
from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity
import time

def require_api_key(f):
    """Décorateur pour vérifier la clé API"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key != current_app.config['API_KEY']:
            return jsonify({'error': 'Invalid API key'}), 401
        return f(*args, **kwargs)
    return decorated_function

def require_permissions(*permissions):
    """Décorateur pour vérifier les permissions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            
            # Obtenir l'utilisateur et ses permissions
            user = User.query.get(user_id)
            if not user:
                return jsonify({'error': 'User not found'}), 404
            
            user_permissions = set(p.name for p in user.permissions)
            required_permissions = set(permissions)
            
            if not required_permissions.issubset(user_permissions):
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            g.current_user = user
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def measure_performance(f):
    """Décorateur pour mesurer les performances"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        end_time = time.time()
        
        duration = end_time - start_time
        current_app.logger.info(f'{f.__name__} took {duration:.3f} seconds')
        
        # Ajouter header de performance
        if hasattr(result, 'headers'):
            result.headers['X-Response-Time'] = f'{duration:.3f}s'
        
        return result
    return decorated_function

# Usage des décorateurs
@api_bp.route('/admin/users', methods=['POST'])
@require_api_key
@require_permissions('user:create')
@measure_performance
@limiter.limit("10 per minute")
def create_user():
    """Créer un nouvel utilisateur (admin seulement)"""
    data = request.get_json()
    
    # Validation des données
    if not data or 'email' not in data:
        return jsonify({'error': 'Email is required'}), 400
    
    # Créer l'utilisateur
    user = User(email=data['email'], name=data.get('name', ''))
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'id': user.id,
        'email': user.email,
        'name': user.name
    }), 201
```

Ces frameworks et technologies représentent le niveau expert requis pour développer des applications Python robustes et scalables.
