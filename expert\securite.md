# Sécurité - Niveau Expert

## 🔐 Authentification et Autorisation

### JWT et OAuth2 Avancé
```python
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, PublicFormat, NoEncryption
import secrets
import hashlib
import hmac

class JWTManager:
    """Gestionnaire JWT avancé avec rotation des clés"""
    
    def __init__(self, private_key: str, public_key: str, algorithm: str = "RS256"):
        self.private_key = private_key
        self.public_key = public_key
        self.algorithm = algorithm
        self.key_rotation_interval = timedelta(days=30)
        self.last_rotation = datetime.utcnow()
    
    def generate_token(self, payload: Dict[str, Any], expires_in: int = 3600) -> str:
        """Générer un token JWT avec expiration"""
        now = datetime.utcnow()
        
        # Claims standard
        claims = {
            'iat': now,
            'exp': now + timedelta(seconds=expires_in),
            'nbf': now,
            'jti': secrets.token_urlsafe(32),  # JWT ID unique
            'iss': 'myapp',  # Issuer
            'aud': 'myapp-users',  # Audience
        }
        
        # Ajouter le payload utilisateur
        claims.update(payload)
        
        return jwt.encode(claims, self.private_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Vérifier et décoder un token JWT"""
        try:
            payload = jwt.decode(
                token, 
                self.public_key, 
                algorithms=[self.algorithm],
                audience='myapp-users',
                issuer='myapp'
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise ValueError("Token expired")
        except jwt.InvalidTokenError:
            raise ValueError("Invalid token")
    
    def refresh_token(self, refresh_token: str) -> Optional[str]:
        """Rafraîchir un token d'accès"""
        try:
            payload = self.verify_token(refresh_token)
            
            # Vérifier que c'est un refresh token
            if payload.get('type') != 'refresh':
                raise ValueError("Not a refresh token")
            
            # Générer un nouveau token d'accès
            new_payload = {
                'user_id': payload['user_id'],
                'type': 'access',
                'permissions': payload.get('permissions', [])
            }
            
            return self.generate_token(new_payload)
        except Exception:
            return None
    
    def should_rotate_keys(self) -> bool:
        """Vérifier si les clés doivent être rotées"""
        return datetime.utcnow() - self.last_rotation > self.key_rotation_interval

class PasswordManager:
    """Gestionnaire de mots de passe sécurisé"""
    
    @staticmethod
    def generate_salt() -> str:
        """Générer un salt aléatoire"""
        return secrets.token_hex(32)
    
    @staticmethod
    def hash_password(password: str, salt: str) -> str:
        """Hasher un mot de passe avec salt"""
        return hashlib.pbkdf2_hex(
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000,  # 100k iterations
            64  # 64 bytes
        )
    
    @staticmethod
    def verify_password(password: str, salt: str, hashed: str) -> bool:
        """Vérifier un mot de passe"""
        return hmac.compare_digest(
            PasswordManager.hash_password(password, salt),
            hashed
        )
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """Vérifier la force d'un mot de passe"""
        import re
        
        checks = {
            'length': len(password) >= 12,
            'uppercase': bool(re.search(r'[A-Z]', password)),
            'lowercase': bool(re.search(r'[a-z]', password)),
            'digits': bool(re.search(r'\d', password)),
            'special': bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password)),
            'no_common': password.lower() not in [
                'password', '123456', 'qwerty', 'admin', 'letmein'
            ]
        }
        
        score = sum(checks.values())
        strength = 'weak' if score < 4 else 'medium' if score < 6 else 'strong'
        
        return {
            'score': score,
            'strength': strength,
            'checks': checks,
            'recommendations': PasswordManager._get_recommendations(checks)
        }
    
    @staticmethod
    def _get_recommendations(checks: Dict[str, bool]) -> List[str]:
        """Obtenir des recommandations pour améliorer le mot de passe"""
        recommendations = []
        
        if not checks['length']:
            recommendations.append("Utilisez au moins 12 caractères")
        if not checks['uppercase']:
            recommendations.append("Ajoutez des lettres majuscules")
        if not checks['lowercase']:
            recommendations.append("Ajoutez des lettres minuscules")
        if not checks['digits']:
            recommendations.append("Ajoutez des chiffres")
        if not checks['special']:
            recommendations.append("Ajoutez des caractères spéciaux")
        if not checks['no_common']:
            recommendations.append("Évitez les mots de passe communs")
        
        return recommendations

# Décorateur pour l'authentification
from functools import wraps
from flask import request, jsonify, g

def require_auth(permissions: List[str] = None):
    """Décorateur pour vérifier l'authentification et les permissions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = None
            
            # Récupérer le token depuis l'header Authorization
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            
            if not token:
                return jsonify({'error': 'Token missing'}), 401
            
            try:
                jwt_manager = JWTManager(private_key, public_key)
                payload = jwt_manager.verify_token(token)
                
                # Vérifier les permissions si spécifiées
                if permissions:
                    user_permissions = set(payload.get('permissions', []))
                    required_permissions = set(permissions)
                    
                    if not required_permissions.issubset(user_permissions):
                        return jsonify({'error': 'Insufficient permissions'}), 403
                
                # Stocker les informations utilisateur dans g
                g.current_user = payload
                
            except ValueError as e:
                return jsonify({'error': str(e)}), 401
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

### Protection CSRF et XSS
```python
import secrets
from flask import session, request, abort
from markupsafe import Markup, escape
import bleach
import html

class CSRFProtection:
    """Protection CSRF avancée"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialiser la protection CSRF pour l'application"""
        app.config.setdefault('CSRF_TOKEN_TIMEOUT', 3600)  # 1 heure
        app.before_request(self._check_csrf_token)
        app.context_processor(self._inject_csrf_token)
    
    def generate_csrf_token(self) -> str:
        """Générer un token CSRF"""
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_urlsafe(32)
        return session['csrf_token']
    
    def _check_csrf_token(self):
        """Vérifier le token CSRF pour les requêtes modifiantes"""
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            token = session.get('csrf_token')
            
            # Récupérer le token depuis le formulaire ou l'header
            submitted_token = (
                request.form.get('csrf_token') or 
                request.headers.get('X-CSRF-Token')
            )
            
            if not token or not submitted_token or not secrets.compare_digest(token, submitted_token):
                abort(403, description="CSRF token missing or invalid")
    
    def _inject_csrf_token(self):
        """Injecter le token CSRF dans le contexte des templates"""
        return {'csrf_token': self.generate_csrf_token}

class XSSProtection:
    """Protection contre les attaques XSS"""
    
    # Configuration pour bleach
    ALLOWED_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote'
    ]
    
    ALLOWED_ATTRIBUTES = {
        '*': ['class'],
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'width', 'height']
    }
    
    @classmethod
    def sanitize_html(cls, content: str) -> str:
        """Nettoyer le contenu HTML"""
        return bleach.clean(
            content,
            tags=cls.ALLOWED_TAGS,
            attributes=cls.ALLOWED_ATTRIBUTES,
            strip=True
        )
    
    @classmethod
    def escape_user_input(cls, content: str) -> str:
        """Échapper l'entrée utilisateur"""
        return html.escape(content)
    
    @classmethod
    def safe_render(cls, content: str, allow_html: bool = False) -> Markup:
        """Rendu sécurisé du contenu"""
        if allow_html:
            content = cls.sanitize_html(content)
        else:
            content = cls.escape_user_input(content)
        
        return Markup(content)

# Middleware de sécurité
class SecurityMiddleware:
    """Middleware de sécurité pour Flask"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialiser le middleware de sécurité"""
        app.after_request(self._add_security_headers)
        app.before_request(self._check_rate_limit)
    
    def _add_security_headers(self, response):
        """Ajouter des headers de sécurité"""
        # Prévenir le clickjacking
        response.headers['X-Frame-Options'] = 'DENY'
        
        # Prévenir le MIME sniffing
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # Protection XSS
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # HSTS (HTTPS Strict Transport Security)
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self';"
        )
        response.headers['Content-Security-Policy'] = csp
        
        # Referrer Policy
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response
    
    def _check_rate_limit(self):
        """Vérification basique du rate limiting"""
        # Implémentation basique - utiliser Redis en production
        pass
```

## 🔒 Chiffrement et Cryptographie

### Chiffrement Symétrique et Asymétrique
```python
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class EncryptionManager:
    """Gestionnaire de chiffrement avancé"""
    
    def __init__(self):
        self.fernet = None
    
    def generate_key_from_password(self, password: str, salt: bytes = None) -> bytes:
        """Générer une clé de chiffrement à partir d'un mot de passe"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def encrypt_symmetric(self, data: str, key: bytes) -> str:
        """Chiffrement symétrique avec Fernet"""
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_symmetric(self, encrypted_data: str, key: bytes) -> str:
        """Déchiffrement symétrique avec Fernet"""
        f = Fernet(key)
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = f.decrypt(decoded_data)
        return decrypted_data.decode()
    
    def generate_rsa_keypair(self, key_size: int = 2048) -> tuple:
        """Générer une paire de clés RSA"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size,
        )
        
        public_key = private_key.public_key()
        
        # Sérialiser les clés
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        return private_pem, public_pem
    
    def encrypt_asymmetric(self, data: str, public_key_pem: bytes) -> str:
        """Chiffrement asymétrique avec RSA"""
        public_key = serialization.load_pem_public_key(public_key_pem)
        
        encrypted_data = public_key.encrypt(
            data.encode(),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_asymmetric(self, encrypted_data: str, private_key_pem: bytes) -> str:
        """Déchiffrement asymétrique avec RSA"""
        private_key = serialization.load_pem_private_key(private_key_pem, password=None)
        
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = private_key.decrypt(
            decoded_data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return decrypted_data.decode()
    
    def sign_data(self, data: str, private_key_pem: bytes) -> str:
        """Signer des données avec RSA"""
        private_key = serialization.load_pem_private_key(private_key_pem, password=None)
        
        signature = private_key.sign(
            data.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        return base64.urlsafe_b64encode(signature).decode()
    
    def verify_signature(self, data: str, signature: str, public_key_pem: bytes) -> bool:
        """Vérifier une signature RSA"""
        try:
            public_key = serialization.load_pem_public_key(public_key_pem)
            decoded_signature = base64.urlsafe_b64decode(signature.encode())
            
            public_key.verify(
                decoded_signature,
                data.encode(),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False

# Gestionnaire de secrets
class SecretManager:
    """Gestionnaire de secrets pour l'application"""
    
    def __init__(self, encryption_key: bytes):
        self.encryption_manager = EncryptionManager()
        self.encryption_key = encryption_key
        self.secrets_cache = {}
    
    def store_secret(self, key: str, value: str) -> None:
        """Stocker un secret chiffré"""
        encrypted_value = self.encryption_manager.encrypt_symmetric(value, self.encryption_key)
        # En production, stocker dans une base de données sécurisée
        self.secrets_cache[key] = encrypted_value
    
    def get_secret(self, key: str) -> str:
        """Récupérer un secret déchiffré"""
        encrypted_value = self.secrets_cache.get(key)
        if encrypted_value:
            return self.encryption_manager.decrypt_symmetric(encrypted_value, self.encryption_key)
        raise KeyError(f"Secret '{key}' not found")
    
    def rotate_secret(self, key: str, new_value: str) -> None:
        """Faire la rotation d'un secret"""
        # Stocker l'ancienne valeur pour rollback si nécessaire
        old_value = self.get_secret(key)
        self.store_secret(f"{key}_backup", old_value)
        
        # Stocker la nouvelle valeur
        self.store_secret(key, new_value)
```

Ces techniques de sécurité sont essentielles pour protéger les applications Python au niveau expert contre les menaces modernes.
