# APIs Web Modernes avec Python 2024

## 🚀 FastAPI - Framework Moderne de Référence

### Application FastAPI Complète et Structurée
```python
from fastapi import FastAPI, HTTPException, Depends, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
import logging
from contextlib import asynccontextmanager

# Configuration et modèles
class Settings(BaseModel):
    """Configuration de l'application."""
    app_name: str = "Modern API"
    app_version: str = "1.0.0"
    debug: bool = False
    database_url: str = "postgresql://user:pass@localhost/db"
    redis_url: str = "redis://localhost:6379"
    secret_key: str = "your-secret-key"
    allowed_hosts: List[str] = ["localhost", "127.0.0.1"]
    cors_origins: List[str] = ["http://localhost:3000"]

settings = Settings()

# Modèles Pydantic
class UserBase(BaseModel):
    """Modèle de base utilisateur."""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    full_name: Optional[str] = None
    is_active: bool = True

class UserCreate(UserBase):
    """Modèle pour création d'utilisateur."""
    password: str = Field(..., min_length=8)

class UserUpdate(BaseModel):
    """Modèle pour mise à jour d'utilisateur."""
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    """Modèle de réponse utilisateur."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class PaginatedResponse(BaseModel):
    """Réponse paginée générique."""
    items: List[Any]
    total: int
    page: int = Field(..., ge=1)
    size: int = Field(..., ge=1, le=100)
    pages: int

# Services et dépendances
class DatabaseService:
    """Service de base de données."""
    
    def __init__(self):
        self.connection = None
    
    async def connect(self):
        """Connexion à la base de données."""
        # Simulation de connexion
        self.connection = "connected"
        logging.info("Database connected")
    
    async def disconnect(self):
        """Déconnexion de la base de données."""
        self.connection = None
        logging.info("Database disconnected")

class CacheService:
    """Service de cache Redis."""
    
    def __init__(self):
        self.redis = None
    
    async def connect(self):
        """Connexion à Redis."""
        self.redis = "redis_connected"
        logging.info("Redis connected")
    
    async def disconnect(self):
        """Déconnexion de Redis."""
        self.redis = None
        logging.info("Redis disconnected")
    
    async def get(self, key: str) -> Optional[str]:
        """Récupérer une valeur du cache."""
        # Simulation
        return None
    
    async def set(self, key: str, value: str, expire: int = 3600):
        """Définir une valeur dans le cache."""
        # Simulation
        pass

# Services globaux
db_service = DatabaseService()
cache_service = CacheService()

# Gestionnaire de cycle de vie
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gestionnaire de cycle de vie de l'application."""
    # Startup
    logging.info("Starting up application...")
    await db_service.connect()
    await cache_service.connect()
    
    yield
    
    # Shutdown
    logging.info("Shutting down application...")
    await db_service.disconnect()
    await cache_service.disconnect()

# Application FastAPI
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="API moderne avec FastAPI et bonnes pratiques 2024",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.allowed_hosts
)

# Middleware personnalisé pour logging des requêtes
@app.middleware("http")
async def log_requests(request, call_next):
    """Logger les requêtes HTTP."""
    start_time = datetime.utcnow()
    
    response = await call_next(request)
    
    process_time = (datetime.utcnow() - start_time).total_seconds()
    
    logging.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    return response

# Authentification
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Récupérer l'utilisateur actuel depuis le token."""
    token = credentials.credentials
    
    # Simulation de validation de token
    if token != "valid-token":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Retourner un utilisateur simulé
    return UserResponse(
        id=1,
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        is_active=True,
        created_at=datetime.utcnow()
    )

# Dépendances pour pagination
def get_pagination_params(page: int = 1, size: int = 20) -> Dict[str, int]:
    """Paramètres de pagination."""
    if page < 1:
        raise HTTPException(status_code=400, detail="Page must be >= 1")
    if size < 1 or size > 100:
        raise HTTPException(status_code=400, detail="Size must be between 1 and 100")
    
    return {"page": page, "size": size, "offset": (page - 1) * size}

# Routes
@app.get("/", tags=["Health"])
async def root():
    """Point d'entrée de l'API."""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Vérification de santé de l'API."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "connected" if db_service.connection else "disconnected",
            "cache": "connected" if cache_service.redis else "disconnected"
        }
    }

# Routes utilisateurs avec toutes les bonnes pratiques
@app.post(
    "/users/",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["Users"],
    summary="Créer un nouvel utilisateur",
    description="Créer un nouvel utilisateur avec validation complète"
)
async def create_user(
    user: UserCreate,
    background_tasks: BackgroundTasks
):
    """Créer un nouvel utilisateur."""
    
    # Vérifier si l'utilisateur existe déjà
    existing_user = await cache_service.get(f"user:email:{user.email}")
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User with this email already exists"
        )
    
    # Créer l'utilisateur (simulation)
    new_user = UserResponse(
        id=123,
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        is_active=user.is_active,
        created_at=datetime.utcnow()
    )
    
    # Tâche en arrière-plan pour envoyer un email de bienvenue
    background_tasks.add_task(send_welcome_email, user.email)
    
    # Mettre en cache
    await cache_service.set(f"user:email:{user.email}", "exists", expire=3600)
    
    return new_user

@app.get(
    "/users/",
    response_model=PaginatedResponse,
    tags=["Users"],
    summary="Lister les utilisateurs",
    description="Récupérer une liste paginée d'utilisateurs"
)
async def list_users(
    pagination: Dict[str, int] = Depends(get_pagination_params),
    current_user: UserResponse = Depends(get_current_user)
):
    """Lister les utilisateurs avec pagination."""
    
    # Simulation de récupération des utilisateurs
    users = [
        UserResponse(
            id=i,
            email=f"user{i}@example.com",
            username=f"user{i}",
            full_name=f"User {i}",
            is_active=True,
            created_at=datetime.utcnow() - timedelta(days=i)
        )
        for i in range(1, pagination["size"] + 1)
    ]
    
    total_users = 100  # Simulation
    total_pages = (total_users + pagination["size"] - 1) // pagination["size"]
    
    return PaginatedResponse(
        items=users,
        total=total_users,
        page=pagination["page"],
        size=pagination["size"],
        pages=total_pages
    )

@app.get(
    "/users/{user_id}",
    response_model=UserResponse,
    tags=["Users"],
    summary="Récupérer un utilisateur",
    description="Récupérer un utilisateur par son ID"
)
async def get_user(
    user_id: int,
    current_user: UserResponse = Depends(get_current_user)
):
    """Récupérer un utilisateur par ID."""
    
    # Vérifier dans le cache d'abord
    cached_user = await cache_service.get(f"user:id:{user_id}")
    if cached_user:
        # En réalité, on désérialiserait le JSON
        pass
    
    # Simulation de récupération depuis la base
    if user_id <= 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    user = UserResponse(
        id=user_id,
        email=f"user{user_id}@example.com",
        username=f"user{user_id}",
        full_name=f"User {user_id}",
        is_active=True,
        created_at=datetime.utcnow() - timedelta(days=user_id)
    )
    
    # Mettre en cache
    await cache_service.set(f"user:id:{user_id}", user.json(), expire=1800)
    
    return user

@app.put(
    "/users/{user_id}",
    response_model=UserResponse,
    tags=["Users"],
    summary="Mettre à jour un utilisateur",
    description="Mettre à jour les informations d'un utilisateur"
)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: UserResponse = Depends(get_current_user)
):
    """Mettre à jour un utilisateur."""
    
    # Vérifier que l'utilisateur existe
    existing_user = await get_user(user_id, current_user)
    
    # Appliquer les mises à jour
    update_data = user_update.dict(exclude_unset=True)
    updated_user = existing_user.copy(update=update_data)
    updated_user.updated_at = datetime.utcnow()
    
    # Invalider le cache
    await cache_service.set(f"user:id:{user_id}", "", expire=1)
    
    return updated_user

@app.delete(
    "/users/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["Users"],
    summary="Supprimer un utilisateur",
    description="Supprimer un utilisateur par son ID"
)
async def delete_user(
    user_id: int,
    current_user: UserResponse = Depends(get_current_user)
):
    """Supprimer un utilisateur."""
    
    # Vérifier que l'utilisateur existe
    await get_user(user_id, current_user)
    
    # Simulation de suppression
    # En réalité, on supprimerait de la base de données
    
    # Invalider le cache
    await cache_service.set(f"user:id:{user_id}", "", expire=1)
    
    return None

# Tâches en arrière-plan
async def send_welcome_email(email: str):
    """Envoyer un email de bienvenue."""
    # Simulation d'envoi d'email
    await asyncio.sleep(1)  # Simulation de latence
    logging.info(f"Welcome email sent to {email}")

# Gestion d'erreurs globale
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Gestionnaire d'exceptions HTTP personnalisé."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "timestamp": datetime.utcnow().isoformat(),
                "path": str(request.url.path)
            }
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Gestionnaire d'exceptions générales."""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "code": 500,
                "message": "Internal server error",
                "timestamp": datetime.utcnow().isoformat(),
                "path": str(request.url.path)
            }
        }
    )

# WebSockets pour temps réel
@app.websocket("/ws")
async def websocket_endpoint(websocket):
    """Endpoint WebSocket pour communication temps réel."""
    await websocket.accept()
    
    try:
        while True:
            # Recevoir des messages du client
            data = await websocket.receive_text()
            
            # Echo du message avec timestamp
            response = {
                "message": data,
                "timestamp": datetime.utcnow().isoformat(),
                "type": "echo"
            }
            
            await websocket.send_json(response)
            
    except Exception as e:
        logging.error(f"WebSocket error: {e}")
    finally:
        await websocket.close()

# Point d'entrée pour développement
if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info"
    )
```

### Tests Complets pour l'API
```python
# tests/test_api.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from main import app, get_current_user, UserResponse
from datetime import datetime

# Override de dépendance pour les tests
def override_get_current_user():
    return UserResponse(
        id=1,
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        is_active=True,
        created_at=datetime.utcnow()
    )

app.dependency_overrides[get_current_user] = override_get_current_user

client = TestClient(app)

class TestHealthEndpoints:
    """Tests des endpoints de santé."""
    
    def test_root_endpoint(self):
        """Test du point d'entrée principal."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "timestamp" in data
    
    def test_health_check(self):
        """Test du health check."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "services" in data

class TestUserEndpoints:
    """Tests des endpoints utilisateurs."""
    
    def test_create_user_success(self):
        """Test de création d'utilisateur réussie."""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "password": "SecurePass123!",
            "full_name": "New User"
        }
        
        response = client.post("/users/", json=user_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert "id" in data
        assert "created_at" in data
    
    def test_create_user_invalid_email(self):
        """Test de création avec email invalide."""
        user_data = {
            "email": "invalid-email",
            "username": "testuser",
            "password": "SecurePass123!"
        }
        
        response = client.post("/users/", json=user_data)
        assert response.status_code == 422
    
    def test_get_user_success(self):
        """Test de récupération d'utilisateur."""
        user_id = 1
        response = client.get(f"/users/{user_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == user_id
        assert "email" in data
        assert "username" in data
    
    def test_get_user_not_found(self):
        """Test de récupération d'utilisateur inexistant."""
        response = client.get("/users/0")
        assert response.status_code == 404
    
    def test_list_users_with_pagination(self):
        """Test de listage avec pagination."""
        response = client.get("/users/?page=1&size=10")
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert data["page"] == 1
        assert data["size"] == 10
    
    def test_update_user_success(self):
        """Test de mise à jour d'utilisateur."""
        user_id = 1
        update_data = {"full_name": "Updated Name"}
        
        response = client.put(f"/users/{user_id}", json=update_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["full_name"] == "Updated Name"
        assert "updated_at" in data
    
    def test_delete_user_success(self):
        """Test de suppression d'utilisateur."""
        user_id = 1
        response = client.delete(f"/users/{user_id}")
        assert response.status_code == 204

class TestAuthentication:
    """Tests d'authentification."""
    
    def test_protected_endpoint_without_token(self):
        """Test d'accès à un endpoint protégé sans token."""
        # Supprimer l'override temporairement
        app.dependency_overrides.clear()
        
        response = client.get("/users/1")
        assert response.status_code == 403
        
        # Remettre l'override
        app.dependency_overrides[get_current_user] = override_get_current_user
    
    def test_protected_endpoint_with_invalid_token(self):
        """Test avec token invalide."""
        app.dependency_overrides.clear()
        
        headers = {"Authorization": "Bearer invalid-token"}
        response = client.get("/users/1", headers=headers)
        assert response.status_code == 401
        
        app.dependency_overrides[get_current_user] = override_get_current_user

@pytest.mark.asyncio
class TestAsyncFeatures:
    """Tests des fonctionnalités asynchrones."""
    
    async def test_background_tasks(self):
        """Test des tâches en arrière-plan."""
        with patch('main.send_welcome_email', new_callable=AsyncMock) as mock_email:
            user_data = {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "SecurePass123!"
            }
            
            response = client.post("/users/", json=user_data)
            assert response.status_code == 201
            
            # Vérifier que la tâche en arrière-plan a été appelée
            # Note: Dans un vrai test, il faudrait attendre l'exécution
```

Cette structure d'API moderne avec FastAPI intègre toutes les bonnes pratiques 2024 : authentification, validation, pagination, cache, WebSockets, gestion d'erreurs, tests complets, et architecture propre.
