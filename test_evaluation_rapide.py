#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 TEST D'ÉVALUATION RAPIDE DE PERFORMANCE
==========================================
Évalue la performance du système fractal sur les résultats déjà générés
"""

import json
import pandas as pd
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def evaluer_performance_rapide():
    """
    🎯 ÉVALUATION RAPIDE SUR LES RÉSULTATS EXISTANTS
    """
    print("🧪 TEST D'ÉVALUATION RAPIDE DE PERFORMANCE")
    print("=" * 50)
    
    # Charger l'analyseur
    analyseur = AnalyseurFractalBaccarat()
    
    # Charger le dataset
    print("📂 Chargement du dataset...")
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    print(f"✅ Dataset chargé: {len(parties)} parties")
    
    # Charger les résultats d'analyse récents
    try:
        df_resultats = pd.read_csv('analyse_fractale_baccarat_20250629_132314.csv')
        print(f"📊 Résultats chargés: {len(df_resultats)} analyses")
    except Exception as e:
        print(f"❌ Erreur chargement résultats: {e}")
        return
    
    # Évaluation simplifiée : comparer prédictions avec résultats réels
    print("\n🎯 ÉVALUATION SIMPLIFIÉE")
    print("=" * 30)
    
    predictions_evaluees = 0
    predictions_correctes = 0
    predictions_banker = 0
    predictions_player = 0
    correctes_banker = 0
    correctes_player = 0
    ties_rencontres = 0
    
    # Pour chaque résultat d'analyse
    for _, row in df_resultats.iterrows():
        numero_partie = int(row['numero_partie'])
        prediction = row['prediction_main_suivante']
        
        # Trouver la partie correspondante
        partie = None
        for p in parties:
            if p.get('numero') == numero_partie:
                partie = p
                break
        
        if not partie:
            continue
            
        # Obtenir le résultat réel de la partie
        mains = partie.get('mains', [])
        if not mains:
            continue
            
        # Prendre le résultat de la dernière main comme "résultat de la partie"
        derniere_main = mains[-1]
        resultat_reel = derniere_main.get('index3_result')
        
        if not resultat_reel:
            continue
            
        # Exclure les TIE
        if resultat_reel == 'TIE':
            ties_rencontres += 1
            continue
            
        # Ignorer les prédictions non-déterministes
        if prediction in ['INDETERMINE', 'ALEATOIRE', 'ATTENDRE']:
            continue
            
        # Compter les prédictions évaluables
        predictions_evaluees += 1
        
        # Compter par type
        if prediction == 'BANKER':
            predictions_banker += 1
            if resultat_reel == 'BANKER':
                correctes_banker += 1
                predictions_correctes += 1
        elif prediction == 'PLAYER':
            predictions_player += 1
            if resultat_reel == 'PLAYER':
                correctes_player += 1
                predictions_correctes += 1
    
    # Calculer les métriques
    if predictions_evaluees > 0:
        taux_reussite_global = (predictions_correctes / predictions_evaluees) * 100
        taux_banker = (correctes_banker / predictions_banker * 100) if predictions_banker > 0 else 0
        taux_player = (correctes_player / predictions_player * 100) if predictions_player > 0 else 0
    else:
        taux_reussite_global = 0
        taux_banker = 0
        taux_player = 0
    
    # Afficher les résultats
    print(f"\n📊 RÉSULTATS DE L'ÉVALUATION RAPIDE")
    print("=" * 40)
    print(f"Prédictions évaluées: {predictions_evaluees}")
    print(f"Prédictions correctes: {predictions_correctes}")
    print(f"Taux de réussite global: {taux_reussite_global:.1f}%")
    print(f"Avantage vs hasard: {taux_reussite_global - 50.0:+.1f}%")
    print(f"TIE rencontrés (exclus): {ties_rencontres}")
    
    print(f"\n📈 DÉTAIL PAR TYPE:")
    print(f"BANKER: {correctes_banker}/{predictions_banker} ({taux_banker:.1f}%)")
    print(f"PLAYER: {correctes_player}/{predictions_player} ({taux_player:.1f}%)")
    
    # Évaluation du succès
    print(f"\n🎯 ÉVALUATION DU SUCCÈS:")
    if taux_reussite_global > 52:
        print("✅ SUCCÈS: Taux de réussite > 52%")
    else:
        print("❌ ÉCHEC: Taux de réussite ≤ 52%")
        
    return {
        'predictions_evaluees': predictions_evaluees,
        'predictions_correctes': predictions_correctes,
        'taux_reussite_global': taux_reussite_global,
        'taux_banker': taux_banker,
        'taux_player': taux_player,
        'avantage_vs_hasard': taux_reussite_global - 50.0,
        'ties_rencontres': ties_rencontres
    }

if __name__ == "__main__":
    try:
        resultats = evaluer_performance_rapide()
        print(f"\n✅ Évaluation rapide terminée avec succès")
    except Exception as e:
        print(f"❌ Erreur lors de l'évaluation: {e}")
        import traceback
        traceback.print_exc()
