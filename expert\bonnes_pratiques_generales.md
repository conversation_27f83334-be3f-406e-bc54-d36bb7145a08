# Bonnes Pratiques Générales Python - Guide Complet 2024

## 📋 Conventions de Codage (PEP 8 et Plus)

### Standards de Codage Modernes 2024
- **PEP 8** : Guide de style officiel Python
- **Black** : Formateur de code automatique (88 caractères par ligne)
- **Ruff** : Linter ultra-rapide remplaçant Flake8, isort, etc.
- **Type Hints** : Annotations de type obligatoires (PEP 484, 585, 604)
- **Dataclasses** : Préférer aux classes traditionnelles pour les structures de données
- **Async/Await** : Programmation asynchrone moderne
- **Context Managers** : Gestion automatique des ressources

### Conventions de Nommage
```python
# Variables et fonctions : snake_case
user_name = "john_doe"
total_amount = 100.50

def calculate_total_price(base_price, tax_rate):
    """Calculer le prix total avec taxes."""
    return base_price * (1 + tax_rate)

# Classes : PascalCase
class UserManager:
    """Gestionnaire d'utilisateurs."""
    
    def __init__(self, database_url):
        self.database_url = database_url
    
    def create_user(self, user_data):
        """Créer un nouvel utilisateur."""
        pass

# Constantes : UPPER_CASE
MAX_RETRY_ATTEMPTS = 3
DEFAULT_TIMEOUT = 30
API_BASE_URL = "https://api.example.com"

# Modules et packages : lowercase
import user_manager
from data_processing import csv_parser

# Méthodes privées : _single_underscore
class DataProcessor:
    def __init__(self):
        self._internal_cache = {}
    
    def _validate_data(self, data):
        """Méthode privée de validation."""
        return data is not None
    
    def process(self, data):
        """Méthode publique."""
        if self._validate_data(data):
            return self._internal_process(data)

# Éviter les conflits avec mots-clés : trailing_underscore
class_ = "user"
type_ = "admin"

# Variables "magiques" : __double_underscore__
class MyClass:
    def __init__(self):
        self.__private_attr = "private"  # Name mangling
    
    def __str__(self):
        return "MyClass instance"
```

### Formatage et Structure du Code
```python
# Longueur de ligne : 88 caractères (Black) ou 79 (PEP 8)
def long_function_name(
    parameter_one: str,
    parameter_two: int,
    parameter_three: Optional[Dict[str, Any]] = None,
) -> Tuple[str, int]:
    """Fonction avec paramètres multiples."""
    if parameter_three is None:
        parameter_three = {}
    
    # Logique de la fonction
    result = f"{parameter_one}_{parameter_two}"
    return result, len(result)

# Imports : ordre et groupement
# 1. Bibliothèques standard
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Union

# 2. Bibliothèques tierces
import requests
import pandas as pd
from fastapi import FastAPI

# 3. Modules locaux
from .models import User
from .utils import logger

# Espacement autour des opérateurs
x = 1
y = 2
z = x + y

# Pas d'espaces autour des = pour les arguments par défaut
def function(param1, param2=None, param3="default"):
    pass

# Espacement dans les structures de données
my_list = [1, 2, 3, 4, 5]
my_dict = {"key1": "value1", "key2": "value2"}

# Compréhensions de liste lisibles
squared_numbers = [
    x**2 
    for x in range(10) 
    if x % 2 == 0
]

# Chaînage de méthodes
result = (
    data_frame
    .filter(lambda x: x > 0)
    .groupby("category")
    .sum()
    .reset_index()
)
```

### Commentaires et Documentation
```python
# Commentaires explicatifs pour la logique complexe
def complex_algorithm(data: List[int]) -> int:
    """
    Implémente l'algorithme de recherche binaire optimisé.
    
    Args:
        data: Liste triée d'entiers
        
    Returns:
        Index de l'élément trouvé ou -1 si non trouvé
        
    Raises:
        ValueError: Si la liste n'est pas triée
    """
    # Vérification préalable de la validité des données
    if not data or not all(data[i] <= data[i+1] for i in range(len(data)-1)):
        raise ValueError("La liste doit être triée")
    
    left, right = 0, len(data) - 1
    
    # Recherche dichotomique avec optimisation pour les petites listes
    while left <= right:
        # Utilisation de la moyenne pour éviter l'overflow
        mid = left + (right - left) // 2
        
        if data[mid] == target:
            return mid
        elif data[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    
    return -1

# TODO: Optimiser pour les très grandes listes (> 1M éléments)
# FIXME: Gérer le cas des doublons
# NOTE: Cette implémentation est thread-safe
```

## 🏗️ Structure de Projet Moderne

### Structure Recommandée
```
my_project/
├── pyproject.toml              # Configuration moderne du projet
├── README.md                   # Documentation principale
├── LICENSE                     # Licence du projet
├── .gitignore                 # Fichiers à ignorer par Git
├── .pre-commit-config.yaml    # Hooks de pre-commit
├── Makefile                   # Automatisation des tâches
├── Dockerfile                 # Conteneurisation
├── docker-compose.yml         # Services multi-conteneurs
├── .github/                   # CI/CD GitHub Actions
│   └── workflows/
│       ├── ci.yml
│       └── release.yml
├── docs/                      # Documentation Sphinx
│   ├── conf.py
│   ├── index.rst
│   └── api/
├── src/                       # Code source principal
│   └── my_project/
│       ├── __init__.py
│       ├── main.py
│       ├── config.py
│       ├── models/
│       │   ├── __init__.py
│       │   └── user.py
│       ├── services/
│       │   ├── __init__.py
│       │   └── user_service.py
│       ├── api/
│       │   ├── __init__.py
│       │   ├── routes/
│       │   └── middleware/
│       └── utils/
│           ├── __init__.py
│           ├── logger.py
│           └── helpers.py
├── tests/                     # Tests organisés
│   ├── __init__.py
│   ├── conftest.py           # Configuration pytest
│   ├── unit/
│   │   ├── test_models.py
│   │   └── test_services.py
│   ├── integration/
│   │   └── test_api.py
│   └── fixtures/
│       └── sample_data.json
├── scripts/                   # Scripts utilitaires
│   ├── setup.py
│   ├── migrate.py
│   └── seed_data.py
└── requirements/              # Dépendances par environnement
    ├── base.txt
    ├── dev.txt
    ├── test.txt
    └── prod.txt
```

### Configuration pyproject.toml Moderne
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "my-project"
version = "1.0.0"
description = "Description du projet"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["python", "api", "web"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "pydantic>=2.0.0",
    "sqlalchemy>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
]

[project.urls]
Homepage = "https://github.com/username/my-project"
Repository = "https://github.com/username/my-project.git"
Documentation = "https://my-project.readthedocs.io/"

[tool.setuptools.packages.find]
where = ["src"]

# Configuration des outils
[tool.black]
line-length = 88
target-version = ['py311']

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "W", "F", "I", "B", "C4", "UP"]
ignore = ["E501"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = [
    "--strict-markers",
    "--cov=src/my_project",
    "--cov-report=html",
    "--cov-report=term-missing",
]
```

## 🔧 Outils de Qualité de Code

### Configuration Ruff (Linter Moderne)
```toml
# pyproject.toml
[tool.ruff]
target-version = "py311"
line-length = 88

# Règles activées
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "N",    # pep8-naming
    "S",    # bandit (security)
    "T20",  # flake8-print
    "PT",   # flake8-pytest-style
    "RET",  # flake8-return
    "SIM",  # flake8-simplify
    "ARG",  # flake8-unused-arguments
]

# Règles ignorées
ignore = [
    "E501",  # line too long (géré par black)
    "S101",  # assert usage (OK dans les tests)
]

[tool.ruff.per-file-ignores]
"tests/**/*" = ["S101", "ARG001"]
"__init__.py" = ["F401"]

[tool.ruff.isort]
known-first-party = ["my_project"]
```

### Pre-commit Hooks Complets
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests]

  - repo: local
    hooks:
      - id: pytest-check
        name: pytest-check
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
        args: [tests/, --tb=short, -q]
```

## 📦 Gestion des Dépendances Moderne

### Avec Poetry (Recommandé)
```toml
# pyproject.toml avec Poetry
[tool.poetry]
name = "my-project"
version = "0.1.0"
description = "Description du projet"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.100.0"
uvicorn = {extras = ["standard"], version = "^0.23.0"}

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
ruff = "^0.1.0"
mypy = "^1.5.0"

[tool.poetry.group.test.dependencies]
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
```

### Avec uv (Alternative Moderne)
```bash
# Installation d'uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Créer un nouveau projet
uv init my-project
cd my-project

# Ajouter des dépendances
uv add fastapi uvicorn[standard]
uv add --dev pytest black ruff mypy

# Installer les dépendances
uv sync

# Exécuter des commandes
uv run python main.py
uv run pytest
```

### Bonnes Pratiques de Dépendances
```python
# requirements/base.txt - Dépendances de production
fastapi>=0.100.0,<0.101.0
uvicorn[standard]>=0.23.0,<0.24.0
pydantic>=2.0.0,<3.0.0
sqlalchemy>=2.0.0,<2.1.0

# requirements/dev.txt - Dépendances de développement
-r base.txt
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
ruff>=0.1.0
mypy>=1.5.0
pre-commit>=3.3.0

# requirements/test.txt - Dépendances de test
-r base.txt
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
factory-boy>=3.3.0
```

## 🎯 Principes de Code Propre

### SOLID en Python
```python
# Single Responsibility Principle
class UserValidator:
    """Responsabilité unique : valider les utilisateurs."""
    
    def validate_email(self, email: str) -> bool:
        return "@" in email and "." in email
    
    def validate_age(self, age: int) -> bool:
        return 0 <= age <= 150

class UserRepository:
    """Responsabilité unique : persistance des utilisateurs."""
    
    def save(self, user: User) -> None:
        # Logique de sauvegarde
        pass
    
    def find_by_id(self, user_id: int) -> Optional[User]:
        # Logique de recherche
        pass

# Open/Closed Principle
from abc import ABC, abstractmethod

class PaymentProcessor(ABC):
    """Interface fermée à la modification, ouverte à l'extension."""
    
    @abstractmethod
    def process_payment(self, amount: float) -> bool:
        pass

class CreditCardProcessor(PaymentProcessor):
    def process_payment(self, amount: float) -> bool:
        # Logique spécifique aux cartes de crédit
        return True

class PayPalProcessor(PaymentProcessor):
    def process_payment(self, amount: float) -> bool:
        # Logique spécifique à PayPal
        return True

# Liskov Substitution Principle
class Bird(ABC):
    @abstractmethod
    def move(self) -> str:
        pass

class FlyingBird(Bird):
    def move(self) -> str:
        return "flying"

class Penguin(Bird):
    def move(self) -> str:
        return "swimming"

# Interface Segregation Principle
class Readable(ABC):
    @abstractmethod
    def read(self) -> str:
        pass

class Writable(ABC):
    @abstractmethod
    def write(self, data: str) -> None:
        pass

class FileManager(Readable, Writable):
    def read(self) -> str:
        return "file content"
    
    def write(self, data: str) -> None:
        # Écrire dans le fichier
        pass

# Dependency Inversion Principle
class EmailService(ABC):
    @abstractmethod
    def send_email(self, to: str, subject: str, body: str) -> None:
        pass

class UserService:
    def __init__(self, email_service: EmailService):
        self._email_service = email_service
    
    def register_user(self, user: User) -> None:
        # Logique d'enregistrement
        self._email_service.send_email(
            user.email, 
            "Welcome", 
            "Welcome to our service!"
        )
```

Ces bonnes pratiques constituent la base d'un développement Python professionnel et maintenable.
