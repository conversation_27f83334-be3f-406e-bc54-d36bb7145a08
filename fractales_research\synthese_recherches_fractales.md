# SYNTHÈSE DES RECHERCHES SUR LES FRACTALES POUR L'ANALYSE PRÉDICTIVE

## OBJECTIF
Analyser la structure fractale de chaque partie du dataset `dataset_baccarat_lupasco_20250626_044753.json` pour développer une stratégie de prédiction de la main n+1.

## CONCEPTS CLÉS IDENTIFIÉS

### 1. ANALYSE FRACTALE DES SÉRIES TEMPORELLES
- **Dimension fractale** : Mesure de la complexité géométrique d'une série temporelle
- **Exposant de Hurst (H)** : Indicateur de persistance/anti-persistance
  - H > 0.5 : Persistance (tendance à continuer dans la même direction)
  - H < 0.5 : Anti-persistance (tendance à inverser la direction)
  - H = 0.5 : Mouvement brownien (aléatoire)

### 2. MÉTHODES D'ANALYSE FRACTALE
- **Analyse R/S (Rescaled Range)** : Calcul de l'exposant de Hurst
- **Analyse DFA (Detrended Fluctuation Analysis)** : Détection de corrélations à long terme
- **Dimension de corrélation** : Mesure de la complexité du système
- **Entropie de Rényi** : Quantification de l'information fractale

### 3. APPLICATIONS PRÉDICTIVES
- **Prédiction de séries temporelles** basée sur les propriétés fractales
- **Détection de patterns** auto-similaires à différentes échelles
- **Analyse multi-échelle** pour identifier les structures récurrentes

## DOCUMENTS SOURCES ANALYSÉS

### 1. "Fractal Analysis of Time Series and Distribution Properties of Hurst Exponent"
- **URL** : https://msme.us/2011-1-2.pdf
- **Concepts clés** :
  - Dimension fractale non-entière
  - Propriétés d'auto-similarité
  - Applications aux séries temporelles financières

### 2. Recherches sur l'analyse fractale des marchés financiers
- **Concepts** :
  - Efficience des marchés et caractéristiques fractales
  - Modélisation ARMA-GARCH avec analyse fractale
  - Prédiction basée sur les propriétés d'auto-similarité

### 3. Méthodes de prédiction par analyse fractale
- **Techniques** :
  - Interpolation fractale
  - Modèles chaotiques globaux
  - Analyse de la dimension de corrélation

## STRATÉGIE D'APPLICATION AU DATASET BACCARAT

### 1. ANALYSE FRACTALE PAR PARTIE
Chaque partie sera analysée selon :
- **Séquence des résultats** : BANKER, PLAYER, TIE
- **Séquence INDEX5** : Combinaisons des états de synchronisation
- **Patterns de cartes** : Catégories A, B, C

### 2. CALCUL DES MÉTRIQUES FRACTALES
Pour chaque partie :
- **Exposant de Hurst** de la séquence des résultats
- **Dimension fractale** des transitions INDEX1
- **Entropie** des patterns INDEX5
- **Corrélations à long terme** entre les mains

### 3. PRÉDICTION FRACTALE
Utilisation des propriétés fractales pour :
- **Identifier les patterns récurrents** à différentes échelles
- **Prédire la persistance/anti-persistance** des tendances
- **Détecter les points de retournement** basés sur l'analyse multi-échelle

## IMPLÉMENTATION TECHNIQUE

### 1. OUTILS PYTHON NÉCESSAIRES
```python
import numpy as np
import scipy.stats as stats
from scipy import signal
import pywt  # Pour l'analyse en ondelettes
import nolds  # Pour l'analyse fractale
```

### 2. MÉTRIQUES À CALCULER
- **Hurst Exponent** : `nolds.hurst_rs()`
- **Dimension de corrélation** : `nolds.corr_dim()`
- **Entropie de Shannon** : `scipy.stats.entropy()`
- **DFA (Detrended Fluctuation Analysis)** : `nolds.dfa()`

### 3. ANALYSE MULTI-ÉCHELLE
- Analyse des patterns sur différentes fenêtres temporelles
- Détection de l'auto-similarité à travers les échelles
- Identification des structures fractales récurrentes

## HYPOTHÈSES DE TRAVAIL

### 1. AUTO-SIMILARITÉ DES PARTIES
Les parties de baccarat peuvent présenter des structures auto-similaires à différentes échelles temporelles.

### 2. PERSISTANCE DES PATTERNS
Certains patterns INDEX5 peuvent montrer une persistance fractale permettant la prédiction.

### 3. TRANSITIONS CHAOTIQUES
Les transitions entre états peuvent suivre des attracteurs chaotiques identifiables.

## PROCHAINES ÉTAPES

1. **Implémentation du module d'analyse fractale**
2. **Calcul des métriques pour chaque partie du dataset**
3. **Identification des patterns fractaux récurrents**
4. **Développement de l'algorithme de prédiction basé sur les fractales**
5. **Validation et optimisation des performances**

## RÉFÉRENCES TECHNIQUES

- Mandelbrot, B. : "The Fractal Geometry of Nature"
- Peters, E. : "Chaos and Order in the Capital Markets"
- Analyse R/S pour les séries temporelles financières
- DFA pour la détection de corrélations à long terme
- Théorie du chaos appliquée à la prédiction
