# Microservices et APIs - Niveau Expert

## 🏗️ Architecture Microservices

### Service Discovery et Communication
```python
import asyncio
import aiohttp
import consul
from typing import Dict, List, Optional, Any
import json
import logging
from dataclasses import dataclass
from enum import Enum

class ServiceStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ServiceInstance:
    """Instance de service dans le registry"""
    id: str
    name: str
    host: str
    port: int
    health_check_url: str
    metadata: Dict[str, Any]
    status: ServiceStatus = ServiceStatus.UNKNOWN

class ServiceRegistry:
    """Registry de services avec Consul"""
    
    def __init__(self, consul_host: str = "localhost", consul_port: int = 8500):
        self.consul = consul.Consul(host=consul_host, port=consul_port)
        self.logger = logging.getLogger(__name__)
    
    async def register_service(self, service: ServiceInstance) -> bool:
        """Enregistrer un service dans Consul"""
        try:
            self.consul.agent.service.register(
                name=service.name,
                service_id=service.id,
                address=service.host,
                port=service.port,
                check=consul.Check.http(
                    url=f"http://{service.host}:{service.port}{service.health_check_url}",
                    interval="10s",
                    timeout="5s"
                ),
                meta=service.metadata
            )
            self.logger.info(f"Service {service.name} registered successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register service {service.name}: {e}")
            return False
    
    async def deregister_service(self, service_id: str) -> bool:
        """Désenregistrer un service"""
        try:
            self.consul.agent.service.deregister(service_id)
            self.logger.info(f"Service {service_id} deregistered successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to deregister service {service_id}: {e}")
            return False
    
    async def discover_services(self, service_name: str) -> List[ServiceInstance]:
        """Découvrir les instances d'un service"""
        try:
            _, services = self.consul.health.service(service_name, passing=True)
            
            instances = []
            for service_data in services:
                service_info = service_data['Service']
                health_info = service_data['Checks']
                
                # Déterminer le statut de santé
                status = ServiceStatus.HEALTHY
                for check in health_info:
                    if check['Status'] != 'passing':
                        status = ServiceStatus.UNHEALTHY
                        break
                
                instance = ServiceInstance(
                    id=service_info['ID'],
                    name=service_info['Service'],
                    host=service_info['Address'],
                    port=service_info['Port'],
                    health_check_url="/health",
                    metadata=service_info.get('Meta', {}),
                    status=status
                )
                instances.append(instance)
            
            return instances
        except Exception as e:
            self.logger.error(f"Failed to discover services for {service_name}: {e}")
            return []

class LoadBalancer:
    """Load balancer avec différentes stratégies"""
    
    def __init__(self, strategy: str = "round_robin"):
        self.strategy = strategy
        self.current_index = 0
        self.request_counts = {}
    
    def select_instance(self, instances: List[ServiceInstance]) -> Optional[ServiceInstance]:
        """Sélectionner une instance selon la stratégie"""
        healthy_instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]
        
        if not healthy_instances:
            return None
        
        if self.strategy == "round_robin":
            return self._round_robin(healthy_instances)
        elif self.strategy == "least_connections":
            return self._least_connections(healthy_instances)
        elif self.strategy == "random":
            import random
            return random.choice(healthy_instances)
        else:
            return healthy_instances[0]
    
    def _round_robin(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Stratégie round-robin"""
        instance = instances[self.current_index % len(instances)]
        self.current_index += 1
        return instance
    
    def _least_connections(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Stratégie least connections"""
        min_connections = float('inf')
        selected_instance = instances[0]
        
        for instance in instances:
            connections = self.request_counts.get(instance.id, 0)
            if connections < min_connections:
                min_connections = connections
                selected_instance = instance
        
        return selected_instance
    
    def increment_connections(self, instance_id: str):
        """Incrémenter le compteur de connexions"""
        self.request_counts[instance_id] = self.request_counts.get(instance_id, 0) + 1
    
    def decrement_connections(self, instance_id: str):
        """Décrémenter le compteur de connexions"""
        if instance_id in self.request_counts:
            self.request_counts[instance_id] = max(0, self.request_counts[instance_id] - 1)

class ServiceClient:
    """Client pour communication inter-services"""
    
    def __init__(self, service_registry: ServiceRegistry, load_balancer: LoadBalancer):
        self.registry = service_registry
        self.load_balancer = load_balancer
        self.session = None
        self.logger = logging.getLogger(__name__)
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def call_service(self, service_name: str, endpoint: str, 
                          method: str = "GET", data: Any = None, 
                          headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Appeler un service avec load balancing et retry"""
        
        instances = await self.registry.discover_services(service_name)
        if not instances:
            raise Exception(f"No instances found for service {service_name}")
        
        instance = self.load_balancer.select_instance(instances)
        if not instance:
            raise Exception(f"No healthy instances for service {service_name}")
        
        url = f"http://{instance.host}:{instance.port}{endpoint}"
        
        # Headers par défaut
        if headers is None:
            headers = {}
        headers.setdefault('Content-Type', 'application/json')
        headers.setdefault('User-Agent', 'ServiceClient/1.0')
        
        # Incrémenter le compteur de connexions
        self.load_balancer.increment_connections(instance.id)
        
        try:
            # Préparer les données
            json_data = json.dumps(data) if data and method in ['POST', 'PUT', 'PATCH'] else None
            
            async with self.session.request(
                method=method,
                url=url,
                data=json_data,
                headers=headers
            ) as response:
                
                if response.status >= 400:
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=error_text
                    )
                
                result = await response.json()
                self.logger.info(f"Successfully called {service_name}{endpoint}")
                return result
                
        except Exception as e:
            self.logger.error(f"Failed to call {service_name}{endpoint}: {e}")
            raise
        finally:
            # Décrémenter le compteur de connexions
            self.load_balancer.decrement_connections(instance.id)
```

### Circuit Breaker Pattern
```python
import asyncio
from datetime import datetime, timedelta
from enum import Enum
from typing import Callable, Any, Optional
import logging

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreakerError(Exception):
    """Exception levée quand le circuit breaker est ouvert"""
    pass

class CircuitBreaker:
    """Implémentation du pattern Circuit Breaker"""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 60,
                 expected_exception: type = Exception,
                 name: str = "CircuitBreaker"):
        
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.name = name
        
        # État du circuit
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = None
        self.success_count = 0
        
        self.logger = logging.getLogger(__name__)
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Exécuter une fonction avec protection circuit breaker"""
        
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                self.logger.info(f"Circuit breaker {self.name} moved to HALF_OPEN")
            else:
                raise CircuitBreakerError(f"Circuit breaker {self.name} is OPEN")
        
        try:
            # Exécuter la fonction
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Succès - réinitialiser les compteurs
            self._on_success()
            return result
            
        except self.expected_exception as e:
            # Échec attendu - incrémenter le compteur
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Vérifier si on doit tenter de réinitialiser le circuit"""
        if self.last_failure_time is None:
            return False
        
        return datetime.now() - self.last_failure_time >= timedelta(seconds=self.recovery_timeout)
    
    def _on_success(self):
        """Gérer un succès"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= 3:  # 3 succès consécutifs pour fermer
                self._reset()
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0
    
    def _on_failure(self):
        """Gérer un échec"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self.logger.warning(f"Circuit breaker {self.name} opened after {self.failure_count} failures")
        elif self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            self.logger.warning(f"Circuit breaker {self.name} reopened during half-open state")
    
    def _reset(self):
        """Réinitialiser le circuit breaker"""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.logger.info(f"Circuit breaker {self.name} reset to CLOSED")
    
    @property
    def is_closed(self) -> bool:
        return self.state == CircuitState.CLOSED
    
    @property
    def is_open(self) -> bool:
        return self.state == CircuitState.OPEN
    
    @property
    def is_half_open(self) -> bool:
        return self.state == CircuitState.HALF_OPEN

# Décorateur pour circuit breaker
def circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60, 
                   expected_exception: type = Exception, name: str = None):
    """Décorateur pour appliquer un circuit breaker à une fonction"""
    
    def decorator(func):
        breaker_name = name or f"{func.__module__}.{func.__name__}"
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception,
            name=breaker_name
        )
        
        async def async_wrapper(*args, **kwargs):
            return await breaker.call(func, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(breaker.call(func, *args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Exemple d'utilisation
@circuit_breaker(failure_threshold=3, recovery_timeout=30, name="external_api")
async def call_external_api(url: str) -> dict:
    """Appel API externe avec circuit breaker"""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status >= 400:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status
                )
            return await response.json()
```

## 🔌 API Gateway et Middleware

### API Gateway avec Rate Limiting
```python
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import redis
import time
import hashlib
import json
from typing import Dict, Any, Optional
import logging

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware de rate limiting avec Redis"""
    
    def __init__(self, app, redis_client: redis.Redis, 
                 default_rate_limit: int = 100, window_size: int = 60):
        super().__init__(app)
        self.redis = redis_client
        self.default_rate_limit = default_rate_limit
        self.window_size = window_size
        self.logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        # Identifier le client (IP ou API key)
        client_id = self._get_client_id(request)
        
        # Vérifier le rate limit
        if not await self._check_rate_limit(client_id, request.url.path):
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "retry_after": self.window_size
                },
                headers={"Retry-After": str(self.window_size)}
            )
        
        # Continuer le traitement
        response = await call_next(request)
        
        # Ajouter headers de rate limiting
        remaining = await self._get_remaining_requests(client_id, request.url.path)
        response.headers["X-RateLimit-Limit"] = str(self.default_rate_limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.window_size)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Identifier le client"""
        # Priorité à l'API key
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key}"
        
        # Sinon utiliser l'IP
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    async def _check_rate_limit(self, client_id: str, endpoint: str) -> bool:
        """Vérifier le rate limit avec sliding window"""
        key = f"rate_limit:{client_id}:{endpoint}"
        current_time = time.time()
        window_start = current_time - self.window_size
        
        # Utiliser une transaction Redis
        pipe = self.redis.pipeline()
        
        # Supprimer les entrées expirées
        pipe.zremrangebyscore(key, 0, window_start)
        
        # Compter les requêtes dans la fenêtre
        pipe.zcard(key)
        
        # Ajouter la requête actuelle
        pipe.zadd(key, {str(current_time): current_time})
        
        # Définir l'expiration
        pipe.expire(key, self.window_size)
        
        results = pipe.execute()
        request_count = results[1]
        
        return request_count < self.default_rate_limit
    
    async def _get_remaining_requests(self, client_id: str, endpoint: str) -> int:
        """Obtenir le nombre de requêtes restantes"""
        key = f"rate_limit:{client_id}:{endpoint}"
        current_count = self.redis.zcard(key)
        return max(0, self.default_rate_limit - current_count)

class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Middleware d'authentification JWT"""
    
    def __init__(self, app, jwt_secret: str, excluded_paths: list = None):
        super().__init__(app)
        self.jwt_secret = jwt_secret
        self.excluded_paths = excluded_paths or ["/health", "/docs", "/openapi.json"]
        self.logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        # Vérifier si le path est exclu
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Vérifier le token JWT
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=401,
                content={"error": "Missing or invalid authorization header"}
            )
        
        token = auth_header.split(" ")[1]
        
        try:
            # Vérifier et décoder le token
            import jwt
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            
            # Ajouter les informations utilisateur à la requête
            request.state.user = payload
            
        except jwt.ExpiredSignatureError:
            return JSONResponse(
                status_code=401,
                content={"error": "Token expired"}
            )
        except jwt.InvalidTokenError:
            return JSONResponse(
                status_code=401,
                content={"error": "Invalid token"}
            )
        
        return await call_next(request)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware de logging des requêtes"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("api_requests")
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Informations de la requête
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host,
            "user_agent": request.headers.get("User-Agent"),
            "timestamp": start_time
        }
        
        # Traiter la requête
        response = await call_next(request)
        
        # Calculer le temps de traitement
        process_time = time.time() - start_time
        
        # Informations de la réponse
        response_info = {
            **request_info,
            "status_code": response.status_code,
            "process_time": process_time
        }
        
        # Logger selon le niveau
        if response.status_code >= 500:
            self.logger.error(f"Server error: {json.dumps(response_info)}")
        elif response.status_code >= 400:
            self.logger.warning(f"Client error: {json.dumps(response_info)}")
        else:
            self.logger.info(f"Request processed: {json.dumps(response_info)}")
        
        # Ajouter header de temps de traitement
        response.headers["X-Process-Time"] = str(process_time)
        
        return response

# Configuration de l'API Gateway
def create_api_gateway() -> FastAPI:
    """Créer une instance d'API Gateway avec tous les middlewares"""
    
    app = FastAPI(
        title="API Gateway",
        description="Gateway pour microservices",
        version="1.0.0"
    )
    
    # Configuration Redis pour rate limiting
    redis_client = redis.Redis(host="localhost", port=6379, db=0)
    
    # Ajouter les middlewares (ordre important)
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(RateLimitMiddleware, redis_client=redis_client)
    app.add_middleware(AuthenticationMiddleware, jwt_secret="your-secret-key")
    
    return app

# Routes de l'API Gateway
app = create_api_gateway()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": time.time()}

@app.api_route("/{service_name}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_request(service_name: str, path: str, request: Request):
    """Proxy des requêtes vers les microservices"""
    
    # Initialiser les composants de service discovery
    registry = ServiceRegistry()
    load_balancer = LoadBalancer(strategy="round_robin")
    
    async with ServiceClient(registry, load_balancer) as client:
        # Préparer les données de la requête
        body = None
        if request.method in ["POST", "PUT", "PATCH"]:
            body = await request.json() if request.headers.get("content-type") == "application/json" else None
        
        # Headers à transmettre
        headers = dict(request.headers)
        headers.pop("host", None)  # Supprimer le header host
        
        try:
            # Appeler le service
            result = await client.call_service(
                service_name=service_name,
                endpoint=f"/{path}",
                method=request.method,
                data=body,
                headers=headers
            )
            
            return result
            
        except Exception as e:
            raise HTTPException(status_code=502, detail=f"Service {service_name} unavailable: {str(e)}")
```

Ces patterns et architectures microservices sont essentiels pour construire des systèmes distribués robustes et scalables au niveau expert.
