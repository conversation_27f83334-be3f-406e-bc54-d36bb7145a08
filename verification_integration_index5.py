#!/usr/bin/env python3
"""
VÉRIFICATION INTÉGRATION INDEX5 DANS ANALYSEUR FRACTAL
Vérifie si l'analyseur fractal utilise correctement les règles INDEX5
"""

import json
import pandas as pd
import numpy as np
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def verifier_valeurs_index5_dataset():
    """Vérifie toutes les valeurs INDEX5 présentes dans le dataset"""
    print("🔍 VÉRIFICATION VALEURS INDEX5 DANS LE DATASET")
    print("=" * 60)
    
    # Charger le dataset
    with open("dataset_baccarat_lupasco_20250626_044753.json", 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    # Collecter toutes les valeurs INDEX5
    valeurs_index5 = set()
    index1_values = set()
    index2_values = set()
    index3_values = set()
    
    total_mains = 0
    
    for partie in dataset['parties']:
        for main in partie['mains']:
            if main.get('main_number') is not None:  # Exclure la main 0
                total_mains += 1
                index5 = main.get('index5_combined', '')
                
                if index5:
                    valeurs_index5.add(index5)
                    
                    # Décomposer INDEX5
                    parties = index5.split('_')
                    if len(parties) >= 3:
                        index1_values.add(parties[0])
                        index2_values.add(parties[1])
                        index3_values.add(parties[2])
    
    print(f"📊 STATISTIQUES DATASET:")
    print(f"   Total mains analysées: {total_mains}")
    print(f"   Valeurs INDEX5 uniques: {len(valeurs_index5)}")
    
    print(f"\n📋 VALEURS INDEX1 TROUVÉES: {sorted(index1_values)}")
    print(f"📋 VALEURS INDEX2 TROUVÉES: {sorted(index2_values)}")
    print(f"📋 VALEURS INDEX3 TROUVÉES: {sorted(index3_values)}")
    
    print(f"\n🎯 TOUTES LES VALEURS INDEX5 TROUVÉES:")
    valeurs_triees = sorted(list(valeurs_index5))
    for i, valeur in enumerate(valeurs_triees):
        if i % 3 == 0:
            print()
        print(f"   {valeur:<12}", end="")
    print()
    
    # Vérifier si toutes les 18 combinaisons théoriques sont présentes
    combinaisons_theoriques = set()
    for index1 in ['0', '1']:
        for index2 in ['A', 'B', 'C']:
            for index3 in ['BANKER', 'PLAYER', 'TIE']:
                combinaisons_theoriques.add(f"{index1}_{index2}_{index3}")
    
    print(f"\n✅ VÉRIFICATION COMPLÉTUDE:")
    print(f"   Combinaisons théoriques: {len(combinaisons_theoriques)}")
    print(f"   Combinaisons trouvées: {len(valeurs_index5)}")
    
    manquantes = combinaisons_theoriques - valeurs_index5
    if manquantes:
        print(f"   ⚠️  Combinaisons manquantes: {sorted(manquantes)}")
    else:
        print(f"   ✅ Toutes les combinaisons INDEX5 sont présentes !")
    
    return valeurs_index5, index1_values, index2_values, index3_values

def verifier_regles_transition_analyseur():
    """Vérifie si l'analyseur fractal utilise les règles de transition INDEX1"""
    print(f"\n🔬 VÉRIFICATION RÈGLES TRANSITION DANS ANALYSEUR FRACTAL")
    print("=" * 60)
    
    analyseur = AnalyseurFractalBaccarat()
    
    print(f"📋 RÈGLES DÉFINIES DANS L'ANALYSEUR:")
    for cle, valeur in analyseur.regles_transition_index1.items():
        index1, index2 = cle
        print(f"   INDEX1={index1}, INDEX2={index2} → INDEX1_suivant={valeur}")
    
    print(f"\n🔍 VÉRIFICATION CONFORMITÉ AUX SPÉCIFICATIONS:")
    
    # Règles attendues selon les spécifications
    regles_attendues = {
        ('0', 'C'): '1',  # C: inversion
        ('1', 'C'): '0',  # C: inversion
        ('0', 'A'): '0',  # A: conservation
        ('1', 'A'): '1',  # A: conservation
        ('0', 'B'): '0',  # B: conservation
        ('1', 'B'): '1',  # B: conservation
    }
    
    conformite = True
    for cle, valeur_attendue in regles_attendues.items():
        valeur_analyseur = analyseur.regles_transition_index1.get(cle)
        if valeur_analyseur == valeur_attendue:
            print(f"   ✅ {cle} → {valeur_attendue} (correct)")
        else:
            print(f"   ❌ {cle} → attendu:{valeur_attendue}, trouvé:{valeur_analyseur}")
            conformite = False
    
    if conformite:
        print(f"\n✅ TOUTES LES RÈGLES SONT CONFORMES AUX SPÉCIFICATIONS")
    else:
        print(f"\n❌ CERTAINES RÈGLES NE SONT PAS CONFORMES")
    
    return conformite

def verifier_utilisation_regles_predictions():
    """Vérifie si les règles de transition sont utilisées dans les prédictions"""
    print(f"\n🎯 VÉRIFICATION UTILISATION RÈGLES DANS PRÉDICTIONS")
    print("=" * 60)
    
    # Analyser le code de la méthode predire_main_suivante
    analyseur = AnalyseurFractalBaccarat()
    
    # Vérifier si la méthode utilise regles_transition_index1
    import inspect
    source_code = inspect.getsource(analyseur.predire_main_suivante)
    
    utilise_regles = 'regles_transition_index1' in source_code
    utilise_index1 = 'index1' in source_code.lower()
    utilise_index2 = 'index2' in source_code.lower()
    utilise_index5 = 'index5' in source_code.lower()
    
    print(f"📋 ANALYSE DU CODE DE PRÉDICTION:")
    print(f"   Utilise regles_transition_index1: {'✅' if utilise_regles else '❌'}")
    print(f"   Mentionne INDEX1: {'✅' if utilise_index1 else '❌'}")
    print(f"   Mentionne INDEX2: {'✅' if utilise_index2 else '❌'}")
    print(f"   Mentionne INDEX5: {'✅' if utilise_index5 else '❌'}")
    
    if not utilise_regles:
        print(f"\n⚠️  PROBLÈME DÉTECTÉ:")
        print(f"   L'analyseur fractal définit les règles de transition INDEX1")
        print(f"   mais ne les utilise PAS dans ses prédictions !")
        print(f"   Les prédictions sont basées uniquement sur l'exposant de Hurst")
        print(f"   des séquences de résultats (BANKER/PLAYER/TIE)")
    
    return utilise_regles

def analyser_sequences_index5_vs_resultats():
    """Compare l'analyse des séquences INDEX5 vs résultats"""
    print(f"\n📊 COMPARAISON ANALYSE INDEX5 VS RÉSULTATS")
    print("=" * 60)
    
    # Charger les résultats de l'analyse fractale
    try:
        df = pd.read_csv("analyse_fractale_baccarat_20250629_100950.csv")
        
        print(f"📈 STATISTIQUES EXPOSANTS DE HURST:")
        print(f"   Hurst résultats - Moyenne: {df['hurst_resultats'].mean():.4f}")
        print(f"   Hurst INDEX5 - Moyenne: {df['hurst_index5'].mean():.4f}")
        print(f"   Hurst INDEX1 - Moyenne: {df['hurst_index1'].mean():.4f}")
        print(f"   Hurst INDEX2 - Moyenne: {df['hurst_index2'].mean():.4f}")
        
        # Corrélations
        corr_resultats_index5 = df['hurst_resultats'].corr(df['hurst_index5'])
        corr_resultats_index1 = df['hurst_resultats'].corr(df['hurst_index1'])
        corr_resultats_index2 = df['hurst_resultats'].corr(df['hurst_index2'])
        
        print(f"\n🔗 CORRÉLATIONS ENTRE EXPOSANTS:")
        print(f"   Résultats ↔ INDEX5: {corr_resultats_index5:.4f}")
        print(f"   Résultats ↔ INDEX1: {corr_resultats_index1:.4f}")
        print(f"   Résultats ↔ INDEX2: {corr_resultats_index2:.4f}")
        
        # Vérifier quelle séquence est utilisée pour les prédictions
        print(f"\n🎯 BASE DES PRÉDICTIONS:")
        print(f"   Les prédictions sont basées sur 'hurst_resultats'")
        print(f"   qui analyse la séquence des résultats BANKER/PLAYER/TIE")
        print(f"   et NON sur les séquences INDEX5 complètes")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ Fichier de résultats non trouvé")
        return False

def recommandations_amelioration():
    """Propose des améliorations pour intégrer correctement INDEX5"""
    print(f"\n💡 RECOMMANDATIONS D'AMÉLIORATION")
    print("=" * 60)
    
    print(f"🔧 PROBLÈMES IDENTIFIÉS:")
    print(f"   1. Les règles de transition INDEX1 sont définies mais non utilisées")
    print(f"   2. Les prédictions ignorent la structure déterministe INDEX1/INDEX2")
    print(f"   3. L'analyse fractale se base uniquement sur les résultats finaux")
    print(f"   4. La richesse informationnelle de l'INDEX5 n'est pas exploitée")
    
    print(f"\n🚀 AMÉLIORATIONS PROPOSÉES:")
    print(f"   1. INTÉGRER les règles de transition dans predire_main_suivante()")
    print(f"   2. PRÉDIRE l'INDEX1 suivant selon les règles déterministes")
    print(f"   3. COMBINER prédiction INDEX1 + analyse fractale INDEX2/INDEX3")
    print(f"   4. UTILISER l'entropie des séquences INDEX5 pour la confiance")
    print(f"   5. CRÉER un modèle hybride: déterministe (INDEX1) + fractal (INDEX2/3)")
    
    print(f"\n📝 EXEMPLE D'IMPLÉMENTATION:")
    print(f"   def predire_avec_index5(self, sequences, resultat):")
    print(f"       # 1. Prédire INDEX1 suivant (déterministe)")
    print(f"       derniere_main = sequences['index5_brut'][-1]")
    print(f"       index1_actuel, index2_actuel = derniere_main.split('_')[:2]")
    print(f"       index1_suivant = self.regles_transition_index1[(index1_actuel, index2_actuel)]")
    print(f"       ")
    print(f"       # 2. Analyser tendances INDEX2/INDEX3 avec Hurst")
    print(f"       # 3. Combiner pour prédiction finale INDEX5")
    print(f"       return prediction_hybride")

def main():
    """Fonction principale de vérification"""
    print("🔍 VÉRIFICATION INTÉGRATION INDEX5 - ANALYSEUR FRACTAL")
    print("=" * 70)
    
    # 1. Vérifier les valeurs INDEX5 dans le dataset
    valeurs_index5, index1_vals, index2_vals, index3_vals = verifier_valeurs_index5_dataset()
    
    # 2. Vérifier les règles de transition
    conformite_regles = verifier_regles_transition_analyseur()
    
    # 3. Vérifier l'utilisation dans les prédictions
    utilise_regles = verifier_utilisation_regles_predictions()
    
    # 4. Analyser les séquences
    analyse_ok = analyser_sequences_index5_vs_resultats()
    
    # 5. Recommandations
    recommandations_amelioration()
    
    # Résumé final
    print(f"\n📋 RÉSUMÉ DE LA VÉRIFICATION")
    print("=" * 70)
    print(f"✅ Dataset contient toutes les valeurs INDEX5: {len(valeurs_index5) == 18}")
    print(f"✅ Règles de transition conformes: {conformite_regles}")
    print(f"❌ Règles utilisées dans prédictions: {utilise_regles}")
    print(f"✅ Analyse des séquences INDEX5: {analyse_ok}")
    
    if not utilise_regles:
        print(f"\n⚠️  CONCLUSION PRINCIPALE:")
        print(f"   L'analyseur fractal N'UTILISE PAS les règles INDEX5")
        print(f"   Il analyse seulement les résultats finaux (BANKER/PLAYER/TIE)")
        print(f"   Une refactorisation est nécessaire pour exploiter INDEX5")

if __name__ == "__main__":
    main()
