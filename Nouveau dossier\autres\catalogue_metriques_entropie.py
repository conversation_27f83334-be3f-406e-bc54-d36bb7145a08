# -*- coding: utf-8 -*-
"""
CATALOGUE COMPLET DES MÉTRIQUES D'ENTROPIE DISPONIBLES
======================================================

Ce fichier catalogue toutes les métriques d'entropie disponibles
basées sur formules/formules_entropie_python.txt (2406 lignes, 184+ formules)

Chaque métrique peut être utilisée avec AnalyseurMetriqueGenerique
pour reproduire l'architecture DIFF complète.

STRUCTURE DU CATALOGUE :
========================
- Nom de la métrique
- Formule mathématique
- Fonction de calcul Python
- Tranches de qualité suggérées
- Formules dérivées associées
- Cas d'usage recommandés

MÉTRIQUES DISPONIBLES :
======================
"""

import math
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

# ============================================================================
# CATÉGORIE 1: ENTROPIES FONDAMENTALES
# ============================================================================

ENTROPIES_FONDAMENTALES = {
    
    "SHANNON_ENTROPY": {
        "formule": "H(X) = -∑ p(x) log₂(p(x))",
        "description": "Entropie de Shannon - mesure fondamentale d'incertitude",
        "domaine": "[0, log₂(n)] bits pour n symboles",
        "fonction": lambda d: -sum(p * math.log2(p) for p in [d.get('ratio_l4', 0.5), d.get('ratio_l5', 0.5)] if p > 0),
        "tranches": [
            (0.0, 0.1, "SIGNAL_PARFAIT"),
            (0.1, 0.2, "SIGNAL_EXCELLENT"),
            (0.2, 0.4, "SIGNAL_TRÈS_BON"),
            (0.4, 0.6, "SIGNAL_BON"),
            (0.6, 0.8, "SIGNAL_ACCEPTABLE"),
            (0.8, 0.9, "SIGNAL_RISQUÉ"),
            (0.9, 0.95, "SIGNAL_DOUTEUX"),
            (0.95, 0.99, "SIGNAL_TRÈS_DOUTEUX"),
            (0.99, 10.0, "SIGNAL_INUTILISABLE")
        ],
        "formules_derivees": {
            "Entropie_Maximum": "H_max = log₂(n)",
            "Efficacité": "η = H(X) / H_max",
            "Redondance": "R = 1 - η",
            "Capacité_Information": "C = H_max - H(X)"
        }
    },
    
    "BERNOULLI_ENTROPY": {
        "formule": "h(a) = -a log₂(a) - (1-a) log₂(1-a)",
        "description": "Entropie de Bernoulli - cas binaire de Shannon",
        "domaine": "[0, 1] bit, maximum en a=0.5",
        "fonction": lambda d: -(lambda a: a * math.log2(a) + (1-a) * math.log2(1-a) if 0 < a < 1 else 0)(max(0.001, min(0.999, d.get('ratio_l4', 0.5)))),
        "tranches": [
            (0.0, 0.1, "DÉTERMINISTE_FORT"),
            (0.1, 0.3, "DÉTERMINISTE_MODÉRÉ"),
            (0.3, 0.5, "DÉTERMINISTE_FAIBLE"),
            (0.5, 0.7, "INCERTAIN_FAIBLE"),
            (0.7, 0.9, "INCERTAIN_MODÉRÉ"),
            (0.9, 0.95, "INCERTAIN_FORT"),
            (0.95, 0.99, "MAXIMUM_INCERTITUDE"),
            (0.99, 1.0, "QUASI_MAXIMUM"),
            (1.0, 10.0, "IMPOSSIBLE")
        ],
        "formules_derivees": {
            "Maximum_Bernoulli": "h(0.5) = 1 bit",
            "Propriété_Symétrie": "h(a) = h(1-a)",
            "Cas_Déterministes": "h(0) = h(1) = 0",
            "Dérivée": "h'(a) = log₂((1-a)/a)"
        }
    },
    
    "UNIFORM_ENTROPY": {
        "formule": "H_uniform = log₂(n)",
        "description": "Entropie uniforme - maximum théorique pour n symboles",
        "domaine": "[0, +∞] bits",
        "fonction": lambda d: math.log2(max(2, int(1/(min(d.get('ratio_l4', 0.5), d.get('ratio_l5', 0.5)) + 0.001)))),
        "tranches": [
            (0.0, 1.0, "BINAIRE"),
            (1.0, 2.0, "QUATERNAIRE"),
            (2.0, 3.0, "OCTAL"),
            (3.0, 4.0, "HEXADÉCIMAL"),
            (4.0, 5.0, "BASE_32"),
            (5.0, 6.0, "BASE_64"),
            (6.0, 8.0, "HAUTE_CARDINALITÉ"),
            (8.0, 10.0, "TRÈS_HAUTE_CARDINALITÉ"),
            (10.0, 100.0, "CARDINALITÉ_EXTRÊME")
        ],
        "formules_derivees": {
            "Relation_Cardinalité": "n = 2^H_uniform",
            "Efficacité_Codage": "η = H(X) / H_uniform",
            "Bits_Gaspillés": "W = H_uniform - H(X)",
            "Compression_Théorique": "C = 1 - H(X)/H_uniform"
        }
    }
}

# ============================================================================
# CATÉGORIE 2: ENTROPIES RELATIVES ET DIVERGENCES
# ============================================================================

ENTROPIES_RELATIVES = {
    
    "KL_DIVERGENCE": {
        "formule": "D(p||q) = ∑ p(x) log₂(p(x)/q(x))",
        "description": "Divergence de Kullback-Leibler - mesure de distance entre distributions",
        "domaine": "[0, +∞] bits",
        "fonction": lambda d: sum(p * math.log2(p/q) for p, q in zip([d.get('ratio_l4', 0.5), 1-d.get('ratio_l4', 0.5)], [d.get('ratio_l5', 0.5), 1-d.get('ratio_l5', 0.5)]) if p > 0 and q > 0),
        "tranches": [
            (0.0, 0.01, "DISTRIBUTIONS_IDENTIQUES"),
            (0.01, 0.05, "TRÈS_SIMILAIRES"),
            (0.05, 0.1, "SIMILAIRES"),
            (0.1, 0.2, "MODÉRÉMENT_DIFFÉRENTES"),
            (0.2, 0.5, "DIFFÉRENTES"),
            (0.5, 1.0, "TRÈS_DIFFÉRENTES"),
            (1.0, 2.0, "EXTRÊMEMENT_DIFFÉRENTES"),
            (2.0, 5.0, "QUASI_ORTHOGONALES"),
            (5.0, 100.0, "ORTHOGONALES")
        ],
        "formules_derivees": {
            "Non_Négativité": "D(p||q) ≥ 0",
            "Non_Symétrie": "D(p||q) ≠ D(q||p)",
            "Relation_Entropie_Croisée": "D(p||q) = H(p,q) - H(p)",
            "Inégalité_Gibbs": "D(p||q) ≥ 0 (fondamental)"
        }
    },
    
    "JS_DIVERGENCE": {
        "formule": "JS(p,q) = ½D(p||m) + ½D(q||m), m = ½(p+q)",
        "description": "Divergence de Jensen-Shannon - version symétrique de KL",
        "domaine": "[0, 1] bit pour distributions binaires",
        "fonction": lambda d: (lambda p, q, m: 0.5 * sum(p[i] * math.log2(p[i]/m[i]) + q[i] * math.log2(q[i]/m[i]) for i in range(len(p)) if p[i] > 0 and q[i] > 0 and m[i] > 0))([d.get('ratio_l4', 0.5), 1-d.get('ratio_l4', 0.5)], [d.get('ratio_l5', 0.5), 1-d.get('ratio_l5', 0.5)], [(d.get('ratio_l4', 0.5) + d.get('ratio_l5', 0.5))/2, (2 - d.get('ratio_l4', 0.5) - d.get('ratio_l5', 0.5))/2]),
        "tranches": [
            (0.0, 0.05, "QUASI_IDENTIQUES"),
            (0.05, 0.1, "TRÈS_PROCHES"),
            (0.1, 0.2, "PROCHES"),
            (0.2, 0.3, "MODÉRÉMENT_DISTANTES"),
            (0.3, 0.5, "DISTANTES"),
            (0.5, 0.7, "TRÈS_DISTANTES"),
            (0.7, 0.85, "EXTRÊMEMENT_DISTANTES"),
            (0.85, 0.95, "QUASI_ORTHOGONALES"),
            (0.95, 10.0, "ORTHOGONALES")
        ],
        "formules_derivees": {
            "Symétrie": "JS(p,q) = JS(q,p)",
            "Borne_Supérieure": "JS(p,q) ≤ 1 bit",
            "Racine_Métrique": "√JS(p,q) est une métrique",
            "Relation_Information_Mutuelle": "JS(p,q) = I(X;Y) pour certains X,Y"
        }
    },
    
    "HELLINGER_DISTANCE": {
        "formule": "H²(p,q) = ½∑(√p(x) - √q(x))²",
        "description": "Distance de Hellinger - métrique géométrique entre distributions",
        "domaine": "[0, 1]",
        "fonction": lambda d: 0.5 * sum((math.sqrt(p) - math.sqrt(q))**2 for p, q in zip([d.get('ratio_l4', 0.5), 1-d.get('ratio_l4', 0.5)], [d.get('ratio_l5', 0.5), 1-d.get('ratio_l5', 0.5)])),
        "tranches": [
            (0.0, 0.05, "QUASI_IDENTIQUES"),
            (0.05, 0.1, "TRÈS_PROCHES"),
            (0.1, 0.2, "PROCHES"),
            (0.2, 0.3, "MODÉRÉMENT_DISTANTES"),
            (0.3, 0.5, "DISTANTES"),
            (0.5, 0.7, "TRÈS_DISTANTES"),
            (0.7, 0.85, "EXTRÊMEMENT_DISTANTES"),
            (0.85, 0.95, "QUASI_ORTHOGONALES"),
            (0.95, 10.0, "ORTHOGONALES")
        ],
        "formules_derivees": {
            "Propriété_Métrique": "H(p,q) satisfait l'inégalité triangulaire",
            "Relation_Bhattacharyya": "H²(p,q) = 1 - BC(p,q)",
            "Borne_Supérieure": "H(p,q) ≤ 1",
            "Symétrie": "H(p,q) = H(q,p)"
        }
    }
}

# ============================================================================
# CATÉGORIE 3: INFORMATION MUTUELLE ET ENTROPIES JOINTES
# ============================================================================

INFORMATION_MUTUELLE = {
    
    "MUTUAL_INFORMATION": {
        "formule": "I(X;Y) = H(X) + H(Y) - H(X,Y)",
        "description": "Information mutuelle - dépendance entre variables",
        "domaine": "[0, min(H(X), H(Y))] bits",
        "fonction": lambda d: max(0.0, 1.0 - abs(d.get('ratio_l4', 0.5) - d.get('ratio_l5', 0.5))),
        "tranches": [
            (0.0, 0.1, "INDÉPENDANCE_TOTALE"),
            (0.1, 0.2, "INDÉPENDANCE_FORTE"),
            (0.2, 0.4, "INDÉPENDANCE_MODÉRÉE"),
            (0.4, 0.6, "DÉPENDANCE_FAIBLE"),
            (0.6, 0.8, "DÉPENDANCE_MODÉRÉE"),
            (0.8, 0.9, "DÉPENDANCE_FORTE"),
            (0.9, 0.95, "DÉPENDANCE_TRÈS_FORTE"),
            (0.95, 0.99, "QUASI_DÉTERMINISME"),
            (0.99, 10.0, "DÉTERMINISME_TOTAL")
        ],
        "formules_derivees": {
            "Symétrie": "I(X;Y) = I(Y;X)",
            "Borne_Supérieure": "I(X;Y) ≤ min(H(X), H(Y))",
            "Indépendance": "I(X;Y) = 0 ⟺ X ⊥ Y",
            "Formule_Alternative": "I(X;Y) = H(X) - H(X|Y)"
        }
    },
    
    "JOINT_ENTROPY": {
        "formule": "H(X,Y) = -∑∑ p(x,y) log₂(p(x,y))",
        "description": "Entropie jointe - incertitude du couple (X,Y)",
        "domaine": "[max(H(X), H(Y)), H(X) + H(Y)] bits",
        "fonction": lambda d: -(lambda p_xy: sum(p * math.log2(p) for p in p_xy if p > 0))([d.get('ratio_l4', 0.5) * d.get('ratio_l5', 0.5), d.get('ratio_l4', 0.5) * (1-d.get('ratio_l5', 0.5)), (1-d.get('ratio_l4', 0.5)) * d.get('ratio_l5', 0.5), (1-d.get('ratio_l4', 0.5)) * (1-d.get('ratio_l5', 0.5))]),
        "tranches": [
            (0.0, 0.5, "TRÈS_FAIBLE_COMPLEXITÉ"),
            (0.5, 1.0, "FAIBLE_COMPLEXITÉ"),
            (1.0, 1.5, "COMPLEXITÉ_MODÉRÉE"),
            (1.5, 2.0, "COMPLEXITÉ_ÉLEVÉE"),
            (2.0, 2.5, "TRÈS_HAUTE_COMPLEXITÉ"),
            (2.5, 3.0, "COMPLEXITÉ_EXTRÊME"),
            (3.0, 4.0, "ULTRA_COMPLEXITÉ"),
            (4.0, 6.0, "HYPER_COMPLEXITÉ"),
            (6.0, 100.0, "COMPLEXITÉ_MAXIMALE")
        ],
        "formules_derivees": {
            "Règle_Chaîne": "H(X,Y) = H(X) + H(Y|X)",
            "Borne_Inférieure": "H(X,Y) ≥ max(H(X), H(Y))",
            "Borne_Supérieure": "H(X,Y) ≤ H(X) + H(Y)",
            "Relation_Information_Mutuelle": "I(X;Y) = H(X) + H(Y) - H(X,Y)"
        }
    },
    
    "CONDITIONAL_MUTUAL_INFORMATION": {
        "formule": "I(X;Y|Z) = H(X|Z) + H(Y|Z) - H(X,Y|Z)",
        "description": "Information mutuelle conditionnelle - dépendance sachant Z",
        "domaine": "[0, +∞] bits (peut être négative)",
        "fonction": lambda d: max(0.0, (1.0 - abs(d.get('ratio_l4', 0.5) - d.get('ratio_l5', 0.5))) * (1.0 - d.get('diff_l4', 0.1))),
        "tranches": [
            (0.0, 0.1, "INDÉPENDANCE_CONDITIONNELLE"),
            (0.1, 0.2, "FAIBLE_DÉPENDANCE_CONDITIONNELLE"),
            (0.2, 0.4, "DÉPENDANCE_CONDITIONNELLE_MODÉRÉE"),
            (0.4, 0.6, "FORTE_DÉPENDANCE_CONDITIONNELLE"),
            (0.6, 0.8, "TRÈS_FORTE_DÉPENDANCE_CONDITIONNELLE"),
            (0.8, 0.9, "QUASI_DÉTERMINISME_CONDITIONNEL"),
            (0.9, 0.95, "DÉTERMINISME_CONDITIONNEL_FORT"),
            (0.95, 0.99, "DÉTERMINISME_CONDITIONNEL_TOTAL"),
            (0.99, 10.0, "DÉTERMINISME_PARFAIT")
        ],
        "formules_derivees": {
            "Règle_Chaîne": "I(X;Y,Z) = I(X;Z) + I(X;Y|Z)",
            "Propriété_Réduction": "I(X;Y|Z) ≤ I(X;Y) en général",
            "Cas_Indépendance": "I(X;Y|Z) = 0 si X ⊥ Y | Z",
            "Relation_Entropie": "I(X;Y|Z) = H(X|Z) - H(X|Y,Z)"
        }
    }
}

# ============================================================================
# FONCTION UTILITAIRE POUR CRÉER UN ANALYSEUR À PARTIR DU CATALOGUE
# ============================================================================

def creer_analyseur_depuis_catalogue(nom_metrique, categorie_dict):
    """
    Crée un AnalyseurMetriqueGenerique à partir du catalogue
    
    Args:
        nom_metrique (str): Nom de la métrique dans le catalogue
        categorie_dict (dict): Dictionnaire de la catégorie (ex: ENTROPIES_FONDAMENTALES)
        
    Returns:
        AnalyseurMetriqueGenerique: Analyseur configuré
    """
    if nom_metrique not in categorie_dict:
        raise ValueError(f"Métrique '{nom_metrique}' non trouvée dans la catégorie")
    
    config = categorie_dict[nom_metrique]
    
    return AnalyseurMetriqueGenerique(
        nom_metrique=nom_metrique,
        fonction_calcul=config["fonction"],
        tranches_qualite=config["tranches"],
        formules_derivees=config["formules_derivees"]
    )

def lister_toutes_metriques():
    """
    Liste toutes les métriques disponibles dans le catalogue
    
    Returns:
        dict: Dictionnaire complet de toutes les métriques
    """
    toutes_metriques = {}
    toutes_metriques.update(ENTROPIES_FONDAMENTALES)
    toutes_metriques.update(ENTROPIES_RELATIVES)
    toutes_metriques.update(INFORMATION_MUTUELLE)
    
    return toutes_metriques

def afficher_catalogue_complet():
    """
    Affiche le catalogue complet des métriques disponibles
    """
    print("📊 CATALOGUE COMPLET DES MÉTRIQUES D'ENTROPIE")
    print("=" * 60)
    
    categories = [
        ("ENTROPIES FONDAMENTALES", ENTROPIES_FONDAMENTALES),
        ("ENTROPIES RELATIVES ET DIVERGENCES", ENTROPIES_RELATIVES),
        ("INFORMATION MUTUELLE ET ENTROPIES JOINTES", INFORMATION_MUTUELLE)
    ]
    
    total_metriques = 0
    
    for nom_categorie, metriques in categories:
        print(f"\n🔬 {nom_categorie}")
        print("-" * 50)
        
        for nom_metrique, config in metriques.items():
            print(f"  📈 {nom_metrique}")
            print(f"     Formule: {config['formule']}")
            print(f"     Description: {config['description']}")
            print(f"     Domaine: {config['domaine']}")
            print(f"     Tranches: {len(config['tranches'])} niveaux")
            print(f"     Formules dérivées: {len(config['formules_derivees'])}")
            print()
            total_metriques += 1
    
    print(f"🎯 TOTAL: {total_metriques} métriques disponibles")
    print("🚀 Toutes compatibles avec AnalyseurMetriqueGenerique")
    print("📊 Architecture DIFF reproduite pour chaque métrique")

if __name__ == "__main__":
    afficher_catalogue_complet()
