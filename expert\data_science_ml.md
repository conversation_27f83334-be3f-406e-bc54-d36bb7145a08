# Data Science et Machine Learning - Niveau Expert

## 📊 NumPy et Pandas Avancé

### Optimisation NumPy
```python
import numpy as np
import pandas as pd
from numba import jit, vectorize, cuda
import time
from typing import Tuple, List, Optional

class OptimizedNumPy:
    """Techniques d'optimisation NumPy avancées"""
    
    @staticmethod
    @jit(nopython=True)
    def fast_matrix_multiply(a: np.ndarray, b: np.ndarray) -> np.ndarray:
        """Multiplication matricielle optimisée avec Numba"""
        return np.dot(a, b)
    
    @staticmethod
    @vectorize(['float64(float64, float64)'], target='parallel')
    def parallel_operation(x, y):
        """Opération vectorisée parallèle"""
        return np.sqrt(x**2 + y**2)
    
    @staticmethod
    def memory_efficient_operations(data: np.ndarray) -> dict:
        """Opérations efficaces en mémoire"""
        # Utiliser des vues au lieu de copies
        view = data[::2]  # Vue sur les éléments pairs
        
        # Opérations in-place
        data += 1  # Modification in-place
        
        # Broadcasting efficace
        result = data[:, np.newaxis] * data[np.newaxis, :]
        
        return {
            'view_mean': np.mean(view),
            'modified_sum': np.sum(data),
            'broadcast_shape': result.shape
        }
    
    @staticmethod
    def advanced_indexing(data: np.ndarray) -> dict:
        """Techniques d'indexation avancées"""
        # Indexation booléenne
        positive_mask = data > 0
        positive_values = data[positive_mask]
        
        # Indexation fancy
        indices = np.array([0, 2, 4])
        selected_values = data[indices]
        
        # Indexation avec np.where
        conditional_values = np.where(data > 0, data, 0)
        
        return {
            'positive_count': len(positive_values),
            'selected_values': selected_values,
            'conditional_mean': np.mean(conditional_values)
        }

class AdvancedPandas:
    """Techniques Pandas avancées pour gros datasets"""
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        """Optimiser les types de données pour réduire la mémoire"""
        optimized_df = df.copy()
        
        # Optimiser les entiers
        for col in optimized_df.select_dtypes(include=['int64']).columns:
            col_min = optimized_df[col].min()
            col_max = optimized_df[col].max()
            
            if col_min >= 0:
                if col_max < 255:
                    optimized_df[col] = optimized_df[col].astype(np.uint8)
                elif col_max < 65535:
                    optimized_df[col] = optimized_df[col].astype(np.uint16)
                elif col_max < 4294967295:
                    optimized_df[col] = optimized_df[col].astype(np.uint32)
            else:
                if col_min > -128 and col_max < 127:
                    optimized_df[col] = optimized_df[col].astype(np.int8)
                elif col_min > -32768 and col_max < 32767:
                    optimized_df[col] = optimized_df[col].astype(np.int16)
                elif col_min > -2147483648 and col_max < 2147483647:
                    optimized_df[col] = optimized_df[col].astype(np.int32)
        
        # Optimiser les floats
        for col in optimized_df.select_dtypes(include=['float64']).columns:
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
        
        # Convertir en catégories si approprié
        for col in optimized_df.select_dtypes(include=['object']).columns:
            if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                optimized_df[col] = optimized_df[col].astype('category')
        
        return optimized_df
    
    @staticmethod
    def chunked_processing(file_path: str, chunk_size: int = 10000) -> pd.DataFrame:
        """Traitement par chunks pour gros fichiers"""
        results = []
        
        for chunk in pd.read_csv(file_path, chunksize=chunk_size):
            # Traitement du chunk
            processed_chunk = chunk.groupby('category').agg({
                'value': ['mean', 'sum', 'count'],
                'amount': 'sum'
            }).reset_index()
            
            results.append(processed_chunk)
        
        # Combiner tous les résultats
        final_result = pd.concat(results, ignore_index=True)
        return final_result.groupby('category').sum().reset_index()
    
    @staticmethod
    def advanced_groupby_operations(df: pd.DataFrame) -> pd.DataFrame:
        """Opérations groupby avancées"""
        # Groupby avec fonctions personnalisées
        def custom_agg(series):
            return {
                'q25': series.quantile(0.25),
                'q75': series.quantile(0.75),
                'iqr': series.quantile(0.75) - series.quantile(0.25),
                'outliers': len(series[np.abs(series - series.mean()) > 2 * series.std()])
            }
        
        # Groupby avec transform
        df['group_mean'] = df.groupby('category')['value'].transform('mean')
        df['value_normalized'] = df.groupby('category')['value'].transform(
            lambda x: (x - x.mean()) / x.std()
        )
        
        # Groupby avec apply
        result = df.groupby('category')['value'].apply(custom_agg).apply(pd.Series)
        
        return result
    
    @staticmethod
    def time_series_resampling(df: pd.DataFrame, date_col: str) -> pd.DataFrame:
        """Rééchantillonnage de séries temporelles avancé"""
        df[date_col] = pd.to_datetime(df[date_col])
        df.set_index(date_col, inplace=True)
        
        # Rééchantillonnage avec agrégations multiples
        resampled = df.resample('D').agg({
            'value': ['mean', 'sum', 'std'],
            'count': 'sum'
        })
        
        # Aplatir les colonnes multi-niveaux
        resampled.columns = ['_'.join(col).strip() for col in resampled.columns.values]
        
        # Interpolation des valeurs manquantes
        resampled = resampled.interpolate(method='time')
        
        return resampled
```

## 🤖 Machine Learning Avancé

### Pipeline ML avec Scikit-learn
```python
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.pipeline import Pipeline, FeatureUnion
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
import joblib
from typing import Any, Dict, List

class CustomFeatureExtractor(BaseEstimator, TransformerMixin):
    """Extracteur de features personnalisé"""
    
    def __init__(self, feature_names: List[str]):
        self.feature_names = feature_names
        self.statistics_ = {}
    
    def fit(self, X, y=None):
        """Calculer les statistiques sur les données d'entraînement"""
        for feature in self.feature_names:
            if feature in X.columns:
                self.statistics_[feature] = {
                    'mean': X[feature].mean(),
                    'std': X[feature].std(),
                    'median': X[feature].median(),
                    'q25': X[feature].quantile(0.25),
                    'q75': X[feature].quantile(0.75)
                }
        return self
    
    def transform(self, X):
        """Créer de nouvelles features"""
        X_transformed = X.copy()
        
        for feature in self.feature_names:
            if feature in X.columns and feature in self.statistics_:
                stats = self.statistics_[feature]
                
                # Features dérivées
                X_transformed[f'{feature}_zscore'] = (X[feature] - stats['mean']) / stats['std']
                X_transformed[f'{feature}_above_median'] = (X[feature] > stats['median']).astype(int)
                X_transformed[f'{feature}_iqr_position'] = (
                    (X[feature] - stats['q25']) / (stats['q75'] - stats['q25'])
                ).clip(0, 1)
        
        return X_transformed

class OutlierRemover(BaseEstimator, TransformerMixin):
    """Suppression d'outliers avec IQR"""
    
    def __init__(self, factor: float = 1.5):
        self.factor = factor
        self.bounds_ = {}
    
    def fit(self, X, y=None):
        """Calculer les bornes pour chaque feature numérique"""
        numeric_columns = X.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            Q1 = X[col].quantile(0.25)
            Q3 = X[col].quantile(0.75)
            IQR = Q3 - Q1
            
            self.bounds_[col] = {
                'lower': Q1 - self.factor * IQR,
                'upper': Q3 + self.factor * IQR
            }
        
        return self
    
    def transform(self, X):
        """Supprimer les outliers"""
        X_clean = X.copy()
        
        for col, bounds in self.bounds_.items():
            if col in X_clean.columns:
                mask = (
                    (X_clean[col] >= bounds['lower']) & 
                    (X_clean[col] <= bounds['upper'])
                )
                X_clean = X_clean[mask]
        
        return X_clean

class MLPipeline:
    """Pipeline ML complet avec validation croisée"""
    
    def __init__(self):
        self.pipeline = None
        self.best_params_ = None
        self.cv_scores_ = None
    
    def create_pipeline(self, feature_columns: List[str]) -> Pipeline:
        """Créer le pipeline de preprocessing et modélisation"""
        
        # Preprocessing pipeline
        preprocessing = Pipeline([
            ('feature_extractor', CustomFeatureExtractor(feature_columns)),
            ('outlier_remover', OutlierRemover(factor=1.5)),
            ('scaler', StandardScaler())
        ])
        
        # Ensemble de modèles
        ensemble = VotingClassifier([
            ('rf', RandomForestClassifier(n_estimators=100, random_state=42)),
            ('lr', LogisticRegression(random_state=42, max_iter=1000)),
            ('svm', SVC(probability=True, random_state=42))
        ])
        
        # Pipeline complet
        self.pipeline = Pipeline([
            ('preprocessing', preprocessing),
            ('classifier', ensemble)
        ])
        
        return self.pipeline
    
    def hyperparameter_tuning(self, X, y, param_grid: Dict[str, Any]) -> Dict[str, Any]:
        """Optimisation des hyperparamètres avec GridSearch"""
        
        grid_search = GridSearchCV(
            self.pipeline,
            param_grid,
            cv=5,
            scoring='accuracy',
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X, y)
        
        self.best_params_ = grid_search.best_params_
        self.pipeline = grid_search.best_estimator_
        
        return {
            'best_params': grid_search.best_params_,
            'best_score': grid_search.best_score_,
            'cv_results': grid_search.cv_results_
        }
    
    def cross_validate(self, X, y, cv: int = 5) -> Dict[str, float]:
        """Validation croisée complète"""
        
        scores = cross_val_score(self.pipeline, X, y, cv=cv, scoring='accuracy')
        
        self.cv_scores_ = {
            'mean': scores.mean(),
            'std': scores.std(),
            'scores': scores.tolist()
        }
        
        return self.cv_scores_
    
    def save_model(self, filepath: str) -> None:
        """Sauvegarder le modèle entraîné"""
        joblib.dump({
            'pipeline': self.pipeline,
            'best_params': self.best_params_,
            'cv_scores': self.cv_scores_
        }, filepath)
    
    @classmethod
    def load_model(cls, filepath: str) -> 'MLPipeline':
        """Charger un modèle sauvegardé"""
        data = joblib.load(filepath)
        
        instance = cls()
        instance.pipeline = data['pipeline']
        instance.best_params_ = data['best_params']
        instance.cv_scores_ = data['cv_scores']
        
        return instance

# Exemple d'utilisation
def train_model_example():
    """Exemple d'entraînement de modèle"""
    
    # Charger les données (exemple)
    # df = pd.read_csv('data.csv')
    # X = df.drop('target', axis=1)
    # y = df['target']
    
    # Créer le pipeline
    ml_pipeline = MLPipeline()
    feature_columns = ['feature1', 'feature2', 'feature3']
    pipeline = ml_pipeline.create_pipeline(feature_columns)
    
    # Grille de paramètres pour l'optimisation
    param_grid = {
        'classifier__rf__n_estimators': [50, 100, 200],
        'classifier__rf__max_depth': [None, 10, 20],
        'classifier__lr__C': [0.1, 1.0, 10.0],
        'preprocessing__outlier_remover__factor': [1.0, 1.5, 2.0]
    }
    
    # Optimisation des hyperparamètres
    # tuning_results = ml_pipeline.hyperparameter_tuning(X, y, param_grid)
    
    # Validation croisée
    # cv_results = ml_pipeline.cross_validate(X, y)
    
    # Sauvegarder le modèle
    # ml_pipeline.save_model('best_model.joblib')
    
    print("Modèle entraîné et sauvegardé avec succès!")
```

## 🧠 Deep Learning avec PyTorch

### Architecture de Réseau Personnalisée
```python
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
from typing import Tuple, Optional

class CustomDataset(Dataset):
    """Dataset personnalisé pour PyTorch"""
    
    def __init__(self, data: np.ndarray, targets: np.ndarray, transform=None):
        self.data = torch.FloatTensor(data)
        self.targets = torch.LongTensor(targets)
        self.transform = transform
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        target = self.targets[idx]
        
        if self.transform:
            sample = self.transform(sample)
        
        return sample, target

class AttentionLayer(nn.Module):
    """Couche d'attention personnalisée"""
    
    def __init__(self, input_dim: int, attention_dim: int):
        super(AttentionLayer, self).__init__()
        self.attention_dim = attention_dim
        
        self.W = nn.Linear(input_dim, attention_dim, bias=False)
        self.U = nn.Linear(attention_dim, 1, bias=False)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        
        # Calculer les scores d'attention
        scores = self.U(torch.tanh(self.W(x)))  # (batch_size, seq_len, 1)
        attention_weights = F.softmax(scores, dim=1)  # (batch_size, seq_len, 1)
        
        # Appliquer l'attention
        context = torch.sum(attention_weights * x, dim=1)  # (batch_size, input_dim)
        
        return context, attention_weights

class AdvancedNeuralNetwork(nn.Module):
    """Réseau de neurones avancé avec attention et dropout"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], num_classes: int, 
                 dropout_rate: float = 0.3, use_attention: bool = True):
        super(AdvancedNeuralNetwork, self).__init__()
        
        self.use_attention = use_attention
        
        # Couches cachées
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_dim = hidden_dim
        
        self.hidden_layers = nn.Sequential(*layers)
        
        # Couche d'attention optionnelle
        if self.use_attention:
            self.attention = AttentionLayer(prev_dim, prev_dim // 2)
        
        # Couche de sortie
        self.output_layer = nn.Linear(prev_dim, num_classes)
        
        # Initialisation des poids
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialisation des poids avec Xavier/He"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Passer par les couches cachées
        hidden_output = self.hidden_layers(x)
        
        # Appliquer l'attention si activée
        if self.use_attention:
            # Reshape pour l'attention (ajouter dimension séquence)
            hidden_output = hidden_output.unsqueeze(1)
            attended_output, attention_weights = self.attention(hidden_output)
            output = self.output_layer(attended_output)
            return output, attention_weights
        else:
            output = self.output_layer(hidden_output)
            return output

class ModelTrainer:
    """Classe pour l'entraînement de modèles PyTorch"""
    
    def __init__(self, model: nn.Module, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
    
    def train_epoch(self, train_loader: DataLoader, optimizer: optim.Optimizer, 
                   criterion: nn.Module) -> Tuple[float, float]:
        """Entraîner le modèle pour une époque"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, targets) in enumerate(train_loader):
            data, targets = data.to(self.device), targets.to(self.device)
            
            optimizer.zero_grad()
            
            # Forward pass
            if hasattr(self.model, 'use_attention') and self.model.use_attention:
                outputs, _ = self.model(data)
            else:
                outputs = self.model(data)
            
            loss = criterion(outputs, targets)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Statistiques
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def validate(self, val_loader: DataLoader, criterion: nn.Module) -> Tuple[float, float]:
        """Valider le modèle"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, targets in val_loader:
                data, targets = data.to(self.device), targets.to(self.device)
                
                if hasattr(self.model, 'use_attention') and self.model.use_attention:
                    outputs, _ = self.model(data)
                else:
                    outputs = self.model(data)
                
                loss = criterion(outputs, targets)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += targets.size(0)
                correct += predicted.eq(targets).sum().item()
        
        avg_loss = total_loss / len(val_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              num_epochs: int, learning_rate: float = 0.001) -> Dict[str, List[float]]:
        """Entraîner le modèle complet"""
        
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5)
        
        for epoch in range(num_epochs):
            # Entraînement
            train_loss, train_acc = self.train_epoch(train_loader, optimizer, criterion)
            
            # Validation
            val_loss, val_acc = self.validate(val_loader, criterion)
            
            # Scheduler
            scheduler.step(val_loss)
            
            # Sauvegarder les métriques
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_accuracies.append(train_acc)
            self.val_accuracies.append(val_acc)
            
            print(f'Epoch {epoch+1}/{num_epochs}:')
            print(f'  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
            print(f'  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'train_accuracies': self.train_accuracies,
            'val_accuracies': self.val_accuracies
        }
```

Ces techniques de Data Science et ML sont essentielles pour développer des solutions d'intelligence artificielle robustes au niveau expert.
