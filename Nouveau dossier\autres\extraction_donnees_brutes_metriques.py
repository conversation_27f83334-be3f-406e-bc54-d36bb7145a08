#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXTRACTION DONNÉES BRUTES POUR ANALYSE STATISTIQUE DES MÉTRIQUES ENTROPIQUES

Ce script extrait les données brutes pour DIFF, DIFF_RENYI et DIFF_TOPO
et calcule les statistiques (moyenne, écart-type, variance) pour chaque métrique.

Objectif : Analyser les distributions réelles et définir des conditions significatives
basées sur les écarts-types plutôt que sur des seuils arbitraires.
"""

import json
import numpy as np
import pandas as pd
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique

def extraire_donnees_brutes_toutes_metriques():
    """
    Extrait les données brutes pour DIFF, DIFF_RENYI et DIFF_TOPO
    avec les patterns S/O correspondants
    
    Returns:
        dict: Données structurées par métrique
    """
    print("🔬 EXTRACTION DONNÉES BRUTES - ANALYSE STATISTIQUE")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurMetriqueGenerique()
    
    # Charger le dataset
    dataset_path = 'dataset_baccarat_lupasco_20250626_044753.json'
    print(f"📂 Chargement dataset: {dataset_path}")
    
    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        print(f"✅ Dataset chargé: {len(dataset)} parties")
    except Exception as e:
        print(f"❌ Erreur chargement dataset: {e}")
        return None
    
    # Extraire données DIFF originales (fonction globale)
    print("📊 Extraction données DIFF...")
    from analyse_complete_avec_diff import analyser_conditions_predictives_so_avec_diff

    # Cette fonction retourne True/False, nous devons accéder aux données globales
    success = analyser_conditions_predictives_so_avec_diff()

    # Accéder aux données globales générées
    from analyse_complete_avec_diff import donnees_diff_globales
    donnees_diff = donnees_diff_globales if donnees_diff_globales else []
    print(f"✅ {len(donnees_diff)} points DIFF extraits")
    
    # Extraire données DIFF_RENYI
    print("📊 Extraction données DIFF_RENYI...")
    donnees_renyi = analyseur._extraire_donnees_avec_diff_renyi_depuis_donnees_globales_v2(donnees_diff)
    print(f"✅ {len(donnees_renyi)} points DIFF_RENYI extraits")
    
    # Extraire données DIFF_TOPO
    print("📊 Extraction données DIFF_TOPO...")
    donnees_topo = analyseur._extraire_donnees_avec_diff_topo_depuis_donnees_globales_v2(donnees_diff)
    print(f"✅ {len(donnees_topo)} points DIFF_TOPO extraits")
    
    return {
        'DIFF': donnees_diff,
        'DIFF_RENYI': donnees_renyi,
        'DIFF_TOPO': donnees_topo
    }

def calculer_statistiques_metriques(donnees_brutes):
    """
    Calcule les statistiques descriptives pour chaque métrique
    
    Args:
        donnees_brutes: Dictionnaire avec les données par métrique
        
    Returns:
        dict: Statistiques par métrique
    """
    print("\n📈 CALCUL STATISTIQUES DESCRIPTIVES")
    print("=" * 40)
    
    statistiques = {}
    
    for nom_metrique, donnees in donnees_brutes.items():
        print(f"\n🔍 Analyse {nom_metrique}:")
        
        # Extraire les valeurs de la métrique
        if nom_metrique == 'DIFF':
            valeurs = [d.get('diff', 0) for d in donnees if d.get('diff') is not None]
        elif nom_metrique == 'DIFF_RENYI':
            valeurs = [d.get('diff_renyi', 0) for d in donnees if d.get('diff_renyi') is not None]
        elif nom_metrique == 'DIFF_TOPO':
            valeurs = [d.get('diff_topo', 0) for d in donnees if d.get('diff_topo') is not None]
        
        if not valeurs:
            print(f"❌ Aucune valeur trouvée pour {nom_metrique}")
            continue
            
        # Calculer statistiques
        valeurs_array = np.array(valeurs)
        
        stats = {
            'count': len(valeurs),
            'moyenne': np.mean(valeurs_array),
            'mediane': np.median(valeurs_array),
            'ecart_type': np.std(valeurs_array),
            'variance': np.var(valeurs_array),
            'min': np.min(valeurs_array),
            'max': np.max(valeurs_array),
            'q25': np.percentile(valeurs_array, 25),
            'q75': np.percentile(valeurs_array, 75)
        }
        
        # Calculer seuils basés sur écart-type
        stats['seuil_1sigma_haut'] = stats['moyenne'] + stats['ecart_type']
        stats['seuil_1sigma_bas'] = stats['moyenne'] - stats['ecart_type']
        stats['seuil_2sigma_haut'] = stats['moyenne'] + 2 * stats['ecart_type']
        stats['seuil_2sigma_bas'] = stats['moyenne'] - 2 * stats['ecart_type']
        stats['seuil_3sigma_haut'] = stats['moyenne'] + 3 * stats['ecart_type']
        stats['seuil_3sigma_bas'] = stats['moyenne'] - 3 * stats['ecart_type']
        
        statistiques[nom_metrique] = stats
        
        # Afficher résultats
        print(f"   📊 Nombre de points: {stats['count']}")
        print(f"   📊 Moyenne: {stats['moyenne']:.6f}")
        print(f"   📊 Écart-type: {stats['ecart_type']:.6f}")
        print(f"   📊 Variance: {stats['variance']:.6f}")
        print(f"   📊 Min/Max: {stats['min']:.6f} / {stats['max']:.6f}")
        print(f"   📊 Seuils ±1σ: [{stats['seuil_1sigma_bas']:.6f}, {stats['seuil_1sigma_haut']:.6f}]")
        print(f"   📊 Seuils ±2σ: [{stats['seuil_2sigma_bas']:.6f}, {stats['seuil_2sigma_haut']:.6f}]")
    
    return statistiques

def analyser_patterns_par_seuils_sigma(donnees_brutes, statistiques):
    """
    Analyse les patterns S/O en fonction des seuils basés sur σ
    
    Args:
        donnees_brutes: Données brutes par métrique
        statistiques: Statistiques calculées
    """
    print("\n🎯 ANALYSE PATTERNS S/O PAR SEUILS SIGMA")
    print("=" * 50)
    
    for nom_metrique, donnees in donnees_brutes.items():
        if nom_metrique not in statistiques:
            continue
            
        print(f"\n🔍 {nom_metrique}:")
        stats = statistiques[nom_metrique]
        
        # Extraire valeurs et patterns
        if nom_metrique == 'DIFF':
            valeurs_patterns = [(d.get('diff', 0), d.get('pattern', '')) for d in donnees 
                              if d.get('diff') is not None and d.get('pattern') in ['S', 'O']]
        elif nom_metrique == 'DIFF_RENYI':
            valeurs_patterns = [(d.get('diff_renyi', 0), d.get('pattern_so', '')) for d in donnees 
                              if d.get('diff_renyi') is not None and d.get('pattern_so') in ['S', 'O']]
        elif nom_metrique == 'DIFF_TOPO':
            valeurs_patterns = [(d.get('diff_topo', 0), d.get('pattern_so', '')) for d in donnees 
                              if d.get('diff_topo') is not None and d.get('pattern_so') in ['S', 'O']]
        
        if not valeurs_patterns:
            print(f"   ❌ Aucune donnée valide pour {nom_metrique}")
            continue
        
        # Analyser par seuils sigma
        seuils = [
            ('1σ_haut', stats['seuil_1sigma_haut'], '>='),
            ('1σ_bas', stats['seuil_1sigma_bas'], '<'),
            ('2σ_haut', stats['seuil_2sigma_haut'], '>='),
            ('2σ_bas', stats['seuil_2sigma_bas'], '<')
        ]
        
        for nom_seuil, valeur_seuil, operateur in seuils:
            if operateur == '>=':
                donnees_seuil = [(v, p) for v, p in valeurs_patterns if v >= valeur_seuil]
            else:  # '<'
                donnees_seuil = [(v, p) for v, p in valeurs_patterns if v < valeur_seuil]
            
            if len(donnees_seuil) < 50:  # Seuil minimum pour analyse
                continue
                
            patterns_s = [p for v, p in donnees_seuil if p == 'S']
            patterns_o = [p for v, p in donnees_seuil if p == 'O']
            
            total = len(donnees_seuil)
            pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
            pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
            
            print(f"   🎯 {nom_seuil} ({operateur} {valeur_seuil:.6f}): {total} cas")
            print(f"      S: {len(patterns_s)} ({pct_s:.1f}%) | O: {len(patterns_o)} ({pct_o:.1f}%)")
            
            # Identifier conditions significatives (> 55% ou < 45%)
            if pct_s > 55:
                print(f"      ✅ CONDITION S SIGNIFICATIVE: {pct_s:.1f}%")
            elif pct_o > 55:
                print(f"      ✅ CONDITION O SIGNIFICATIVE: {pct_o:.1f}%")

def sauvegarder_donnees_brutes(donnees_brutes, statistiques):
    """
    Sauvegarde les données brutes et statistiques pour analyse ultérieure
    """
    print("\n💾 SAUVEGARDE DONNÉES BRUTES")
    print("=" * 30)
    
    # Sauvegarder données brutes
    with open('donnees_brutes_metriques.json', 'w', encoding='utf-8') as f:
        json.dump(donnees_brutes, f, indent=2, ensure_ascii=False)
    print("✅ donnees_brutes_metriques.json sauvegardé")
    
    # Sauvegarder statistiques
    with open('statistiques_metriques.json', 'w', encoding='utf-8') as f:
        json.dump(statistiques, f, indent=2, ensure_ascii=False)
    print("✅ statistiques_metriques.json sauvegardé")

def main():
    """
    Fonction principale d'extraction et d'analyse
    """
    print("🚀 DÉMARRAGE ANALYSE STATISTIQUE MÉTRIQUES ENTROPIQUES")
    print("=" * 70)
    
    # Étape 1: Extraction données brutes
    donnees_brutes = extraire_donnees_brutes_toutes_metriques()
    if not donnees_brutes:
        print("❌ Échec extraction données brutes")
        return
    
    # Étape 2: Calcul statistiques
    statistiques = calculer_statistiques_metriques(donnees_brutes)
    
    # Étape 3: Analyse patterns par seuils sigma
    analyser_patterns_par_seuils_sigma(donnees_brutes, statistiques)
    
    # Étape 4: Sauvegarde
    sauvegarder_donnees_brutes(donnees_brutes, statistiques)
    
    print("\n🎉 ANALYSE TERMINÉE")
    print("📊 Consultez les fichiers JSON pour les données détaillées")

if __name__ == "__main__":
    main()
