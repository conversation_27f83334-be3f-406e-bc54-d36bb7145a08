# Testing et Qualité - Niveau Expert

## 🧪 Testing Avancé avec pytest

### Fixtures Avancées et Paramétrage
```python
import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from typing import Generator, Any
import tempfile
import os

# Fixtures avec scope et paramétrage
@pytest.fixture(scope="session")
def database_url():
    """URL de base de données pour les tests"""
    return "sqlite:///:memory:"

@pytest.fixture(scope="function")
def db_session(database_url):
    """Session de base de données pour chaque test"""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Setup
    Base.metadata.create_all(engine)
    
    yield session
    
    # Teardown
    session.rollback()
    session.close()

@pytest.fixture(params=[
    {"name": "John", "age": 30},
    {"name": "<PERSON>", "age": 25},
    {"name": "<PERSON>", "age": 35}
])
def user_data(request):
    """Données utilisateur paramétrées"""
    return request.param

@pytest.fixture
def temp_file():
    """Fichier temporaire pour les tests"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        f.write("test content")
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)

# Fixture factory pattern
@pytest.fixture
def user_factory(db_session):
    """Factory pour créer des utilisateurs de test"""
    created_users = []
    
    def _create_user(name="Test User", email="<EMAIL>", **kwargs):
        user = User(name=name, email=email, **kwargs)
        db_session.add(user)
        db_session.commit()
        created_users.append(user)
        return user
    
    yield _create_user
    
    # Cleanup
    for user in created_users:
        db_session.delete(user)
    db_session.commit()

# Tests avec fixtures
def test_user_creation(user_factory, user_data):
    """Test de création d'utilisateur avec données paramétrées"""
    user = user_factory(**user_data)
    assert user.name == user_data["name"]
    assert user.age == user_data["age"]

@pytest.mark.asyncio
async def test_async_function():
    """Test de fonction asynchrone"""
    async def async_operation():
        await asyncio.sleep(0.1)
        return "success"
    
    result = await async_operation()
    assert result == "success"
```

### Mocking Avancé
```python
from unittest.mock import Mock, patch, MagicMock, PropertyMock
import pytest
from datetime import datetime, timedelta

class TestAdvancedMocking:
    """Tests avec mocking avancé"""
    
    def test_mock_with_side_effect(self):
        """Test avec side_effect pour simuler différents comportements"""
        mock_api = Mock()
        
        # Simuler différentes réponses selon les appels
        mock_api.get_user.side_effect = [
            {"id": 1, "name": "John"},
            {"id": 2, "name": "Jane"},
            Exception("API Error")
        ]
        
        # Premier appel
        result1 = mock_api.get_user(1)
        assert result1["name"] == "John"
        
        # Deuxième appel
        result2 = mock_api.get_user(2)
        assert result2["name"] == "Jane"
        
        # Troisième appel lève une exception
        with pytest.raises(Exception, match="API Error"):
            mock_api.get_user(3)
    
    @patch('requests.get')
    def test_external_api_call(self, mock_get):
        """Test d'appel API externe avec patch"""
        # Configuration du mock
        mock_response = Mock()
        mock_response.json.return_value = {"status": "success", "data": []}
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Code à tester
        from myapp.services import ApiService
        service = ApiService()
        result = service.fetch_data("https://api.example.com/data")
        
        # Vérifications
        assert result["status"] == "success"
        mock_get.assert_called_once_with("https://api.example.com/data")
    
    def test_mock_property(self):
        """Test avec mock de propriété"""
        mock_obj = Mock()
        
        # Mock d'une propriété
        type(mock_obj).expensive_property = PropertyMock(return_value="cached_value")
        
        # Test
        assert mock_obj.expensive_property == "cached_value"
        
        # Vérifier que la propriété a été accédée
        type(mock_obj).expensive_property.assert_called_once()
    
    @patch('myapp.utils.datetime')
    def test_time_dependent_function(self, mock_datetime):
        """Test de fonction dépendante du temps"""
        # Fixer le temps pour le test
        fixed_time = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.now.return_value = fixed_time
        
        from myapp.utils import get_business_hours_status
        
        # Test pendant les heures d'ouverture
        status = get_business_hours_status()
        assert status == "open"
        
        # Changer l'heure
        mock_datetime.now.return_value = datetime(2023, 1, 1, 22, 0, 0)
        status = get_business_hours_status()
        assert status == "closed"

# Context manager pour mocking
@pytest.fixture
def mock_database():
    """Context manager pour mocker la base de données"""
    with patch('myapp.database.get_connection') as mock_conn:
        mock_cursor = Mock()
        mock_conn.return_value.cursor.return_value = mock_cursor
        yield mock_cursor

def test_database_operation(mock_database):
    """Test d'opération base de données avec mock"""
    mock_database.fetchall.return_value = [
        (1, "John", "<EMAIL>"),
        (2, "Jane", "<EMAIL>")
    ]
    
    from myapp.repositories import UserRepository
    repo = UserRepository()
    users = repo.get_all_users()
    
    assert len(users) == 2
    assert users[0]["name"] == "John"
```

### Property-Based Testing
```python
from hypothesis import given, strategies as st, assume, example
import pytest

class TestPropertyBased:
    """Tests basés sur les propriétés avec Hypothesis"""
    
    @given(st.integers(min_value=0, max_value=1000))
    def test_square_root_property(self, n):
        """Test que sqrt(n)² ≈ n"""
        import math
        sqrt_n = math.sqrt(n)
        assert abs(sqrt_n * sqrt_n - n) < 1e-10
    
    @given(st.lists(st.integers(), min_size=1))
    def test_sort_properties(self, lst):
        """Test des propriétés du tri"""
        sorted_lst = sorted(lst)
        
        # La liste triée a la même longueur
        assert len(sorted_lst) == len(lst)
        
        # La liste triée contient les mêmes éléments
        assert sorted(lst) == sorted_lst
        
        # La liste est effectivement triée
        for i in range(len(sorted_lst) - 1):
            assert sorted_lst[i] <= sorted_lst[i + 1]
    
    @given(st.text(min_size=1), st.text(min_size=1))
    def test_string_concatenation(self, s1, s2):
        """Test des propriétés de la concaténation"""
        result = s1 + s2
        
        # La longueur est la somme des longueurs
        assert len(result) == len(s1) + len(s2)
        
        # Le résultat commence par s1 et finit par s2
        assert result.startswith(s1)
        assert result.endswith(s2)
    
    @given(st.emails())
    @example("<EMAIL>")  # Exemple spécifique à tester
    def test_email_validation(self, email):
        """Test de validation d'email avec exemples générés"""
        from myapp.validators import is_valid_email
        
        # Tous les emails générés par hypothesis doivent être valides
        assert is_valid_email(email)
        assert "@" in email
        assert "." in email.split("@")[1]
    
    @given(st.dictionaries(
        keys=st.text(min_size=1, max_size=10),
        values=st.integers(min_value=0, max_value=100),
        min_size=1,
        max_size=10
    ))
    def test_dictionary_operations(self, data):
        """Test d'opérations sur dictionnaire"""
        # Copie profonde préserve les données
        import copy
        data_copy = copy.deepcopy(data)
        assert data == data_copy
        
        # Les clés et valeurs correspondent
        for key, value in data.items():
            assert data[key] == value
            assert key in data.keys()
            assert value in data.values()

# Stratégies personnalisées
@st.composite
def user_strategy(draw):
    """Stratégie pour générer des utilisateurs valides"""
    name = draw(st.text(min_size=2, max_size=50))
    age = draw(st.integers(min_value=18, max_value=120))
    email = draw(st.emails())
    
    return {
        "name": name,
        "age": age,
        "email": email
    }

@given(user_strategy())
def test_user_creation_property(user_data):
    """Test de création d'utilisateur avec stratégie personnalisée"""
    from myapp.models import User
    
    user = User(**user_data)
    assert user.name == user_data["name"]
    assert user.age >= 18
    assert "@" in user.email
```

### Test Coverage et Qualité
```python
# Configuration pytest.ini
"""
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=myapp
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=90
    --durations=10
markers =
    slow: marks tests as slow
    integration: marks tests as integration tests
    unit: marks tests as unit tests
"""

# Markers personnalisés
import pytest

@pytest.mark.slow
def test_expensive_operation():
    """Test marqué comme lent"""
    import time
    time.sleep(2)  # Simulation d'opération coûteuse
    assert True

@pytest.mark.integration
def test_database_integration():
    """Test d'intégration avec base de données"""
    # Test nécessitant une vraie base de données
    pass

@pytest.mark.unit
def test_pure_function():
    """Test unitaire pur"""
    def add(a, b):
        return a + b
    
    assert add(2, 3) == 5

# Skip conditionnel
@pytest.mark.skipif(
    not pytest.importorskip("redis"),
    reason="Redis not available"
)
def test_redis_functionality():
    """Test nécessitant Redis"""
    import redis
    client = redis.Redis()
    # Test avec Redis

# Paramétrage avec ids personnalisés
@pytest.mark.parametrize("input,expected", [
    (2, 4),
    (3, 9),
    (4, 16),
    (-2, 4),
], ids=["positive_small", "positive_medium", "positive_large", "negative"])
def test_square_function(input, expected):
    """Test avec IDs personnalisés pour meilleure lisibilité"""
    def square(x):
        return x * x
    
    assert square(input) == expected

# Plugin personnalisé pour pytest
class TestReportPlugin:
    """Plugin personnalisé pour rapport de test"""
    
    def __init__(self):
        self.test_results = []
    
    def pytest_runtest_logreport(self, report):
        """Hook appelé pour chaque rapport de test"""
        if report.when == "call":
            self.test_results.append({
                'name': report.nodeid,
                'outcome': report.outcome,
                'duration': report.duration
            })
    
    def pytest_sessionfinish(self, session):
        """Hook appelé à la fin de la session de test"""
        total_tests = len(self.test_results)
        passed = len([r for r in self.test_results if r['outcome'] == 'passed'])
        failed = len([r for r in self.test_results if r['outcome'] == 'failed'])
        
        print(f"\n=== Test Summary ===")
        print(f"Total: {total_tests}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/total_tests)*100:.1f}%")

# Configuration dans conftest.py
def pytest_configure(config):
    """Configuration globale des tests"""
    config.pluginmanager.register(TestReportPlugin())

# Fixtures pour tests de performance
@pytest.fixture
def benchmark_timer():
    """Fixture pour mesurer les performances"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.perf_counter()
        
        def stop(self):
            self.end_time = time.perf_counter()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()

def test_performance_requirement(benchmark_timer):
    """Test avec exigence de performance"""
    benchmark_timer.start()
    
    # Code à tester
    result = sum(range(1000000))
    
    benchmark_timer.stop()
    
    # Vérifier le résultat ET la performance
    assert result == 499999500000
    assert benchmark_timer.elapsed < 0.1  # Moins de 100ms
```

Ces techniques de testing avancées sont essentielles pour maintenir une qualité de code élevée au niveau expert.
