# 🔴 NIVEAU EXPERT - Chapitre 2
## Entropie Topologique et Complexité Dynamique

### 🎯 Objectifs de ce chapitre
- Maîtriser la définition rigoureuse de l'entropie topologique
- Comprendre les différentes approches (recouvrements, ensembles séparés, spanning)
- Étudier le principe variationnel et ses applications
- Analyser les liens avec la dimension fractale et la complexité

---

## 📐 Définitions Rigoureuses

### Entropie Topologique par Recouvrements Ouverts

**Contexte** : Soit (X,d) un espace métrique compact et f : X → X continue.

**Définition** : Pour un recouvrement ouvert U de X,
```
h_top(f,U) = lim_{n→∞} (1/n) log N(⋁ᵢ₌₀ⁿ⁻¹ f⁻ⁱU)
```

où N(V) est le nombre minimal d'éléments d'un sous-recouvrement fini de V.

**Entropie topologique** :
```
h_top(f) = sup{h_top(f,U) : U recouvrement ouvert de X}
```

### Entropie Topologique par Ensembles (n,ε)-Séparés

**Définition** : Un ensemble E ⊂ X est (n,ε)-séparé si pour x,y ∈ E distincts,
```
max_{0≤i≤n-1} d(fⁱ(x), fⁱ(y)) ≥ ε
```

**Fonction de séparation** :
```
s_n(ε) = max{|E| : E est (n,ε)-séparé}
```

**Entropie topologique** :
```
h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε)
```

### Entropie Topologique par Ensembles (n,ε)-Spanning

**Définition** : Un ensemble E ⊂ X est (n,ε)-spanning si pour tout x ∈ X, il existe y ∈ E tel que
```
max_{0≤i≤n-1} d(fⁱ(x), fⁱ(y)) < ε
```

**Fonction spanning** :
```
r_n(ε) = min{|E| : E est (n,ε)-spanning}
```

**Entropie topologique** :
```
h_top(f) = lim_{ε→0} lim_{n→∞} (1/n) log r_n(ε)
```

### Équivalence des Définitions

**Théorème** : Les trois définitions ci-dessus sont équivalentes pour les espaces métriques compacts.

**Preuve esquisse** :
1. **Spanning ≤ Séparé** : r_n(ε) ≤ s_n(ε) par définition
2. **Séparé ≤ Recouvrement** : Via le lemme de Bowen
3. **Recouvrement ≤ Spanning** : Construction explicite de recouvrements

---

## 🧮 Calculs Fondamentaux

### Exemple 1 : Décalage Unilatéral

**Système** : σ : Σ_A^+ → Σ_A^+ où Σ_A^+ = {séquences infinies sur alphabet A}

**Matrice de transition** : A = (a_{ij}) avec a_{ij} = 1 si transition i→j autorisée

**Théorème** : h_top(σ) = log λ(A) où λ(A) est le rayon spectral de A

**Preuve** :
- Les mots de longueur n correspondent aux chemins dans le graphe
- Nombre de mots ≈ λ(A)ⁿ pour n grand
- h_top = lim_{n→∞} (1/n) log(nombre de mots) = log λ(A)

### Exemple 2 : Applications Dilatantes du Cercle

**Système** : f_d : S¹ → S¹, f_d(x) = dx (mod 1) avec d ≥ 2 entier

**Calcul direct** :
- Partition en d intervalles de longueur 1/d
- Après n itérations : dⁿ intervalles
- h_top(f_d) = log d

### Exemple 3 : Fer à Cheval de Smale

**Construction** :
1. Étirer le carré [0,1]² par facteur λ > 2 horizontalement
2. Contracter par facteur μ < 1/2 verticalement  
3. Plier et replacer dans le carré original

**Résultat** : h_top = log λ

**Généralisation** : Pour les difféomorphismes d'Anosov, h_top = ∑ λᵢ⁺ (exposants de Lyapunov positifs)

---

## 📊 Principe Variationnel

### Énoncé du Théorème Principal

**Théorème (Goodwyn, Dinaburg, Goodman)** :
```
h_top(f) = sup{h_μ(f) : μ mesure f-invariante}
```

### Démonstration Esquissée

**Partie 1** : h_μ(f) ≤ h_top(f)
- Utiliser la définition par partitions
- Montrer que l'entropie d'une partition est majorée par l'entropie topologique

**Partie 2** : sup h_μ(f) ≥ h_top(f)
- Construction de mesures approchant l'entropie topologique
- Utilisation du théorème de compacité faible-*

### Mesures d'Entropie Maximale

**Définition** : Une mesure μ est d'entropie maximale si h_μ(f) = h_top(f)

**Existence** : Toujours garantie par compacité

**Unicité** : Pas toujours (contre-exemple : décalages sofiques)

**Propriétés** :
- Souvent ergodiques
- Caractérisent la complexité maximale du système

---

## 🔬 Propriétés Avancées

### Théorème de Yomdin

**Énoncé** : Pour f : M → M difféomorphisme C^∞ d'une variété compacte,
```
h_top(f) ≤ ∫ log⁺ |Jac(Df)| dμ_Leb
```

où μ_Leb est la mesure de Lebesgue.

### Entropie et Degré Topologique

**Théorème** : Pour f : S^n → S^n continue,
```
h_top(f) ≥ log |deg(f)|
```

**Application** : Minoration de l'entropie par des invariants algébriques

### Entropie des Automorphismes de Tore

**Système** : f_A : T^n → T^n, f_A(x) = Ax (mod 1) où A ∈ GL_n(ℤ)

**Théorème** : h_top(f_A) = ∑ log⁺ |λᵢ| où λᵢ sont les valeurs propres de A

**Preuve** : Utilisation de la théorie spectrale et des sous-espaces stables

---

## 🎯 Applications Spécialisées

### 1. Systèmes Codés

#### Décalages Sofiques

**Définition** : Sous-décalage défini par un ensemble fini de mots interdits

**Calcul** : h_top = log λ où λ est le rayon spectral de la matrice de transition

**Exemple** : Décalage du carré doré (mots interdits : {11})
```
Matrice : [0 1]
          [1 1]
λ = (1+√5)/2 = φ (nombre d'or)
h_top = log φ
```

#### Décalages de Type Fini

**Définition** : Définis par des contraintes locales finies

**Propriété** : Entropie calculable algorithmiquement

**Applications** : Modèles de physique statistique, codes correcteurs

### 2. Billards Dynamiques

#### Billards Dispersifs

**Propriété** : h_top > 0 (chaos)

**Calcul** : Via les exposants de Lyapunov
```
h_top = ∫ λ⁺(x) dμ(x)
```

#### Billards Polygonaux

**Cas rationnel** : h_top = 0 (intégrable)
**Cas irrationnel** : h_top > 0 génériquement

### 3. Flots Géodésiques

#### Surfaces à Courbure Négative

**Théorème** : Pour une surface de courbure K ≤ -a² < 0,
```
h_top(φᵗ) = ∫ √(-K) dμ
```

**Cas hyperbolique** : h_top = (n-1)λ où λ > 0 est l'exposant de Lyapunov

---

## 🧪 Méthodes de Calcul

### Méthode Spectrale

**Principe** : Utiliser l'opérateur de transfert L_f

**Formule** : h_top(f) = log r(L_f) où r est le rayon spectral

**Application** : Systèmes expansifs par morceaux

### Méthode des Partitions Markoviennes

**Principe** : Construire une partition génératrice finie

**Avantage** : Ramène le calcul à l'algèbre linéaire

**Limitation** : Existence pas toujours garantie

### Approximation Numérique

**Algorithme de Grassberger-Procaccia** :
1. Choisir ε petit
2. Calculer s_n(ε) pour n croissant
3. Estimer lim_{n→∞} (1/n) log s_n(ε)
4. Extrapoler ε → 0

**Complexité** : Exponentielle en la dimension

---

## 🔍 Liens avec d'Autres Concepts

### Entropie et Dimension

**Dimension de Hausdorff** : dim_H(X) ≥ h_top(f)/λ_min

**Dimension de corrélation** : Liée à l'entropie de Rényi d'ordre 2

**Dimension d'information** : D₁ = h_top/λ_moy

### Entropie et Complexité

**Complexité par blocs** : p_n(x) = |{f^k x : 0 ≤ k < n}|

**Théorème** : h_top(f) = lim_{n→∞} (1/n) log p_n(x) pour x générique

### Entropie et Croissance

**Croissance exponentielle** : |{orbites périodiques de période ≤ n}| ≈ e^{h_top·n}

**Fonction zêta dynamique** : ζ_f(s) = exp(∑_{n=1}^∞ N_n s^n/n)

---

## 🎯 Exercices Avancés

### Exercice 1 : Calcul Direct

**Système** : f : [0,1] → [0,1], f(x) = 2x (mod 1)

**Tâches** :
1. Calculer h_top(f) par la méthode des ensembles séparés
2. Vérifier par la méthode spectrale
3. Construire une mesure d'entropie maximale

### Exercice 2 : Automorphisme de Tore

**Matrice** : A = [2 1; 1 1] (matrice de Fibonacci)

**Questions** :
1. Calculer les valeurs propres
2. Déterminer h_top(f_A)
3. Analyser la dynamique sur les sous-espaces propres

### Exercice 3 : Décalage Sofique

**Contrainte** : Interdire les mots {00, 11}

**Objectifs** :
1. Construire la matrice de transition
2. Calculer l'entropie topologique
3. Dénombrer les orbites périodiques

---

## 🔬 Développements Récents

### Entropie Topologique Relative

**Définition** : h_top(f,Y) pour Y ⊂ X sous-ensemble fermé invariant

**Applications** : Étude des attracteurs, bifurcations

### Entropie Topologique Conditionnelle

**Contexte** : Actions de groupes, systèmes multi-dimensionnels

**Formule** : h_top(T|S) pour deux transformations commutantes

### Entropie Topologique Locale

**Principe** : Mesurer la complexité en un point donné

**Définition** : h_top(f,x) = lim_{ε→0} lim_{n→∞} (1/n) log s_n(ε,x)

---

## 📚 Points Clés à Retenir

✅ **Définitions équivalentes** : Recouvrements, séparés, spanning  
✅ **Principe variationnel** : h_top = sup h_μ  
✅ **Calcul spectral** : h_top = log λ pour systèmes codés  
✅ **Liens géométriques** : Courbure, exposants de Lyapunov  
✅ **Applications** : Billards, flots géodésiques, automorphismes  

---

*Prochaine étape : [Chapitre 3 - Théorème d'Équipartition Asymptotique](03_equipartition_asymptotique.md)*
