# Validation de Données - Bonnes Pratiques 2024

## 🔍 Validation avec Pydantic v2

### Modèles de Base Avancés
```python
from pydantic import BaseModel, Field, validator, root_validator, EmailStr
from pydantic.types import constr, conint, confloat
from typing import Optional, List, Dict, Union, Literal
from datetime import datetime, date
from enum import Enum
import re

class UserRole(str, Enum):
    """Rôles utilisateur avec validation automatique."""
    ADMIN = "admin"
    USER = "user"
    MODERATOR = "moderator"
    GUEST = "guest"

class Address(BaseModel):
    """Modèle d'adresse avec validation complète."""
    street: constr(min_length=5, max_length=100)
    city: constr(min_length=2, max_length=50)
    postal_code: constr(regex=r'^\d{5}(-\d{4})?$')
    country: constr(min_length=2, max_length=2)  # Code ISO 2 lettres
    
    @validator('country')
    def validate_country_code(cls, v):
        """Valider le code pays ISO."""
        valid_countries = {'FR', 'US', 'GB', 'DE', 'ES', 'IT'}  # Exemple
        if v.upper() not in valid_countries:
            raise ValueError(f'Country code must be one of {valid_countries}')
        return v.upper()

class User(BaseModel):
    """Modèle utilisateur avec validation avancée."""
    
    # Champs obligatoires avec contraintes
    email: EmailStr
    username: constr(
        min_length=3, 
        max_length=20, 
        regex=r'^[a-zA-Z0-9_]+$'
    )
    password: constr(min_length=8, max_length=128)
    
    # Champs avec valeurs par défaut
    role: UserRole = UserRole.USER
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Champs optionnels
    first_name: Optional[constr(max_length=50)] = None
    last_name: Optional[constr(max_length=50)] = None
    age: Optional[conint(ge=13, le=120)] = None
    phone: Optional[constr(regex=r'^\+?1?\d{9,15}$')] = None
    address: Optional[Address] = None
    
    # Métadonnées
    tags: List[str] = Field(default_factory=list, max_items=10)
    preferences: Dict[str, Union[str, int, bool]] = Field(default_factory=dict)
    
    class Config:
        # Configuration Pydantic
        validate_assignment = True  # Valider lors des assignations
        use_enum_values = True      # Utiliser les valeurs d'enum
        extra = "forbid"           # Interdire les champs supplémentaires
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Valider la force du mot de passe."""
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v
    
    @validator('tags')
    def validate_tags(cls, v):
        """Valider les tags."""
        if len(v) != len(set(v)):
            raise ValueError('Tags must be unique')
        
        for tag in v:
            if not isinstance(tag, str) or len(tag) > 20:
                raise ValueError('Each tag must be a string with max 20 characters')
        
        return v
    
    @root_validator
    def validate_user_consistency(cls, values):
        """Validation croisée des champs."""
        age = values.get('age')
        role = values.get('role')
        
        # Les admins doivent être majeurs
        if role == UserRole.ADMIN and age and age < 18:
            raise ValueError('Admin users must be at least 18 years old')
        
        # Vérifier la cohérence nom/prénom
        first_name = values.get('first_name')
        last_name = values.get('last_name')
        
        if (first_name and not last_name) or (last_name and not first_name):
            raise ValueError('Both first_name and last_name must be provided together')
        
        return values
    
    @validator('email')
    def validate_email_domain(cls, v):
        """Valider le domaine de l'email."""
        blocked_domains = {'tempmail.com', '10minutemail.com', 'guerrillamail.com'}
        domain = v.split('@')[1].lower()
        
        if domain in blocked_domains:
            raise ValueError(f'Email domain {domain} is not allowed')
        
        return v.lower()

# Modèles pour les API
class UserCreateRequest(BaseModel):
    """Modèle pour la création d'utilisateur."""
    email: EmailStr
    username: constr(min_length=3, max_length=20, regex=r'^[a-zA-Z0-9_]+$')
    password: constr(min_length=8, max_length=128)
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    age: Optional[conint(ge=13, le=120)] = None
    
    # Validation du mot de passe identique à User
    @validator('password')
    def validate_password_strength(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

class UserUpdateRequest(BaseModel):
    """Modèle pour la mise à jour d'utilisateur."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    age: Optional[conint(ge=13, le=120)] = None
    phone: Optional[constr(regex=r'^\+?1?\d{9,15}$')] = None
    address: Optional[Address] = None
    tags: Optional[List[str]] = None
    preferences: Optional[Dict[str, Union[str, int, bool]]] = None
    
    class Config:
        extra = "forbid"

class UserResponse(BaseModel):
    """Modèle de réponse utilisateur (sans données sensibles)."""
    id: int
    email: EmailStr
    username: str
    role: UserRole
    is_active: bool
    created_at: datetime
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    age: Optional[int] = None
    
    class Config:
        from_attributes = True  # Pour la compatibilité ORM
```

### Validation Avancée avec Validators Personnalisés
```python
from pydantic import BaseModel, Field, validator, root_validator
from typing import Any, Dict, List, Optional
import json
import base64
from datetime import datetime, timedelta

class CustomValidators:
    """Collection de validateurs personnalisés réutilisables."""
    
    @staticmethod
    def validate_json_string(v: str) -> Dict[str, Any]:
        """Valider et parser une chaîne JSON."""
        try:
            return json.loads(v)
        except json.JSONDecodeError:
            raise ValueError('Invalid JSON string')
    
    @staticmethod
    def validate_base64(v: str) -> str:
        """Valider un string base64."""
        try:
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError('Invalid base64 string')
    
    @staticmethod
    def validate_future_date(v: datetime) -> datetime:
        """Valider que la date est dans le futur."""
        if v <= datetime.utcnow():
            raise ValueError('Date must be in the future')
        return v
    
    @staticmethod
    def validate_business_hours(v: datetime) -> datetime:
        """Valider que la date est en heures ouvrables."""
        if v.weekday() >= 5:  # Samedi = 5, Dimanche = 6
            raise ValueError('Date must be on a weekday')
        if not (9 <= v.hour <= 17):
            raise ValueError('Time must be between 9 AM and 5 PM')
        return v

class FileUpload(BaseModel):
    """Modèle pour l'upload de fichiers."""
    filename: constr(max_length=255)
    content_type: str
    size: conint(gt=0, le=10_000_000)  # Max 10MB
    content: str  # Base64 encoded
    
    @validator('filename')
    def validate_filename(cls, v):
        """Valider le nom de fichier."""
        # Caractères interdits
        forbidden_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        if any(char in v for char in forbidden_chars):
            raise ValueError(f'Filename contains forbidden characters: {forbidden_chars}')
        
        # Extensions autorisées
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'}
        if not any(v.lower().endswith(ext) for ext in allowed_extensions):
            raise ValueError(f'File extension must be one of: {allowed_extensions}')
        
        return v
    
    @validator('content_type')
    def validate_content_type(cls, v, values):
        """Valider le type MIME."""
        filename = values.get('filename', '')
        
        # Mapping extension -> content-type
        content_type_mapping = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        
        # Vérifier la cohérence extension/content-type
        for ext, expected_type in content_type_mapping.items():
            if filename.lower().endswith(ext) and v != expected_type:
                raise ValueError(f'Content-type {v} does not match extension {ext}')
        
        return v
    
    @validator('content')
    def validate_content(cls, v):
        """Valider le contenu base64."""
        return CustomValidators.validate_base64(v)

class Event(BaseModel):
    """Modèle d'événement avec validation complexe."""
    title: constr(min_length=1, max_length=200)
    description: Optional[constr(max_length=1000)] = None
    start_date: datetime
    end_date: datetime
    location: Optional[str] = None
    max_participants: Optional[conint(gt=0, le=1000)] = None
    tags: List[str] = Field(default_factory=list, max_items=5)
    metadata: Optional[str] = None  # JSON string
    
    @validator('start_date')
    def validate_start_date(cls, v):
        """La date de début doit être dans le futur."""
        return CustomValidators.validate_future_date(v)
    
    @validator('end_date')
    def validate_end_date(cls, v, values):
        """La date de fin doit être après la date de début."""
        start_date = values.get('start_date')
        if start_date and v <= start_date:
            raise ValueError('End date must be after start date')
        return v
    
    @validator('metadata', pre=True)
    def validate_metadata(cls, v):
        """Valider et parser les métadonnées JSON."""
        if v is None:
            return None
        if isinstance(v, str):
            return CustomValidators.validate_json_string(v)
        return v
    
    @root_validator
    def validate_event_duration(cls, values):
        """Valider la durée de l'événement."""
        start_date = values.get('start_date')
        end_date = values.get('end_date')
        
        if start_date and end_date:
            duration = end_date - start_date
            
            # Durée minimale : 30 minutes
            if duration < timedelta(minutes=30):
                raise ValueError('Event duration must be at least 30 minutes')
            
            # Durée maximale : 7 jours
            if duration > timedelta(days=7):
                raise ValueError('Event duration cannot exceed 7 days')
        
        return values

# Validation conditionnelle
class PaymentMethod(BaseModel):
    """Modèle de méthode de paiement avec validation conditionnelle."""
    type: Literal["credit_card", "paypal", "bank_transfer"]
    
    # Champs conditionnels
    card_number: Optional[constr(regex=r'^\d{16}$')] = None
    card_expiry: Optional[constr(regex=r'^\d{2}/\d{2}$')] = None
    card_cvv: Optional[constr(regex=r'^\d{3,4}$')] = None
    
    paypal_email: Optional[EmailStr] = None
    
    bank_account: Optional[constr(regex=r'^\d{10,20}$')] = None
    bank_routing: Optional[constr(regex=r'^\d{9}$')] = None
    
    @root_validator
    def validate_payment_fields(cls, values):
        """Validation conditionnelle selon le type de paiement."""
        payment_type = values.get('type')
        
        if payment_type == 'credit_card':
            required_fields = ['card_number', 'card_expiry', 'card_cvv']
            for field in required_fields:
                if not values.get(field):
                    raise ValueError(f'{field} is required for credit card payments')
        
        elif payment_type == 'paypal':
            if not values.get('paypal_email'):
                raise ValueError('paypal_email is required for PayPal payments')
        
        elif payment_type == 'bank_transfer':
            required_fields = ['bank_account', 'bank_routing']
            for field in required_fields:
                if not values.get(field):
                    raise ValueError(f'{field} is required for bank transfer payments')
        
        return values

# Validation avec dépendances externes
class ProductCode(BaseModel):
    """Modèle avec validation externe."""
    code: constr(regex=r'^[A-Z]{2}\d{6}$')
    category: str
    
    @validator('code')
    def validate_product_code(cls, v, values):
        """Valider le code produit avec une source externe."""
        # Simulation d'une validation externe
        # En réalité, cela pourrait être un appel à une API ou base de données
        
        valid_prefixes = {
            'electronics': ['EL', 'TE', 'CO'],
            'clothing': ['CL', 'SH', 'AC'],
            'books': ['BK', 'MA', 'ED']
        }
        
        category = values.get('category', '').lower()
        prefix = v[:2]
        
        if category in valid_prefixes and prefix not in valid_prefixes[category]:
            raise ValueError(
                f'Product code prefix {prefix} is not valid for category {category}'
            )
        
        return v

# Validation en lot
class BulkUserCreate(BaseModel):
    """Modèle pour création d'utilisateurs en lot."""
    users: List[UserCreateRequest] = Field(..., min_items=1, max_items=100)
    
    @validator('users')
    def validate_unique_emails(cls, v):
        """Vérifier l'unicité des emails dans le lot."""
        emails = [user.email for user in v]
        if len(emails) != len(set(emails)):
            raise ValueError('All email addresses must be unique')
        return v
    
    @validator('users')
    def validate_unique_usernames(cls, v):
        """Vérifier l'unicité des usernames dans le lot."""
        usernames = [user.username for user in v]
        if len(usernames) != len(set(usernames)):
            raise ValueError('All usernames must be unique')
        return v
```

### Gestion d'Erreurs de Validation
```python
from pydantic import ValidationError
from fastapi import HTTPException
from typing import List, Dict, Any

class ValidationErrorHandler:
    """Gestionnaire d'erreurs de validation."""
    
    @staticmethod
    def format_validation_error(error: ValidationError) -> Dict[str, Any]:
        """Formater une erreur de validation Pydantic."""
        formatted_errors = []
        
        for err in error.errors():
            formatted_error = {
                "field": ".".join(str(loc) for loc in err["loc"]),
                "message": err["msg"],
                "type": err["type"],
                "input": err.get("input")
            }
            formatted_errors.append(formatted_error)
        
        return {
            "error": "Validation failed",
            "details": formatted_errors,
            "error_count": len(formatted_errors)
        }
    
    @staticmethod
    def handle_validation_error(error: ValidationError) -> HTTPException:
        """Convertir une erreur de validation en HTTPException."""
        formatted_error = ValidationErrorHandler.format_validation_error(error)
        
        return HTTPException(
            status_code=422,
            detail=formatted_error
        )

# Décorateur pour la validation automatique
def validate_input(model_class: BaseModel):
    """Décorateur pour valider automatiquement les entrées."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Valider les kwargs avec le modèle Pydantic
                validated_data = model_class(**kwargs)
                # Remplacer kwargs par les données validées
                kwargs.update(validated_data.dict())
                return func(*args, **kwargs)
            except ValidationError as e:
                raise ValidationErrorHandler.handle_validation_error(e)
        return wrapper
    return decorator

# Exemple d'utilisation
@validate_input(UserCreateRequest)
def create_user_service(email: str, username: str, password: str, **kwargs):
    """Service de création d'utilisateur avec validation automatique."""
    # Les données sont déjà validées par le décorateur
    user = User(email=email, username=username, password=password, **kwargs)
    # Logique de création...
    return user
```

Ces pratiques de validation garantissent l'intégrité des données et une meilleure expérience utilisateur avec des messages d'erreur clairs et précis.
