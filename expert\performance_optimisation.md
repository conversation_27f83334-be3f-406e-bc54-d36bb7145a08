# Performance et Optimisation - Niveau Expert

## 🚀 Profiling et Benchmarking

### cProfile et py-spy
```python
import cProfile
import pstats
import io
from functools import wraps
import time

def profile_function(func):
    """Décorateur pour profiler une fonction"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        pr = cProfile.Profile()
        pr.enable()
        
        result = func(*args, **kwargs)
        
        pr.disable()
        s = io.StringIO()
        ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
        ps.print_stats()
        
        print(f"Profile for {func.__name__}:")
        print(s.getvalue())
        
        return result
    return wrapper

# Usage avec context manager
import contextlib

@contextlib.contextmanager
def profile_block(description="Code block"):
    """Context manager pour profiler un bloc de code"""
    pr = cProfile.Profile()
    pr.enable()
    
    start_time = time.time()
    try:
        yield
    finally:
        pr.disable()
        end_time = time.time()
        
        print(f"\n{description} - Execution time: {end_time - start_time:.4f}s")
        
        s = io.StringIO()
        ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
        ps.print_stats(10)  # Top 10 functions
        print(s.getvalue())

# Exemple d'usage
@profile_function
def expensive_computation(n):
    """Fonction coûteuse pour démonstration"""
    result = 0
    for i in range(n):
        result += i ** 2
    return result

# Avec context manager
with profile_block("Matrix multiplication"):
    import numpy as np
    a = np.random.rand(1000, 1000)
    b = np.random.rand(1000, 1000)
    c = np.dot(a, b)
```

### Memory Profiling
```python
import tracemalloc
import psutil
import os
from memory_profiler import profile
import gc

class MemoryProfiler:
    """Profiler mémoire avancé"""
    
    def __init__(self):
        self.snapshots = []
        self.process = psutil.Process(os.getpid())
    
    def start_tracing(self):
        """Démarrer le traçage mémoire"""
        tracemalloc.start()
        self.initial_memory = self.process.memory_info().rss
    
    def take_snapshot(self, description=""):
        """Prendre un snapshot mémoire"""
        if tracemalloc.is_tracing():
            snapshot = tracemalloc.take_snapshot()
            current_memory = self.process.memory_info().rss
            
            self.snapshots.append({
                'description': description,
                'snapshot': snapshot,
                'memory_rss': current_memory,
                'memory_diff': current_memory - self.initial_memory
            })
    
    def compare_snapshots(self, index1=0, index2=-1):
        """Comparer deux snapshots"""
        if len(self.snapshots) < 2:
            print("Need at least 2 snapshots to compare")
            return
        
        snap1 = self.snapshots[index1]['snapshot']
        snap2 = self.snapshots[index2]['snapshot']
        
        top_stats = snap2.compare_to(snap1, 'lineno')
        
        print(f"Top 10 memory differences:")
        for stat in top_stats[:10]:
            print(stat)
    
    def get_current_memory_usage(self):
        """Obtenir l'usage mémoire actuel"""
        return {
            'rss': self.process.memory_info().rss / 1024 / 1024,  # MB
            'vms': self.process.memory_info().vms / 1024 / 1024,  # MB
            'percent': self.process.memory_percent(),
            'available': psutil.virtual_memory().available / 1024 / 1024  # MB
        }

# Décorateur pour monitoring mémoire
def monitor_memory(func):
    """Décorateur pour monitorer l'usage mémoire"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        profiler = MemoryProfiler()
        profiler.start_tracing()
        
        initial_memory = profiler.get_current_memory_usage()
        print(f"Initial memory: {initial_memory['rss']:.2f} MB")
        
        result = func(*args, **kwargs)
        
        final_memory = profiler.get_current_memory_usage()
        memory_diff = final_memory['rss'] - initial_memory['rss']
        
        print(f"Final memory: {final_memory['rss']:.2f} MB")
        print(f"Memory difference: {memory_diff:.2f} MB")
        
        return result
    return wrapper

# Usage avec memory_profiler
@profile
def memory_intensive_function():
    """Fonction intensive en mémoire"""
    # Créer de gros objets
    big_list = [i for i in range(1000000)]
    big_dict = {i: str(i) * 100 for i in range(100000)}
    
    # Libérer explicitement
    del big_list
    del big_dict
    gc.collect()
```

## ⚡ Optimisation des Algorithmes

### Structures de Données Optimisées
```python
import bisect
from collections import deque, defaultdict, Counter
import heapq
from typing import List, Dict, Any

class OptimizedDataStructures:
    """Collection de structures de données optimisées"""
    
    @staticmethod
    def binary_search_insert(sorted_list: List[int], value: int) -> int:
        """Insertion optimisée dans une liste triée"""
        index = bisect.bisect_left(sorted_list, value)
        sorted_list.insert(index, value)
        return index
    
    @staticmethod
    def sliding_window_maximum(arr: List[int], k: int) -> List[int]:
        """Maximum dans une fenêtre glissante - O(n)"""
        if not arr or k == 0:
            return []
        
        dq = deque()  # Stocke les indices
        result = []
        
        for i in range(len(arr)):
            # Retirer les éléments hors de la fenêtre
            while dq and dq[0] <= i - k:
                dq.popleft()
            
            # Retirer les éléments plus petits que l'élément actuel
            while dq and arr[dq[-1]] <= arr[i]:
                dq.pop()
            
            dq.append(i)
            
            # Ajouter le maximum de la fenêtre actuelle
            if i >= k - 1:
                result.append(arr[dq[0]])
        
        return result
    
    @staticmethod
    def top_k_frequent(nums: List[int], k: int) -> List[int]:
        """Top K éléments les plus fréquents - O(n log k)"""
        counter = Counter(nums)
        
        # Utiliser un heap de taille k
        heap = []
        for num, freq in counter.items():
            heapq.heappush(heap, (freq, num))
            if len(heap) > k:
                heapq.heappop(heap)
        
        return [num for freq, num in heap]

class LRUCache:
    """Cache LRU optimisé"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = {}
        self.order = deque()
    
    def get(self, key: Any) -> Any:
        if key in self.cache:
            # Déplacer à la fin (plus récent)
            self.order.remove(key)
            self.order.append(key)
            return self.cache[key]
        return None
    
    def put(self, key: Any, value: Any) -> None:
        if key in self.cache:
            # Mettre à jour et déplacer à la fin
            self.cache[key] = value
            self.order.remove(key)
            self.order.append(key)
        else:
            # Nouveau élément
            if len(self.cache) >= self.capacity:
                # Supprimer le plus ancien
                oldest = self.order.popleft()
                del self.cache[oldest]
            
            self.cache[key] = value
            self.order.append(key)

class BloomFilter:
    """Filtre de Bloom pour test d'appartenance rapide"""
    
    def __init__(self, capacity: int, error_rate: float = 0.1):
        import math
        
        self.capacity = capacity
        self.error_rate = error_rate
        
        # Calculer la taille optimale du bit array
        self.bit_array_size = int(-capacity * math.log(error_rate) / (math.log(2) ** 2))
        
        # Calculer le nombre optimal de fonctions de hash
        self.hash_count = int(self.bit_array_size * math.log(2) / capacity)
        
        self.bit_array = [False] * self.bit_array_size
    
    def _hash(self, item: str, seed: int) -> int:
        """Fonction de hash avec seed"""
        hash_value = hash(item + str(seed))
        return hash_value % self.bit_array_size
    
    def add(self, item: str) -> None:
        """Ajouter un élément au filtre"""
        for i in range(self.hash_count):
            index = self._hash(item, i)
            self.bit_array[index] = True
    
    def might_contain(self, item: str) -> bool:
        """Vérifier si l'élément pourrait être dans le set"""
        for i in range(self.hash_count):
            index = self._hash(item, i)
            if not self.bit_array[index]:
                return False
        return True
```

## 🔄 Programmation Asynchrone et Concurrence

### AsyncIO Avancé
```python
import asyncio
import aiohttp
import aiofiles
from typing import List, Callable, Any
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

class AsyncBatchProcessor:
    """Processeur de batch asynchrone optimisé"""
    
    def __init__(self, max_concurrent: int = 10, batch_size: int = 100):
        self.max_concurrent = max_concurrent
        self.batch_size = batch_size
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_items(self, items: List[Any], processor: Callable) -> List[Any]:
        """Traiter une liste d'éléments en parallèle"""
        
        # Diviser en batches
        batches = [items[i:i + self.batch_size] 
                  for i in range(0, len(items), self.batch_size)]
        
        # Traiter les batches en parallèle
        tasks = [self._process_batch(batch, processor) for batch in batches]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aplatir les résultats
        results = []
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                print(f"Batch error: {batch_result}")
                continue
            results.extend(batch_result)
        
        return results
    
    async def _process_batch(self, batch: List[Any], processor: Callable) -> List[Any]:
        """Traiter un batch d'éléments"""
        async with self.semaphore:
            tasks = [self._process_item(item, processor) for item in batch]
            return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_item(self, item: Any, processor: Callable) -> Any:
        """Traiter un élément individuel"""
        if asyncio.iscoroutinefunction(processor):
            return await processor(item)
        else:
            # Exécuter dans un thread pool pour les fonctions synchrones
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(executor, processor, item)

# Exemple d'usage
async def fetch_url(session: aiohttp.ClientSession, url: str) -> dict:
    """Récupérer une URL de manière asynchrone"""
    try:
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
            return {
                'url': url,
                'status': response.status,
                'content_length': len(await response.text())
            }
    except Exception as e:
        return {'url': url, 'error': str(e)}

async def process_urls(urls: List[str]) -> List[dict]:
    """Traiter une liste d'URLs en parallèle"""
    processor = AsyncBatchProcessor(max_concurrent=20, batch_size=50)
    
    async with aiohttp.ClientSession() as session:
        async def url_processor(url):
            return await fetch_url(session, url)
        
        return await processor.process_items(urls, url_processor)

# Gestion avancée des tâches
class TaskManager:
    """Gestionnaire de tâches asynchrones avancé"""
    
    def __init__(self):
        self.running_tasks = set()
        self.completed_tasks = []
        self.failed_tasks = []
    
    async def run_with_timeout(self, coro, timeout: float):
        """Exécuter une coroutine avec timeout"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(f"Task timed out after {timeout} seconds")
    
    async def run_with_retry(self, coro_factory, max_retries: int = 3, delay: float = 1.0):
        """Exécuter avec retry automatique"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await coro_factory()
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                
        raise last_exception
    
    async def run_tasks_with_progress(self, tasks: List[Callable], 
                                    progress_callback: Callable = None):
        """Exécuter des tâches avec callback de progression"""
        total_tasks = len(tasks)
        completed = 0
        
        async def task_wrapper(task_func):
            nonlocal completed
            try:
                result = await task_func()
                self.completed_tasks.append(result)
                completed += 1
                
                if progress_callback:
                    await progress_callback(completed, total_tasks)
                
                return result
            except Exception as e:
                self.failed_tasks.append(e)
                completed += 1
                
                if progress_callback:
                    await progress_callback(completed, total_tasks)
                
                raise
        
        wrapped_tasks = [task_wrapper(task) for task in tasks]
        return await asyncio.gather(*wrapped_tasks, return_exceptions=True)

# Usage du TaskManager
async def example_usage():
    manager = TaskManager()
    
    async def progress_callback(completed, total):
        percentage = (completed / total) * 100
        print(f"Progress: {completed}/{total} ({percentage:.1f}%)")
    
    # Créer des tâches d'exemple
    async def sample_task(delay):
        await asyncio.sleep(delay)
        return f"Task completed after {delay}s"
    
    tasks = [lambda d=i: sample_task(d) for i in range(1, 6)]
    
    results = await manager.run_tasks_with_progress(tasks, progress_callback)
    print(f"Completed: {len(manager.completed_tasks)}")
    print(f"Failed: {len(manager.failed_tasks)}")
```

Ces techniques d'optimisation sont essentielles pour développer des applications Python performantes au niveau expert.
