#!/usr/bin/env python3
"""
DÉMONSTRATION PRÉDICTEUR FRACTAL BACCARAT
Version automatisée pour démonstration des capacités
"""

import json
import numpy as np
from predicteur_fractal_temps_reel import PredicteurFractalTempsReel
import random

def demo_prediction_progressive():
    """Démonstration de prédiction progressive sur une partie"""
    print("🎮 DÉMONSTRATION PRÉDICTEUR FRACTAL")
    print("=" * 60)
    
    # Initialiser le prédicteur
    predicteur = PredicteurFractalTempsReel()
    dataset = predicteur.analyseur_fractal.charger_dataset()
    
    # Sélectionner une partie avec beaucoup de mains
    parties_longues = [p for p in dataset['parties'] 
                      if len([m for m in p['mains'] if m.get('main_number') is not None]) >= 20]
    
    if not parties_longues:
        print("❌ Aucune partie longue trouvée")
        return
    
    partie_demo = random.choice(parties_longues)
    mains_partie = [main for main in partie_demo['mains'] if main.get('main_number') is not None]
    
    print(f"📊 Partie sélectionnée: {partie_demo['partie_number']}")
    print(f"📈 {len(mains_partie)} mains disponibles")
    
    # Statistiques de performance
    predictions_correctes = 0
    predictions_totales = 0
    predictions_neutres = 0
    
    # Analyser progressivement
    points_analyse = [4, 7, 10, 15, 20]
    
    for point in points_analyse:
        if point >= len(mains_partie):
            continue
            
        mains_jouees = mains_partie[:point]
        
        print(f"\n" + "="*70)
        print(f"🕐 ANALYSE APRÈS {point} MAINS JOUÉES")
        print("="*70)
        
        # Générer la prédiction
        prediction = predicteur.analyser_partie_en_cours(mains_jouees)
        
        # Afficher l'analyse
        print(f"📊 ANALYSE FRACTALE:")
        if not np.isnan(prediction.hurst_exponent):
            print(f"   🔬 Exposant de Hurst: {prediction.hurst_exponent:.4f}")
            print(f"   📈 Type: {prediction.type_persistance}")
            print(f"   📐 Dimension fractale: {prediction.dimension_fractale:.4f}")
            
            # Interprétation du Hurst
            if prediction.hurst_exponent > 0.65:
                print(f"   🔥 TRÈS PERSISTANT - Tendances fortes")
            elif prediction.hurst_exponent > 0.55:
                print(f"   📈 PERSISTANT - Tendances modérées")
            elif prediction.hurst_exponent < 0.45:
                print(f"   🔄 ANTI-PERSISTANT - Retour à la moyenne")
            else:
                print(f"   🎲 ALÉATOIRE - Peu prévisible")
        else:
            print(f"   ⚠️  Données insuffisantes")
        
        # Contexte
        print(f"\n📈 CONTEXTE:")
        print(f"   Mains analysées: {prediction.nb_mains_analysees}")
        print(f"   Tendance: {prediction.tendance_actuelle}")
        if prediction.derniers_resultats:
            derniers = ' → '.join(prediction.derniers_resultats[-5:])
            print(f"   Séquence récente: {derniers}")
        
        # Prédiction
        print(f"\n🎯 PRÉDICTION:")
        print(f"   Main suivante: {prediction.prediction_main_suivante}")
        print(f"   Probabilité: {prediction.probabilite:.3f}")
        print(f"   Confiance: {prediction.confiance:.3f}")
        print(f"   Recommandation: {prediction.recommandation_mise}")
        print(f"   Risque: {prediction.niveau_risque}")
        
        # Vérifier le résultat réel
        if point < len(mains_partie):
            main_suivante = mains_partie[point]
            resultat_reel = main_suivante.get('index3_result', 'INCONNU')
            
            print(f"\n✅ RÉSULTAT RÉEL: {resultat_reel}")
            
            predictions_totales += 1
            
            if prediction.prediction_main_suivante == resultat_reel:
                print("🎯 ✅ PRÉDICTION CORRECTE !")
                predictions_correctes += 1
            elif prediction.prediction_main_suivante in ['ALEATOIRE', 'INDETERMINE', 'ATTENDRE']:
                print("⚪ Prédiction neutre (pas de pari)")
                predictions_neutres += 1
            else:
                print("❌ Prédiction incorrecte")
                
            # Analyse de la performance
            if predictions_totales > 0:
                taux_reussite = predictions_correctes / predictions_totales * 100
                print(f"📊 Performance: {predictions_correctes}/{predictions_totales} ({taux_reussite:.1f}%)")
    
    # Résumé final
    print(f"\n" + "="*70)
    print(f"📊 RÉSUMÉ DE PERFORMANCE")
    print("="*70)
    print(f"Prédictions totales: {predictions_totales}")
    print(f"Prédictions correctes: {predictions_correctes}")
    print(f"Prédictions neutres: {predictions_neutres}")
    
    if predictions_totales > 0:
        taux_global = predictions_correctes / predictions_totales * 100
        print(f"Taux de réussite global: {taux_global:.1f}%")
        
        # Comparaison avec le hasard
        print(f"\nComparaison avec le hasard:")
        print(f"- Hasard pur (BANKER/PLAYER): ~50%")
        print(f"- Hasard avec TIE (~9%): ~45.5%")
        print(f"- Notre prédicteur: {taux_global:.1f}%")
        
        if taux_global > 50:
            print(f"🎉 Performance supérieure au hasard !")
        elif taux_global > 45:
            print(f"📈 Performance acceptable")
        else:
            print(f"⚠️  Performance à améliorer")

def demo_analyse_multiple_parties():
    """Démonstration sur plusieurs parties"""
    print(f"\n🔬 ANALYSE MULTIPLE PARTIES")
    print("=" * 50)
    
    predicteur = PredicteurFractalTempsReel()
    dataset = predicteur.analyseur_fractal.charger_dataset()
    
    # Sélectionner 5 parties aléatoires
    parties_demo = random.sample(dataset['parties'], 5)
    
    resultats_globaux = {
        'total_predictions': 0,
        'predictions_correctes': 0,
        'predictions_neutres': 0,
        'hurst_moyens': [],
        'confiances_moyennes': []
    }
    
    for i, partie in enumerate(parties_demo, 1):
        mains_partie = [main for main in partie['mains'] if main.get('main_number') is not None]
        
        if len(mains_partie) < 10:
            continue
            
        print(f"\n📊 PARTIE {i}: {partie['partie_number']} ({len(mains_partie)} mains)")
        
        # Analyser à mi-parcours
        point_analyse = len(mains_partie) // 2
        mains_jouees = mains_partie[:point_analyse]
        
        prediction = predicteur.analyser_partie_en_cours(mains_jouees)
        
        print(f"   Hurst: {prediction.hurst_exponent:.4f} ({prediction.type_persistance})")
        print(f"   Prédiction: {prediction.prediction_main_suivante} (conf: {prediction.confiance:.3f})")
        
        # Vérifier le résultat
        if point_analyse < len(mains_partie):
            resultat_reel = mains_partie[point_analyse].get('index3_result', 'INCONNU')
            print(f"   Réel: {resultat_reel}", end="")
            
            resultats_globaux['total_predictions'] += 1
            
            if prediction.prediction_main_suivante == resultat_reel:
                print(" ✅")
                resultats_globaux['predictions_correctes'] += 1
            elif prediction.prediction_main_suivante in ['ALEATOIRE', 'INDETERMINE']:
                print(" ⚪")
                resultats_globaux['predictions_neutres'] += 1
            else:
                print(" ❌")
        
        if not np.isnan(prediction.hurst_exponent):
            resultats_globaux['hurst_moyens'].append(prediction.hurst_exponent)
            resultats_globaux['confiances_moyennes'].append(prediction.confiance)
    
    # Résumé global
    print(f"\n📈 RÉSUMÉ GLOBAL:")
    if resultats_globaux['total_predictions'] > 0:
        taux = resultats_globaux['predictions_correctes'] / resultats_globaux['total_predictions'] * 100
        print(f"Taux de réussite: {taux:.1f}%")
    
    if resultats_globaux['hurst_moyens']:
        hurst_moy = np.mean(resultats_globaux['hurst_moyens'])
        conf_moy = np.mean(resultats_globaux['confiances_moyennes'])
        print(f"Hurst moyen: {hurst_moy:.4f}")
        print(f"Confiance moyenne: {conf_moy:.3f}")

def main():
    """Fonction principale de démonstration"""
    print("🔮 DÉMONSTRATION COMPLÈTE - PRÉDICTEUR FRACTAL BACCARAT")
    print("=" * 70)
    
    try:
        # Démonstration 1: Analyse progressive
        demo_prediction_progressive()
        
        # Démonstration 2: Analyse multiple
        demo_analyse_multiple_parties()
        
        print(f"\n🎉 DÉMONSTRATION TERMINÉE")
        print("=" * 70)
        print("💡 Le prédicteur fractal utilise l'exposant de Hurst pour:")
        print("   - Détecter la persistance/anti-persistance des séquences")
        print("   - Prédire la continuation ou l'inversion des tendances")
        print("   - Évaluer la confiance des prédictions")
        print("   - Recommander des stratégies de mise adaptées")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
