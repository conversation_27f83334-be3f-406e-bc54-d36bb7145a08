#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 DIAGNOSTIC SPÉCIFIQUE DU COMPTAGE TIE
========================================
Identifie pourquoi le comptage TIE est erroné
"""

import json
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def diagnostic_tie_counting():
    """
    🔍 DIAGNOSTIC DU COMPTAGE TIE
    """
    print("🔍 DIAGNOSTIC SPÉCIFIQUE DU COMPTAGE TIE")
    print("=" * 50)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    # Test sur 10 parties seulement
    pourcentage_test = 0.01  # 1% = 10 parties
    nb_parties_total = len(parties)
    nb_parties_test = int(nb_parties_total * pourcentage_test)
    parties_test = parties[-nb_parties_test:]
    
    print(f"📊 Test sur {len(parties_test)} parties")
    
    total_mains = 0
    total_tie_reels = 0
    total_banker_reels = 0
    total_player_reels = 0
    total_mains_analysees = 0
    total_tie_comptes = 0
    
    for idx, partie in enumerate(parties_test):
        print(f"\n--- PARTIE {idx+1} ---")
        mains = partie.get('mains', [])
        print(f"Mains dans la partie: {len(mains)}")
        
        if len(mains) < 10:
            continue
            
        # Compter les résultats réels dans cette partie
        tie_partie = 0
        banker_partie = 0
        player_partie = 0
        
        for main in mains:
            resultat = main.get('index3_result')
            if resultat == 'TIE':
                tie_partie += 1
            elif resultat == 'BANKER':
                banker_partie += 1
            elif resultat == 'PLAYER':
                player_partie += 1
                
        print(f"Résultats réels: TIE={tie_partie}, BANKER={banker_partie}, PLAYER={player_partie}")
        
        total_mains += len(mains)
        total_tie_reels += tie_partie
        total_banker_reels += banker_partie
        total_player_reels += player_partie
        
        # Simuler l'évaluation pour cette partie
        mains_analysees_partie = 0
        tie_comptes_partie = 0
        
        for i in range(10, len(mains)):
            mains_analyse = mains[:i]
            main_cible = mains[i]
            resultat_reel = main_cible.get('index3_result')
            
            if not resultat_reel:
                continue
                
            mains_analysees_partie += 1
            
            if resultat_reel == 'TIE':
                tie_comptes_partie += 1
                
        print(f"Évaluation: {mains_analysees_partie} mains analysées, {tie_comptes_partie} TIE comptés")
        
        total_mains_analysees += mains_analysees_partie
        total_tie_comptes += tie_comptes_partie
    
    print(f"\n📊 RÉSUMÉ GLOBAL")
    print("=" * 30)
    print(f"Total mains dans les parties: {total_mains}")
    print(f"Total TIE réels: {total_tie_reels}")
    print(f"Total BANKER réels: {total_banker_reels}")
    print(f"Total PLAYER réels: {total_player_reels}")
    print(f"Total mains analysées: {total_mains_analysees}")
    print(f"Total TIE comptés: {total_tie_comptes}")
    
    # Calculer les proportions
    if total_mains > 0:
        prop_tie_reel = (total_tie_reels / total_mains) * 100
        print(f"Proportion TIE réelle: {prop_tie_reel:.1f}%")
        
    if total_mains_analysees > 0:
        prop_tie_compte = (total_tie_comptes / total_mains_analysees) * 100
        print(f"Proportion TIE comptée: {prop_tie_compte:.1f}%")
        
    # Vérification mathématique
    print(f"\n🔍 VÉRIFICATION MATHÉMATIQUE")
    print("=" * 30)
    
    # Dans le baccarat, TIE représente environ 9.5% des résultats
    tie_attendu_theorique = total_mains_analysees * 0.095
    print(f"TIE attendu théorique (9.5%): {tie_attendu_theorique:.0f}")
    print(f"TIE observé: {total_tie_comptes}")
    
    if total_tie_comptes > tie_attendu_theorique * 2:
        print("❌ ANOMALIE: Trop de TIE comptés")
    elif total_tie_comptes < tie_attendu_theorique * 0.5:
        print("❌ ANOMALIE: Trop peu de TIE comptés")
    else:
        print("✅ Comptage TIE cohérent")

def test_structure_donnees():
    """
    🔬 TEST STRUCTURE DES DONNÉES
    """
    print(f"\n🔬 TEST STRUCTURE DES DONNÉES")
    print("=" * 30)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    if len(parties) > 0:
        partie = parties[0]
        mains = partie.get('mains', [])
        
        print(f"Première partie: {len(mains)} mains")
        
        # Examiner les 20 premières mains
        for i in range(min(20, len(mains))):
            main = mains[i]
            resultat = main.get('index3_result')
            print(f"Main {i+1}: {resultat}")

if __name__ == "__main__":
    try:
        diagnostic_tie_counting()
        test_structure_donnees()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
