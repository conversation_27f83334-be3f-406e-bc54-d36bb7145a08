# Bases de Données et ORM - Bonnes Pratiques 2024

## 🗄️ SQLAlchemy 2.0 - ORM Moderne

### Configuration et Modèles Avancés
```python
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker, selectinload, joinedload
from sqlalchemy.sql import select, update, delete, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.types import TypeDecorator, VARCHAR
from datetime import datetime
from typing import Optional, List, Dict, Any
import uuid
import json

# Configuration de base
DATABASE_URL = "postgresql+asyncpg://user:password@localhost/dbname"
SYNC_DATABASE_URL = "postgresql://user:password@localhost/dbname"

# Moteurs de base de données
async_engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # Log des requêtes SQL
    pool_size=20,
    max_overflow=0,
    pool_pre_ping=True,  # Vérification de connexion
    pool_recycle=3600,   # Recyclage des connexions
)

sync_engine = create_engine(
    SYNC_DATABASE_URL,
    echo=True,
    pool_size=20,
    max_overflow=0,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# Sessions
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

# Base déclarative
Base = declarative_base()

# Types personnalisés
class JSONType(TypeDecorator):
    """Type JSON personnalisé pour compatibilité."""
    impl = VARCHAR
    cache_ok = True
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value
    
    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value

# Mixins pour fonctionnalités communes
class TimestampMixin:
    """Mixin pour timestamps automatiques."""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class UUIDMixin:
    """Mixin pour clé primaire UUID."""
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

# Modèles
class User(Base, UUIDMixin, TimestampMixin):
    """Modèle utilisateur avec toutes les bonnes pratiques."""
    __tablename__ = "users"
    
    # Champs de base
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Informations personnelles
    first_name = Column(String(100))
    last_name = Column(String(100))
    full_name = Column(String(200), index=True)  # Champ calculé
    
    # Statut et métadonnées
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    last_login = Column(DateTime)
    login_count = Column(Integer, default=0)
    
    # Données flexibles
    preferences = Column(JSONB)  # PostgreSQL JSONB
    metadata = Column(JSONType)  # Type personnalisé pour autres DB
    
    # Relations
    posts = relationship("Post", back_populates="author", lazy="dynamic")
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"
    
    @property
    def display_name(self) -> str:
        """Nom d'affichage calculé."""
        if self.full_name:
            return self.full_name
        return f"{self.first_name} {self.last_name}".strip() or self.username
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            "id": str(self.id),
            "email": self.email,
            "username": self.username,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "preferences": self.preferences,
        }

class UserProfile(Base, UUIDMixin, TimestampMixin):
    """Profil utilisateur étendu."""
    __tablename__ = "user_profiles"
    
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    bio = Column(Text)
    avatar_url = Column(String(500))
    website = Column(String(200))
    location = Column(String(100))
    birth_date = Column(DateTime)
    
    # Statistiques
    followers_count = Column(Integer, default=0)
    following_count = Column(Integer, default=0)
    posts_count = Column(Integer, default=0)
    
    # Relations
    user = relationship("User", back_populates="profile")

class Post(Base, UUIDMixin, TimestampMixin):
    """Modèle de publication."""
    __tablename__ = "posts"
    
    title = Column(String(200), nullable=False, index=True)
    content = Column(Text, nullable=False)
    slug = Column(String(250), unique=True, nullable=False, index=True)
    
    # Statut
    is_published = Column(Boolean, default=False, nullable=False)
    published_at = Column(DateTime)
    
    # Métadonnées
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    
    # Relations
    author_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    author = relationship("User", back_populates="posts")
    
    # Index composé pour performance
    __table_args__ = (
        {"postgresql_partition_by": "RANGE (created_at)"},  # Partitioning
    )

# Repository Pattern
class BaseRepository:
    """Repository de base avec opérations CRUD."""
    
    def __init__(self, session: AsyncSession, model_class):
        self.session = session
        self.model_class = model_class
    
    async def create(self, **kwargs) -> Any:
        """Créer un nouvel enregistrement."""
        instance = self.model_class(**kwargs)
        self.session.add(instance)
        await self.session.commit()
        await self.session.refresh(instance)
        return instance
    
    async def get_by_id(self, id: uuid.UUID) -> Optional[Any]:
        """Récupérer par ID."""
        result = await self.session.execute(
            select(self.model_class).where(self.model_class.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_all(
        self, 
        offset: int = 0, 
        limit: int = 100,
        order_by: str = "created_at"
    ) -> List[Any]:
        """Récupérer tous les enregistrements avec pagination."""
        order_column = getattr(self.model_class, order_by, self.model_class.created_at)
        
        result = await self.session.execute(
            select(self.model_class)
            .order_by(order_column.desc())
            .offset(offset)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def update(self, id: uuid.UUID, **kwargs) -> Optional[Any]:
        """Mettre à jour un enregistrement."""
        result = await self.session.execute(
            update(self.model_class)
            .where(self.model_class.id == id)
            .values(**kwargs)
            .returning(self.model_class)
        )
        
        updated_instance = result.scalar_one_or_none()
        if updated_instance:
            await self.session.commit()
            await self.session.refresh(updated_instance)
        
        return updated_instance
    
    async def delete(self, id: uuid.UUID) -> bool:
        """Supprimer un enregistrement."""
        result = await self.session.execute(
            delete(self.model_class).where(self.model_class.id == id)
        )
        
        deleted_count = result.rowcount
        if deleted_count > 0:
            await self.session.commit()
            return True
        return False
    
    async def count(self) -> int:
        """Compter les enregistrements."""
        result = await self.session.execute(
            select(func.count(self.model_class.id))
        )
        return result.scalar()

class UserRepository(BaseRepository):
    """Repository spécialisé pour les utilisateurs."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(session, User)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Récupérer un utilisateur par email."""
        result = await self.session.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """Récupérer un utilisateur par nom d'utilisateur."""
        result = await self.session.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_with_profile(self, user_id: uuid.UUID) -> Optional[User]:
        """Récupérer un utilisateur avec son profil."""
        result = await self.session.execute(
            select(User)
            .options(joinedload(User.profile))
            .where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def get_active_users(self, limit: int = 100) -> List[User]:
        """Récupérer les utilisateurs actifs."""
        result = await self.session.execute(
            select(User)
            .where(User.is_active == True)
            .order_by(User.last_login.desc())
            .limit(limit)
        )
        return result.scalars().all()
    
    async def search_users(self, query: str, limit: int = 50) -> List[User]:
        """Rechercher des utilisateurs."""
        search_pattern = f"%{query}%"
        
        result = await self.session.execute(
            select(User)
            .where(
                (User.username.ilike(search_pattern)) |
                (User.full_name.ilike(search_pattern)) |
                (User.email.ilike(search_pattern))
            )
            .order_by(User.username)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def update_login_info(self, user_id: uuid.UUID) -> None:
        """Mettre à jour les informations de connexion."""
        await self.session.execute(
            update(User)
            .where(User.id == user_id)
            .values(
                last_login=datetime.utcnow(),
                login_count=User.login_count + 1
            )
        )
        await self.session.commit()

class PostRepository(BaseRepository):
    """Repository pour les publications."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(session, Post)
    
    async def get_published_posts(
        self, 
        offset: int = 0, 
        limit: int = 20
    ) -> List[Post]:
        """Récupérer les publications publiées."""
        result = await self.session.execute(
            select(Post)
            .options(joinedload(Post.author))
            .where(Post.is_published == True)
            .order_by(Post.published_at.desc())
            .offset(offset)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_user_posts(
        self, 
        user_id: uuid.UUID, 
        include_drafts: bool = False
    ) -> List[Post]:
        """Récupérer les publications d'un utilisateur."""
        query = select(Post).where(Post.author_id == user_id)
        
        if not include_drafts:
            query = query.where(Post.is_published == True)
        
        query = query.order_by(Post.created_at.desc())
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def get_by_slug(self, slug: str) -> Optional[Post]:
        """Récupérer une publication par slug."""
        result = await self.session.execute(
            select(Post)
            .options(joinedload(Post.author))
            .where(Post.slug == slug)
        )
        return result.scalar_one_or_none()
    
    async def increment_view_count(self, post_id: uuid.UUID) -> None:
        """Incrémenter le compteur de vues."""
        await self.session.execute(
            update(Post)
            .where(Post.id == post_id)
            .values(view_count=Post.view_count + 1)
        )
        await self.session.commit()

# Service Layer
class UserService:
    """Service métier pour les utilisateurs."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.user_repo = UserRepository(session)
    
    async def create_user(
        self, 
        email: str, 
        username: str, 
        password: str,
        **kwargs
    ) -> User:
        """Créer un nouvel utilisateur."""
        
        # Vérifier l'unicité
        existing_user = await self.user_repo.get_by_email(email)
        if existing_user:
            raise ValueError("Email already exists")
        
        existing_username = await self.user_repo.get_by_username(username)
        if existing_username:
            raise ValueError("Username already exists")
        
        # Hasher le mot de passe (utiliser bcrypt en réalité)
        password_hash = f"hashed_{password}"
        
        # Créer l'utilisateur
        user = await self.user_repo.create(
            email=email,
            username=username,
            password_hash=password_hash,
            **kwargs
        )
        
        return user
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authentifier un utilisateur."""
        user = await self.user_repo.get_by_email(email)
        
        if not user or not user.is_active:
            return None
        
        # Vérifier le mot de passe (utiliser bcrypt en réalité)
        if user.password_hash != f"hashed_{password}":
            return None
        
        # Mettre à jour les informations de connexion
        await self.user_repo.update_login_info(user.id)
        
        return user

# Gestion des sessions et contexte
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_db_session():
    """Context manager pour les sessions de base de données."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# Dépendance FastAPI
async def get_db():
    """Dépendance pour obtenir une session de base de données."""
    async with get_db_session() as session:
        yield session

# Migrations avec Alembic
# alembic/env.py configuration
"""
from alembic import context
from sqlalchemy import engine_from_config, pool
from logging.config import fileConfig
import asyncio
from sqlalchemy.ext.asyncio import AsyncEngine

# Import your models here
from your_app.models import Base

config = context.config
fileConfig(config.config_file_name)
target_metadata = Base.metadata

def run_migrations_offline():
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()

async def run_migrations_online():
    connectable = AsyncEngine(
        engine_from_config(
            config.get_section(config.config_ini_section),
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
        )
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
"""
```

Cette architecture moderne avec SQLAlchemy 2.0 offre une base solide pour des applications Python robustes avec gestion asynchrone, patterns repository, et bonnes pratiques de performance.
