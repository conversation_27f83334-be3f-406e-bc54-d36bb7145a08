#!/usr/bin/env python3
"""
ANALYSEUR FRACTAL COMPLET POUR DATASET BACCARAT
Basé sur l'analyse R/S et l'exposant de Hurst pour la prédiction
Implémentation complète selon la recherche fractale maîtrisée
"""

import json
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Tentative d'import des bibliothèques optimisées
try:
    import orjson
    HAS_ORJSON = True
    print("✅ orjson disponible pour optimisation")
except ImportError:
    HAS_ORJSON = False
    print("⚠️  orjson non disponible, utilisation de json standard")

try:
    import nolds
    HAS_NOLDS = True
    print("✅ nolds disponible pour analyse fractale")
except ImportError:
    HAS_NOLDS = False
    print("⚠️  nolds non disponible, utilisation d'implémentation manuelle")

@dataclass
class ResultatAnalyseFractale:
    """Structure pour stocker les résultats d'analyse fractale d'une partie"""
    
    # Identification
    numero_partie: int
    nb_mains_total: int
    nb_mains_valides: int
    
    # Exposants de Hurst calculés
    hurst_resultats: float
    hurst_index5: float
    hurst_index1: float
    hurst_index2: float
    
    # Dimensions fractales (D = 2 - H)
    dimension_fractale_resultats: float
    dimension_fractale_index5: float
    
    # Classification des processus
    type_persistance_resultats: str
    type_persistance_index5: str
    
    # Métriques de qualité
    qualite_estimation: str
    confiance_prediction: float
    
    # Prédiction pour main suivante
    prediction_main_suivante: str
    probabilite_prediction: float
    
    # Statistiques descriptives
    moyenne_resultats: float
    variance_resultats: float
    entropie_shannon: float
    
    # Métadonnées
    timestamp_analyse: str

class AnalyseurFractalBaccarat:
    """
    Analyseur fractal complet pour dataset baccarat
    Implémentation basée sur l'analyse R/S et l'exposant de Hurst
    """
    
    def __init__(self, dataset_path: str = "dataset_baccarat_lupasco_20250626_044753.json"):
        """
        Initialise l'analyseur fractal
        
        Args:
            dataset_path: Chemin vers le dataset JSON
        """
        self.dataset_path = dataset_path
        self.resultats_analyses = []
        
        # Règles de transition INDEX1 (déterministes)
        self.regles_transition_index1 = {
            # Si INDEX2 = C : inversion
            ('0', 'C'): '1',
            ('1', 'C'): '0',
            # Si INDEX2 = A : conservation
            ('0', 'A'): '0',
            ('1', 'A'): '1',
            # Si INDEX2 = B : conservation
            ('0', 'B'): '0',
            ('1', 'B'): '1'
        }
        
        # Mapping pour conversion numérique
        self.mapping_resultats = {'BANKER': 1, 'PLAYER': -1, 'TIE': 0}
        self.mapping_index2 = {'A': 1, 'B': 2, 'C': 3}
        
        print(f"🔬 Analyseur Fractal Baccarat initialisé")
        print(f"📂 Dataset: {self.dataset_path}")
    
    def charger_dataset(self) -> Dict:
        """
        Charge le dataset avec optimisation selon les bibliothèques disponibles
        
        Returns:
            Dict: Dataset chargé
        """
        print("📂 Chargement du dataset...")
        
        try:
            if HAS_ORJSON:
                print("🚀 Utilisation orjson pour performance optimale")
                with open(self.dataset_path, 'rb') as f:
                    dataset = orjson.loads(f.read())
            else:
                print("📦 Utilisation json standard")
                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                    dataset = json.load(f)
            
            nb_parties = len(dataset.get('parties', []))
            print(f"✅ Dataset chargé: {nb_parties} parties")
            return dataset
            
        except Exception as e:
            print(f"❌ Erreur chargement dataset: {e}")
            raise
    
    def hurst_rs_manuel(self, serie_temporelle: np.ndarray) -> float:
        """
        Calcul manuel de l'exposant de Hurst par analyse R/S
        Implémentation exacte selon la méthodologie du document de recherche
        
        Args:
            serie_temporelle: Série temporelle à analyser
            
        Returns:
            float: Valeur R/S pour cette série
        """
        N = len(serie_temporelle)
        if N < 4:
            return np.nan
        
        # Étape 1: Calcul de la moyenne et recentrage
        mu = np.mean(serie_temporelle)
        serie_centree = serie_temporelle - mu
        
        # Étape 2: Série cumulative
        serie_cumulative = np.cumsum(serie_centree)
        
        # Étape 3: Calcul de la plage (Range)
        R = np.max(serie_cumulative) - np.min(serie_cumulative)
        
        # Étape 4: Calcul de l'écart-type
        S = np.std(serie_temporelle, ddof=1)
        
        # Éviter division par zéro
        if S == 0 or R == 0:
            return np.nan
        
        # Étape 5: Plage rescalée
        RS = R / S
        
        return RS
    
    def estimer_hurst_regression(self, serie_temporelle: np.ndarray, min_window: int = 4) -> float:
        """
        Estimation de l'exposant de Hurst par régression log-log
        Segmentation récursive comme décrit dans le document de recherche
        
        Args:
            serie_temporelle: Série temporelle à analyser
            min_window: Taille minimale de fenêtre
            
        Returns:
            float: Exposant de Hurst estimé
        """
        N = len(serie_temporelle)
        if N < min_window:
            return np.nan
        
        # Tailles de fenêtres (puissances de 2 décroissantes)
        tailles = []
        rs_values = []
        
        taille_actuelle = N
        while taille_actuelle >= min_window:
            # Calcul R/S pour cette taille
            rs_segments = []
            nb_segments = N // taille_actuelle
            
            for i in range(nb_segments):
                debut = i * taille_actuelle
                fin = debut + taille_actuelle
                segment = serie_temporelle[debut:fin]
                rs_segment = self.hurst_rs_manuel(segment)
                if not np.isnan(rs_segment) and rs_segment > 0:
                    rs_segments.append(rs_segment)
            
            if rs_segments:
                rs_moyenne = np.mean(rs_segments)
                tailles.append(taille_actuelle)
                rs_values.append(rs_moyenne)
            
            taille_actuelle //= 2
        
        # Régression log-log
        if len(tailles) < 2:
            return np.nan
        
        try:
            log_tailles = np.log(tailles)
            log_rs = np.log(rs_values)
            
            # Vérifier que les valeurs sont valides
            if np.any(np.isnan(log_tailles)) or np.any(np.isnan(log_rs)):
                return np.nan
            
            # Régression linéaire
            coeffs = np.polyfit(log_tailles, log_rs, 1)
            hurst_exponent = coeffs[0]
            
            # Validation de l'exposant de Hurst (doit être entre 0 et 1)
            if 0 <= hurst_exponent <= 1:
                return hurst_exponent
            else:
                return np.nan
                
        except Exception:
            return np.nan
    
    def calculer_entropie_shannon(self, sequence: List) -> float:
        """
        Calcule l'entropie de Shannon d'une séquence
        
        Args:
            sequence: Séquence à analyser
            
        Returns:
            float: Entropie de Shannon
        """
        if not sequence:
            return 0.0
        
        # Compter les occurrences
        from collections import Counter
        compteur = Counter(sequence)
        total = len(sequence)
        
        # Calculer l'entropie
        entropie = 0.0
        for count in compteur.values():
            if count > 0:
                p = count / total
                entropie -= p * np.log2(p)
        
        return entropie
    
    def extraire_sequences_partie(self, partie: Dict) -> Dict[str, np.ndarray]:
        """
        Extrait les différentes séquences d'une partie pour analyse fractale
        
        Args:
            partie: Données d'une partie
            
        Returns:
            Dict: Séquences extraites
        """
        mains = partie.get('mains', [])
        
        # Filtrer les mains valides (ignorer les mains dummy)
        mains_valides = [main for main in mains if main.get('main_number') is not None]
        
        if len(mains_valides) < 4:
            return {}
        
        sequences = {
            'resultats': [],
            'index5_numerique': [],
            'index1': [],
            'index2_numerique': [],
            'index5_brut': []
        }
        
        for main in mains_valides:
            # Séquence des résultats
            resultat = main.get('index3_result', '')
            if resultat in self.mapping_resultats:
                sequences['resultats'].append(self.mapping_resultats[resultat])
            
            # INDEX5 complet
            index5 = main.get('index5_combined', '')
            sequences['index5_brut'].append(index5)
            
            if index5:
                # Décomposer INDEX5: "index1_index2_index3"
                parties_index5 = index5.split('_')
                if len(parties_index5) >= 3:
                    # INDEX1 (0 ou 1)
                    try:
                        index1 = int(parties_index5[0])
                        sequences['index1'].append(index1)
                    except ValueError:
                        pass
                    
                    # INDEX2 (A, B, C)
                    index2 = parties_index5[1]
                    if index2 in self.mapping_index2:
                        sequences['index2_numerique'].append(self.mapping_index2[index2])
                    
                    # INDEX5 comme valeur numérique (hash simple)
                    sequences['index5_numerique'].append(hash(index5) % 1000)
        
        # Convertir en numpy arrays
        for key in sequences:
            if sequences[key]:
                sequences[key] = np.array(sequences[key])
            else:
                sequences[key] = np.array([])
        
        return sequences

    def analyser_partie_fractale(self, partie: Dict) -> ResultatAnalyseFractale:
        """
        Analyse fractale complète d'une partie de baccarat

        Args:
            partie: Données d'une partie

        Returns:
            ResultatAnalyseFractale: Résultats de l'analyse
        """
        numero_partie = partie.get('partie_number', 0)

        # Initialiser le résultat avec des valeurs par défaut
        resultat = ResultatAnalyseFractale(
            numero_partie=numero_partie,
            nb_mains_total=len(partie.get('mains', [])),
            nb_mains_valides=0,
            hurst_resultats=np.nan,
            hurst_index5=np.nan,
            hurst_index1=np.nan,
            hurst_index2=np.nan,
            dimension_fractale_resultats=np.nan,
            dimension_fractale_index5=np.nan,
            type_persistance_resultats='indetermine',
            type_persistance_index5='indetermine',
            qualite_estimation='insuffisant',
            confiance_prediction=0.0,
            prediction_main_suivante='INDETERMINE',
            probabilite_prediction=0.0,
            moyenne_resultats=np.nan,
            variance_resultats=np.nan,
            entropie_shannon=np.nan,
            timestamp_analyse=datetime.now().isoformat()
        )

        # Extraire les séquences
        sequences = self.extraire_sequences_partie(partie)

        if not sequences or len(sequences.get('resultats', [])) < 4:
            return resultat

        resultat.nb_mains_valides = len(sequences['resultats'])

        # Analyse de la séquence des résultats
        seq_resultats = sequences['resultats']
        if len(seq_resultats) >= 4:
            resultat.hurst_resultats = self.estimer_hurst_regression(seq_resultats)
            resultat.dimension_fractale_resultats = 2 - resultat.hurst_resultats if not np.isnan(resultat.hurst_resultats) else np.nan
            resultat.type_persistance_resultats = self.classifier_persistance(resultat.hurst_resultats)
            resultat.moyenne_resultats = np.mean(seq_resultats)
            resultat.variance_resultats = np.var(seq_resultats)

        # Analyse de la séquence INDEX5
        seq_index5 = sequences['index5_numerique']
        if len(seq_index5) >= 4:
            resultat.hurst_index5 = self.estimer_hurst_regression(seq_index5)
            resultat.dimension_fractale_index5 = 2 - resultat.hurst_index5 if not np.isnan(resultat.hurst_index5) else np.nan
            resultat.type_persistance_index5 = self.classifier_persistance(resultat.hurst_index5)

        # Analyse de la séquence INDEX1
        seq_index1 = sequences['index1']
        if len(seq_index1) >= 4:
            resultat.hurst_index1 = self.estimer_hurst_regression(seq_index1.astype(float))

        # Analyse de la séquence INDEX2
        seq_index2 = sequences['index2_numerique']
        if len(seq_index2) >= 4:
            resultat.hurst_index2 = self.estimer_hurst_regression(seq_index2.astype(float))

        # Calcul de l'entropie de Shannon
        if sequences['index5_brut'].size > 0:
            resultat.entropie_shannon = self.calculer_entropie_shannon(sequences['index5_brut'].tolist())

        # Évaluation de la qualité de l'estimation
        resultat.qualite_estimation = self.evaluer_qualite_estimation(resultat)

        # Prédiction pour la main suivante
        prediction_info = self.predire_main_suivante(sequences, resultat)
        resultat.prediction_main_suivante = prediction_info['prediction']
        resultat.probabilite_prediction = prediction_info['probabilite']
        resultat.confiance_prediction = prediction_info['confiance']

        return resultat

    def classifier_persistance(self, hurst_exponent: float) -> str:
        """
        Classifie le type de persistance selon l'exposant de Hurst

        Args:
            hurst_exponent: Exposant de Hurst calculé

        Returns:
            str: Type de persistance
        """
        if np.isnan(hurst_exponent):
            return 'indetermine'

        if hurst_exponent > 0.55:
            return 'persistant'
        elif hurst_exponent < 0.45:
            return 'anti-persistant'
        else:
            return 'aleatoire'

    def evaluer_qualite_estimation(self, resultat: ResultatAnalyseFractale) -> str:
        """
        Évalue la qualité de l'estimation fractale

        Args:
            resultat: Résultat de l'analyse fractale

        Returns:
            str: Qualité de l'estimation
        """
        if resultat.nb_mains_valides < 4:
            return 'insuffisant'
        elif resultat.nb_mains_valides < 10:
            return 'faible'
        elif resultat.nb_mains_valides < 20:
            return 'moyen'
        elif resultat.nb_mains_valides < 40:
            return 'bon'
        else:
            return 'excellent'

    def predire_main_suivante(self, sequences: Dict[str, np.ndarray], resultat: ResultatAnalyseFractale) -> Dict:
        """
        Prédit la main suivante basée sur l'analyse fractale

        Args:
            sequences: Séquences extraites de la partie
            resultat: Résultat de l'analyse fractale

        Returns:
            Dict: Information de prédiction
        """
        prediction_info = {
            'prediction': 'INDETERMINE',
            'probabilite': 0.0,
            'confiance': 0.0,
            'methode': 'fractale'
        }

        seq_resultats = sequences.get('resultats', np.array([]))

        if len(seq_resultats) < 2 or np.isnan(resultat.hurst_resultats):
            return prediction_info

        # Utiliser les dernières valeurs pour la prédiction
        derniere_valeur = seq_resultats[-1]
        hurst = resultat.hurst_resultats

        # Seuil de persistance/anti-persistance
        seuil_persistance = 0.6
        seuil_anti_persistance = 0.4

        if hurst > seuil_persistance:
            # Processus persistant : tendance continue
            if derniere_valeur > 0:  # BANKER
                prediction_info['prediction'] = 'BANKER'
                prediction_info['probabilite'] = 0.6 + (hurst - 0.5) * 0.8
            elif derniere_valeur < 0:  # PLAYER
                prediction_info['prediction'] = 'PLAYER'
                prediction_info['probabilite'] = 0.6 + (hurst - 0.5) * 0.8
            else:  # TIE
                prediction_info['prediction'] = 'TIE'
                prediction_info['probabilite'] = 0.4

            prediction_info['confiance'] = (hurst - 0.5) * 2

        elif hurst < seuil_anti_persistance:
            # Processus anti-persistant : inversion de tendance
            if derniere_valeur > 0:  # BANKER → PLAYER
                prediction_info['prediction'] = 'PLAYER'
                prediction_info['probabilite'] = 0.6 + (0.5 - hurst) * 0.8
            elif derniere_valeur < 0:  # PLAYER → BANKER
                prediction_info['prediction'] = 'BANKER'
                prediction_info['probabilite'] = 0.6 + (0.5 - hurst) * 0.8
            else:  # TIE → BANKER (arbitraire)
                prediction_info['prediction'] = 'BANKER'
                prediction_info['probabilite'] = 0.5

            prediction_info['confiance'] = (0.5 - hurst) * 2

        else:
            # Processus proche du hasard
            prediction_info['prediction'] = 'ALEATOIRE'
            prediction_info['probabilite'] = 0.33
            prediction_info['confiance'] = 0.1

        # Ajustement de la confiance selon la qualité de l'estimation
        facteur_qualite = {
            'insuffisant': 0.1,
            'faible': 0.3,
            'moyen': 0.6,
            'bon': 0.8,
            'excellent': 1.0
        }

        prediction_info['confiance'] *= facteur_qualite.get(resultat.qualite_estimation, 0.1)

        return prediction_info

    def analyser_dataset_complet(self, nb_parties_max: Optional[int] = None) -> pd.DataFrame:
        """
        Analyse fractale complète du dataset

        Args:
            nb_parties_max: Nombre maximum de parties à analyser (None = toutes)

        Returns:
            pd.DataFrame: Résultats de toutes les analyses
        """
        print("🔬 ANALYSE FRACTALE COMPLÈTE DU DATASET BACCARAT")
        print("=" * 60)

        # Charger le dataset
        dataset = self.charger_dataset()
        parties = dataset.get('parties', [])

        if nb_parties_max:
            parties = parties[:nb_parties_max]
            print(f"🎯 Limitation à {nb_parties_max} parties")

        print(f"📊 Analyse de {len(parties)} parties...")

        # Analyser chaque partie
        resultats = []
        for i, partie in enumerate(parties):
            if i % 100 == 0 and i > 0:
                print(f"   📈 Progression: {i}/{len(parties)} parties analysées ({i/len(parties)*100:.1f}%)")

            try:
                resultat = self.analyser_partie_fractale(partie)
                resultats.append(resultat)
            except Exception as e:
                print(f"⚠️  Erreur partie {partie.get('partie_number', i)}: {e}")
                continue

        print(f"✅ Analyse terminée: {len(resultats)} parties analysées avec succès")

        # Convertir en DataFrame
        df_resultats = self.convertir_resultats_dataframe(resultats)

        # Générer les statistiques globales
        self.generer_statistiques_globales(df_resultats)

        return df_resultats

    def convertir_resultats_dataframe(self, resultats: List[ResultatAnalyseFractale]) -> pd.DataFrame:
        """
        Convertit les résultats en DataFrame pandas

        Args:
            resultats: Liste des résultats d'analyse

        Returns:
            pd.DataFrame: DataFrame avec tous les résultats
        """
        data = []
        for resultat in resultats:
            data.append({
                'numero_partie': resultat.numero_partie,
                'nb_mains_total': resultat.nb_mains_total,
                'nb_mains_valides': resultat.nb_mains_valides,
                'hurst_resultats': resultat.hurst_resultats,
                'hurst_index5': resultat.hurst_index5,
                'hurst_index1': resultat.hurst_index1,
                'hurst_index2': resultat.hurst_index2,
                'dimension_fractale_resultats': resultat.dimension_fractale_resultats,
                'dimension_fractale_index5': resultat.dimension_fractale_index5,
                'type_persistance_resultats': resultat.type_persistance_resultats,
                'type_persistance_index5': resultat.type_persistance_index5,
                'qualite_estimation': resultat.qualite_estimation,
                'confiance_prediction': resultat.confiance_prediction,
                'prediction_main_suivante': resultat.prediction_main_suivante,
                'probabilite_prediction': resultat.probabilite_prediction,
                'moyenne_resultats': resultat.moyenne_resultats,
                'variance_resultats': resultat.variance_resultats,
                'entropie_shannon': resultat.entropie_shannon,
                'timestamp_analyse': resultat.timestamp_analyse
            })

        return pd.DataFrame(data)

    def generer_statistiques_globales(self, df: pd.DataFrame) -> None:
        """
        Génère et affiche les statistiques globales de l'analyse

        Args:
            df: DataFrame avec les résultats
        """
        print("\n📊 STATISTIQUES GLOBALES DE L'ANALYSE FRACTALE")
        print("=" * 60)

        # Statistiques de base
        print(f"📈 Nombre total de parties analysées: {len(df)}")
        print(f"📈 Nombre moyen de mains par partie: {df['nb_mains_valides'].mean():.1f}")

        # Distribution des exposants de Hurst
        hurst_valides = df['hurst_resultats'].dropna()
        if len(hurst_valides) > 0:
            print(f"\n🔬 EXPOSANTS DE HURST (RÉSULTATS)")
            print(f"   Moyenne: {hurst_valides.mean():.4f}")
            print(f"   Médiane: {hurst_valides.median():.4f}")
            print(f"   Écart-type: {hurst_valides.std():.4f}")
            print(f"   Min: {hurst_valides.min():.4f}")
            print(f"   Max: {hurst_valides.max():.4f}")

        # Distribution des types de persistance
        print(f"\n📊 DISTRIBUTION DES TYPES DE PERSISTANCE")
        persistance_counts = df['type_persistance_resultats'].value_counts()
        for type_pers, count in persistance_counts.items():
            pourcentage = count / len(df) * 100
            print(f"   {type_pers}: {count} parties ({pourcentage:.1f}%)")

        # Qualité des estimations
        print(f"\n🎯 QUALITÉ DES ESTIMATIONS")
        qualite_counts = df['qualite_estimation'].value_counts()
        for qualite, count in qualite_counts.items():
            pourcentage = count / len(df) * 100
            print(f"   {qualite}: {count} parties ({pourcentage:.1f}%)")

        # Prédictions
        print(f"\n🔮 PRÉDICTIONS GÉNÉRÉES")
        prediction_counts = df['prediction_main_suivante'].value_counts()
        for prediction, count in prediction_counts.items():
            pourcentage = count / len(df) * 100
            print(f"   {prediction}: {count} parties ({pourcentage:.1f}%)")

        # Confiance moyenne des prédictions
        confiance_moyenne = df['confiance_prediction'].mean()
        print(f"\n🎯 Confiance moyenne des prédictions: {confiance_moyenne:.3f}")

    def sauvegarder_resultats(self, df: pd.DataFrame, nom_fichier: Optional[str] = None) -> str:
        """
        Sauvegarde les résultats dans un fichier CSV

        Args:
            df: DataFrame avec les résultats
            nom_fichier: Nom du fichier (optionnel)

        Returns:
            str: Nom du fichier sauvegardé
        """
        if nom_fichier is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"analyse_fractale_baccarat_{timestamp}.csv"

        df.to_csv(nom_fichier, index=False, encoding='utf-8')
        print(f"💾 Résultats sauvegardés: {nom_fichier}")

        return nom_fichier

    def generer_rapport_detaille(self, df: pd.DataFrame) -> str:
        """
        Génère un rapport détaillé de l'analyse fractale

        Args:
            df: DataFrame avec les résultats

        Returns:
            str: Contenu du rapport
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        rapport = f"""
RAPPORT D'ANALYSE FRACTALE - DATASET BACCARAT
============================================
Généré le: {timestamp}
Dataset analysé: {self.dataset_path}

RÉSUMÉ EXÉCUTIF
===============
Nombre de parties analysées: {len(df)}
Nombre moyen de mains par partie: {df['nb_mains_valides'].mean():.1f}

ANALYSE DES EXPOSANTS DE HURST
==============================
"""

        hurst_valides = df['hurst_resultats'].dropna()
        if len(hurst_valides) > 0:
            rapport += f"""
Résultats (séquences BANKER/PLAYER/TIE):
- Moyenne: {hurst_valides.mean():.4f}
- Médiane: {hurst_valides.median():.4f}
- Écart-type: {hurst_valides.std():.4f}
- Plage: [{hurst_valides.min():.4f}, {hurst_valides.max():.4f}]

Interprétation selon la théorie fractale:
- H > 0.5: Processus persistant (tendances continues)
- H < 0.5: Processus anti-persistant (retour à la moyenne)
- H ≈ 0.5: Processus aléatoire (mouvement brownien)
"""

        # Distribution des types de persistance
        rapport += "\nDISTRIBUTION DES TYPES DE PERSISTANCE\n"
        rapport += "====================================\n"
        persistance_counts = df['type_persistance_resultats'].value_counts()
        for type_pers, count in persistance_counts.items():
            pourcentage = count / len(df) * 100
            rapport += f"{type_pers}: {count} parties ({pourcentage:.1f}%)\n"

        # Recommandations stratégiques
        rapport += "\nRECOMMANDATIONS STRATÉGIQUES\n"
        rapport += "============================\n"

        pct_persistant = persistance_counts.get('persistant', 0) / len(df) * 100
        pct_anti_persistant = persistance_counts.get('anti-persistant', 0) / len(df) * 100

        if pct_persistant > 40:
            rapport += "- Stratégie de suivi de tendance recommandée pour les parties persistantes\n"
        if pct_anti_persistant > 40:
            rapport += "- Stratégie de retour à la moyenne recommandée pour les parties anti-persistantes\n"

        rapport += f"\nConfiance moyenne des prédictions: {df['confiance_prediction'].mean():.3f}\n"

        return rapport

def main():
    """
    Fonction principale pour exécuter l'analyse fractale complète
    """
    print("🔬 ANALYSEUR FRACTAL BACCARAT - DÉMARRAGE")
    print("=" * 60)

    try:
        # Initialiser l'analyseur
        analyseur = AnalyseurFractalBaccarat()

        # Demander à l'utilisateur le nombre de parties à analyser
        print("\n🎯 OPTIONS D'ANALYSE")
        print("1. Analyser toutes les parties (peut prendre du temps)")
        print("2. Analyser un échantillon (100 parties)")
        print("3. Analyser un échantillon personnalisé")

        choix = input("\nVotre choix (1/2/3): ").strip()

        nb_parties_max = None
        if choix == "2":
            nb_parties_max = 100
        elif choix == "3":
            try:
                nb_parties_max = int(input("Nombre de parties à analyser: "))
            except ValueError:
                print("⚠️  Valeur invalide, utilisation de 100 parties par défaut")
                nb_parties_max = 100

        # Exécuter l'analyse
        print(f"\n🚀 Démarrage de l'analyse fractale...")
        df_resultats = analyseur.analyser_dataset_complet(nb_parties_max)

        # Sauvegarder les résultats
        nom_fichier = analyseur.sauvegarder_resultats(df_resultats)

        # Générer et sauvegarder le rapport
        rapport = analyseur.generer_rapport_detaille(df_resultats)
        nom_rapport = nom_fichier.replace('.csv', '_rapport.txt')
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write(rapport)
        print(f"📄 Rapport détaillé sauvegardé: {nom_rapport}")

        # Afficher quelques exemples de résultats
        print("\n🔍 EXEMPLES DE RÉSULTATS")
        print("=" * 40)

        # Parties les plus persistantes
        parties_persistantes = df_resultats[df_resultats['type_persistance_resultats'] == 'persistant']
        if len(parties_persistantes) > 0:
            top_persistant = parties_persistantes.nlargest(3, 'hurst_resultats')
            print("\n🔥 TOP 3 PARTIES LES PLUS PERSISTANTES:")
            for _, row in top_persistant.iterrows():
                print(f"   Partie {row['numero_partie']}: H={row['hurst_resultats']:.4f}, "
                      f"Prédiction={row['prediction_main_suivante']}, "
                      f"Confiance={row['confiance_prediction']:.3f}")

        # Parties les plus anti-persistantes
        parties_anti_persistantes = df_resultats[df_resultats['type_persistance_resultats'] == 'anti-persistant']
        if len(parties_anti_persistantes) > 0:
            top_anti_persistant = parties_anti_persistantes.nsmallest(3, 'hurst_resultats')
            print("\n🔄 TOP 3 PARTIES LES PLUS ANTI-PERSISTANTES:")
            for _, row in top_anti_persistant.iterrows():
                print(f"   Partie {row['numero_partie']}: H={row['hurst_resultats']:.4f}, "
                      f"Prédiction={row['prediction_main_suivante']}, "
                      f"Confiance={row['confiance_prediction']:.3f}")

        # Parties avec la meilleure qualité d'estimation
        parties_excellentes = df_resultats[df_resultats['qualite_estimation'] == 'excellent']
        if len(parties_excellentes) > 0:
            print(f"\n⭐ {len(parties_excellentes)} parties avec qualité d'estimation 'excellent'")
            exemple_excellent = parties_excellentes.iloc[0]
            print(f"   Exemple - Partie {exemple_excellent['numero_partie']}: "
                  f"{exemple_excellent['nb_mains_valides']} mains, "
                  f"H={exemple_excellent['hurst_resultats']:.4f}")

        print(f"\n✅ ANALYSE FRACTALE TERMINÉE AVEC SUCCÈS")
        print(f"📊 Fichiers générés:")
        print(f"   - Données: {nom_fichier}")
        print(f"   - Rapport: {nom_rapport}")

        return df_resultats

    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyser_partie_specifique(numero_partie: int):
    """
    Analyse fractale d'une partie spécifique

    Args:
        numero_partie: Numéro de la partie à analyser
    """
    print(f"🔬 ANALYSE FRACTALE - PARTIE {numero_partie}")
    print("=" * 50)

    try:
        analyseur = AnalyseurFractalBaccarat()
        dataset = analyseur.charger_dataset()
        parties = dataset.get('parties', [])

        # Trouver la partie
        partie_cible = None
        for partie in parties:
            if partie.get('partie_number') == numero_partie:
                partie_cible = partie
                break

        if partie_cible is None:
            print(f"❌ Partie {numero_partie} non trouvée")
            return None

        # Analyser la partie
        resultat = analyseur.analyser_partie_fractale(partie_cible)

        # Afficher les résultats détaillés
        print(f"\n📊 RÉSULTATS DÉTAILLÉS - PARTIE {numero_partie}")
        print("-" * 50)
        print(f"Nombre de mains valides: {resultat.nb_mains_valides}")
        print(f"Exposant de Hurst (résultats): {resultat.hurst_resultats:.4f}")
        print(f"Dimension fractale: {resultat.dimension_fractale_resultats:.4f}")
        print(f"Type de persistance: {resultat.type_persistance_resultats}")
        print(f"Qualité estimation: {resultat.qualite_estimation}")
        print(f"Prédiction main suivante: {resultat.prediction_main_suivante}")
        print(f"Probabilité prédiction: {resultat.probabilite_prediction:.3f}")
        print(f"Confiance: {resultat.confiance_prediction:.3f}")
        print(f"Entropie Shannon: {resultat.entropie_shannon:.3f}")

        return resultat

    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return None

if __name__ == "__main__":
    # Exécution du programme principal
    main()
