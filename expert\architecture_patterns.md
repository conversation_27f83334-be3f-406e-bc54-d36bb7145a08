# Architecture et Design Patterns - Niveau Expert

## 🏗️ Principes SOLID

### Single Responsibility Principle (SRP)
```python
# ❌ Violation du SRP
class User:
    def __init__(self, name, email):
        self.name = name
        self.email = email
    
    def save_to_database(self):
        # Logique de sauvegarde
        pass
    
    def send_email(self):
        # Logique d'envoi d'email
        pass

# ✅ Respect du SRP
class User:
    def __init__(self, name, email):
        self.name = name
        self.email = email

class UserRepository:
    def save(self, user: User):
        # Logique de sauvegarde
        pass

class EmailService:
    def send_email(self, user: User, message: str):
        # Logique d'envoi d'email
        pass
```

### Open/Closed Principle (OCP)
```python
from abc import ABC, abstractmethod
from typing import List

class PaymentProcessor(ABC):
    @abstractmethod
    def process_payment(self, amount: float) -> bool:
        pass

class CreditCardProcessor(PaymentProcessor):
    def process_payment(self, amount: float) -> bool:
        print(f"Processing ${amount} via Credit Card")
        return True

class PayPalProcessor(PaymentProcessor):
    def process_payment(self, amount: float) -> bool:
        print(f"Processing ${amount} via PayPal")
        return True

class BitcoinProcessor(PaymentProcessor):
    def process_payment(self, amount: float) -> bool:
        print(f"Processing ${amount} via Bitcoin")
        return True

class PaymentService:
    def __init__(self):
        self.processors: List[PaymentProcessor] = []
    
    def add_processor(self, processor: PaymentProcessor):
        self.processors.append(processor)
    
    def process_payment(self, processor_type: str, amount: float):
        # Logique de sélection du processeur
        pass
```

### Dependency Inversion Principle (DIP)
```python
from abc import ABC, abstractmethod

class DatabaseInterface(ABC):
    @abstractmethod
    def save(self, data: dict) -> bool:
        pass
    
    @abstractmethod
    def find(self, id: str) -> dict:
        pass

class PostgreSQLDatabase(DatabaseInterface):
    def save(self, data: dict) -> bool:
        print(f"Saving to PostgreSQL: {data}")
        return True
    
    def find(self, id: str) -> dict:
        print(f"Finding in PostgreSQL: {id}")
        return {"id": id, "data": "sample"}

class MongoDatabase(DatabaseInterface):
    def save(self, data: dict) -> bool:
        print(f"Saving to MongoDB: {data}")
        return True
    
    def find(self, id: str) -> dict:
        print(f"Finding in MongoDB: {id}")
        return {"_id": id, "data": "sample"}

class UserService:
    def __init__(self, database: DatabaseInterface):
        self.database = database  # Dépend de l'abstraction, pas de l'implémentation
    
    def create_user(self, user_data: dict):
        return self.database.save(user_data)
    
    def get_user(self, user_id: str):
        return self.database.find(user_id)

# Usage avec injection de dépendance
postgres_db = PostgreSQLDatabase()
user_service = UserService(postgres_db)
```

## 🎯 Design Patterns Essentiels

### Factory Pattern
```python
from abc import ABC, abstractmethod
from enum import Enum

class VehicleType(Enum):
    CAR = "car"
    MOTORCYCLE = "motorcycle"
    TRUCK = "truck"

class Vehicle(ABC):
    @abstractmethod
    def start_engine(self):
        pass
    
    @abstractmethod
    def get_max_speed(self) -> int:
        pass

class Car(Vehicle):
    def start_engine(self):
        return "Car engine started"
    
    def get_max_speed(self) -> int:
        return 200

class Motorcycle(Vehicle):
    def start_engine(self):
        return "Motorcycle engine started"
    
    def get_max_speed(self) -> int:
        return 300

class Truck(Vehicle):
    def start_engine(self):
        return "Truck engine started"
    
    def get_max_speed(self) -> int:
        return 120

class VehicleFactory:
    @staticmethod
    def create_vehicle(vehicle_type: VehicleType) -> Vehicle:
        if vehicle_type == VehicleType.CAR:
            return Car()
        elif vehicle_type == VehicleType.MOTORCYCLE:
            return Motorcycle()
        elif vehicle_type == VehicleType.TRUCK:
            return Truck()
        else:
            raise ValueError(f"Unknown vehicle type: {vehicle_type}")

# Usage
factory = VehicleFactory()
car = factory.create_vehicle(VehicleType.CAR)
print(car.start_engine())
```

### Observer Pattern
```python
from abc import ABC, abstractmethod
from typing import List, Any

class Observer(ABC):
    @abstractmethod
    def update(self, subject: 'Subject', event_data: Any):
        pass

class Subject(ABC):
    def __init__(self):
        self._observers: List[Observer] = []
    
    def attach(self, observer: Observer):
        if observer not in self._observers:
            self._observers.append(observer)
    
    def detach(self, observer: Observer):
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notify(self, event_data: Any = None):
        for observer in self._observers:
            observer.update(self, event_data)

class StockPrice(Subject):
    def __init__(self, symbol: str, price: float):
        super().__init__()
        self.symbol = symbol
        self._price = price
    
    @property
    def price(self) -> float:
        return self._price
    
    @price.setter
    def price(self, new_price: float):
        old_price = self._price
        self._price = new_price
        self.notify({
            'symbol': self.symbol,
            'old_price': old_price,
            'new_price': new_price,
            'change': new_price - old_price
        })

class StockDisplay(Observer):
    def __init__(self, name: str):
        self.name = name
    
    def update(self, subject: Subject, event_data: Any):
        if isinstance(subject, StockPrice):
            print(f"{self.name}: {event_data['symbol']} price changed "
                  f"from ${event_data['old_price']:.2f} to ${event_data['new_price']:.2f}")

class StockAlert(Observer):
    def __init__(self, threshold: float):
        self.threshold = threshold
    
    def update(self, subject: Subject, event_data: Any):
        if abs(event_data['change']) > self.threshold:
            print(f"ALERT: {event_data['symbol']} price changed by ${event_data['change']:.2f}!")

# Usage
stock = StockPrice("AAPL", 150.0)
display = StockDisplay("Main Display")
alert = StockAlert(5.0)

stock.attach(display)
stock.attach(alert)

stock.price = 155.0  # Déclenche les notifications
```

### Strategy Pattern
```python
from abc import ABC, abstractmethod
from typing import List

class SortingStrategy(ABC):
    @abstractmethod
    def sort(self, data: List[int]) -> List[int]:
        pass

class BubbleSort(SortingStrategy):
    def sort(self, data: List[int]) -> List[int]:
        data = data.copy()
        n = len(data)
        for i in range(n):
            for j in range(0, n - i - 1):
                if data[j] > data[j + 1]:
                    data[j], data[j + 1] = data[j + 1], data[j]
        return data

class QuickSort(SortingStrategy):
    def sort(self, data: List[int]) -> List[int]:
        if len(data) <= 1:
            return data
        
        pivot = data[len(data) // 2]
        left = [x for x in data if x < pivot]
        middle = [x for x in data if x == pivot]
        right = [x for x in data if x > pivot]
        
        return self.sort(left) + middle + self.sort(right)

class MergeSort(SortingStrategy):
    def sort(self, data: List[int]) -> List[int]:
        if len(data) <= 1:
            return data
        
        mid = len(data) // 2
        left = self.sort(data[:mid])
        right = self.sort(data[mid:])
        
        return self._merge(left, right)
    
    def _merge(self, left: List[int], right: List[int]) -> List[int]:
        result = []
        i = j = 0
        
        while i < len(left) and j < len(right):
            if left[i] <= right[j]:
                result.append(left[i])
                i += 1
            else:
                result.append(right[j])
                j += 1
        
        result.extend(left[i:])
        result.extend(right[j:])
        return result

class SortContext:
    def __init__(self, strategy: SortingStrategy):
        self._strategy = strategy
    
    def set_strategy(self, strategy: SortingStrategy):
        self._strategy = strategy
    
    def sort_data(self, data: List[int]) -> List[int]:
        return self._strategy.sort(data)

# Usage
data = [64, 34, 25, 12, 22, 11, 90]

context = SortContext(BubbleSort())
print("Bubble Sort:", context.sort_data(data))

context.set_strategy(QuickSort())
print("Quick Sort:", context.sort_data(data))

context.set_strategy(MergeSort())
print("Merge Sort:", context.sort_data(data))
```

## 🏛️ Architecture Hexagonale (Ports & Adapters)

```python
from abc import ABC, abstractmethod
from typing import Optional, List
from dataclasses import dataclass

# Domain Layer
@dataclass
class User:
    id: Optional[str]
    name: str
    email: str
    
    def change_email(self, new_email: str):
        if "@" not in new_email:
            raise ValueError("Invalid email format")
        self.email = new_email

# Ports (Interfaces)
class UserRepositoryPort(ABC):
    @abstractmethod
    def save(self, user: User) -> User:
        pass
    
    @abstractmethod
    def find_by_id(self, user_id: str) -> Optional[User]:
        pass
    
    @abstractmethod
    def find_by_email(self, email: str) -> Optional[User]:
        pass

class EmailServicePort(ABC):
    @abstractmethod
    def send_welcome_email(self, user: User) -> bool:
        pass

# Application Layer (Use Cases)
class UserService:
    def __init__(self, user_repository: UserRepositoryPort, email_service: EmailServicePort):
        self.user_repository = user_repository
        self.email_service = email_service
    
    def create_user(self, name: str, email: str) -> User:
        # Vérifier si l'utilisateur existe déjà
        existing_user = self.user_repository.find_by_email(email)
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Créer nouvel utilisateur
        user = User(id=None, name=name, email=email)
        saved_user = self.user_repository.save(user)
        
        # Envoyer email de bienvenue
        self.email_service.send_welcome_email(saved_user)
        
        return saved_user
    
    def update_user_email(self, user_id: str, new_email: str) -> User:
        user = self.user_repository.find_by_id(user_id)
        if not user:
            raise ValueError("User not found")
        
        user.change_email(new_email)
        return self.user_repository.save(user)

# Adapters (Infrastructure Layer)
class InMemoryUserRepository(UserRepositoryPort):
    def __init__(self):
        self.users: List[User] = []
        self.next_id = 1
    
    def save(self, user: User) -> User:
        if user.id is None:
            user.id = str(self.next_id)
            self.next_id += 1
            self.users.append(user)
        else:
            # Update existing user
            for i, existing_user in enumerate(self.users):
                if existing_user.id == user.id:
                    self.users[i] = user
                    break
        return user
    
    def find_by_id(self, user_id: str) -> Optional[User]:
        for user in self.users:
            if user.id == user_id:
                return user
        return None
    
    def find_by_email(self, email: str) -> Optional[User]:
        for user in self.users:
            if user.email == email:
                return user
        return None

class SMTPEmailService(EmailServicePort):
    def send_welcome_email(self, user: User) -> bool:
        print(f"Sending welcome email to {user.email}")
        # Logique SMTP réelle ici
        return True

# Composition Root (Dependency Injection)
class ApplicationContainer:
    def __init__(self):
        self.user_repository = InMemoryUserRepository()
        self.email_service = SMTPEmailService()
        self.user_service = UserService(self.user_repository, self.email_service)

# Usage
container = ApplicationContainer()
user_service = container.user_service

try:
    user = user_service.create_user("John Doe", "<EMAIL>")
    print(f"Created user: {user}")
    
    updated_user = user_service.update_user_email(user.id, "<EMAIL>")
    print(f"Updated user: {updated_user}")
except ValueError as e:
    print(f"Error: {e}")
```

## 🎪 Command Pattern avec CQRS

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List
from dataclasses import dataclass
from datetime import datetime
import uuid

# Command Pattern
class Command(ABC):
    @abstractmethod
    def execute(self) -> Any:
        pass

class CommandHandler(ABC):
    @abstractmethod
    def handle(self, command: Command) -> Any:
        pass

@dataclass
class CreateUserCommand(Command):
    name: str
    email: str
    
    def execute(self) -> Any:
        return {"action": "create_user", "name": self.name, "email": self.email}

@dataclass
class UpdateUserEmailCommand(Command):
    user_id: str
    new_email: str
    
    def execute(self) -> Any:
        return {"action": "update_email", "user_id": self.user_id, "email": self.new_email}

# Event Sourcing
@dataclass
class Event:
    id: str
    aggregate_id: str
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
    version: int

class EventStore:
    def __init__(self):
        self.events: List[Event] = []
    
    def append_event(self, aggregate_id: str, event_type: str, data: Dict[str, Any], version: int):
        event = Event(
            id=str(uuid.uuid4()),
            aggregate_id=aggregate_id,
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            version=version
        )
        self.events.append(event)
        return event
    
    def get_events(self, aggregate_id: str) -> List[Event]:
        return [e for e in self.events if e.aggregate_id == aggregate_id]

# CQRS - Command Side
class UserCommandHandler(CommandHandler):
    def __init__(self, event_store: EventStore):
        self.event_store = event_store
    
    def handle(self, command: Command) -> Any:
        if isinstance(command, CreateUserCommand):
            return self._handle_create_user(command)
        elif isinstance(command, UpdateUserEmailCommand):
            return self._handle_update_email(command)
        else:
            raise ValueError(f"Unknown command type: {type(command)}")
    
    def _handle_create_user(self, command: CreateUserCommand) -> str:
        user_id = str(uuid.uuid4())
        self.event_store.append_event(
            aggregate_id=user_id,
            event_type="UserCreated",
            data={"name": command.name, "email": command.email},
            version=1
        )
        return user_id
    
    def _handle_update_email(self, command: UpdateUserEmailCommand) -> bool:
        events = self.event_store.get_events(command.user_id)
        if not events:
            raise ValueError("User not found")
        
        version = len(events) + 1
        self.event_store.append_event(
            aggregate_id=command.user_id,
            event_type="UserEmailUpdated",
            data={"new_email": command.new_email},
            version=version
        )
        return True

# CQRS - Query Side
class UserReadModel:
    def __init__(self):
        self.users: Dict[str, Dict[str, Any]] = {}
    
    def project_event(self, event: Event):
        if event.event_type == "UserCreated":
            self.users[event.aggregate_id] = {
                "id": event.aggregate_id,
                "name": event.data["name"],
                "email": event.data["email"],
                "created_at": event.timestamp
            }
        elif event.event_type == "UserEmailUpdated":
            if event.aggregate_id in self.users:
                self.users[event.aggregate_id]["email"] = event.data["new_email"]
    
    def get_user(self, user_id: str) -> Dict[str, Any]:
        return self.users.get(user_id)
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        return list(self.users.values())

# Usage
event_store = EventStore()
command_handler = UserCommandHandler(event_store)
read_model = UserReadModel()

# Créer un utilisateur
create_command = CreateUserCommand("Alice", "<EMAIL>")
user_id = command_handler.handle(create_command)

# Mettre à jour l'email
update_command = UpdateUserEmailCommand(user_id, "<EMAIL>")
command_handler.handle(update_command)

# Projeter les événements vers le read model
for event in event_store.events:
    read_model.project_event(event)

# Requête
user = read_model.get_user(user_id)
print(f"User: {user}")
```

Ces patterns d'architecture sont essentiels pour construire des applications robustes, maintenables et évolutives au niveau expert.
