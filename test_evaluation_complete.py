#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 TEST COMPLET DE L'ÉVALUATION DE PERFORMANCE
==============================================
Test direct de la méthode evaluer_performance_predictions_corrigee
"""

import json
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def test_evaluation_complete():
    """
    🧪 TEST COMPLET DE L'ÉVALUATION
    """
    print("🧪 TEST COMPLET DE L'ÉVALUATION DE PERFORMANCE")
    print("=" * 60)
    
    # Initialiser l'analyseur
    analyseur = AnalyseurFractalBaccarat()
    
    # Charger le dataset
    print("📂 Chargement du dataset...")
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    print(f"✅ Dataset chargé: {len(parties)} parties")
    
    # Test avec un petit pourcentage pour commencer
    print(f"\n🔬 TEST AVEC 5% DES PARTIES (50 parties)")
    performance_5pct = analyseur.evaluer_performance_predictions_corrigee(dataset, pourcentage_test=0.05)
    
    print(f"📊 RÉSULTATS 5%:")
    for key, value in performance_5pct.items():
        print(f"  {key}: {value}")
    
    # Test avec 10%
    print(f"\n🔬 TEST AVEC 10% DES PARTIES (100 parties)")
    performance_10pct = analyseur.evaluer_performance_predictions_corrigee(dataset, pourcentage_test=0.10)
    
    print(f"📊 RÉSULTATS 10%:")
    for key, value in performance_10pct.items():
        print(f"  {key}: {value}")
    
    # Test avec 20%
    print(f"\n🔬 TEST AVEC 20% DES PARTIES (200 parties)")
    performance_20pct = analyseur.evaluer_performance_predictions_corrigee(dataset, pourcentage_test=0.20)
    
    print(f"📊 RÉSULTATS 20%:")
    for key, value in performance_20pct.items():
        print(f"  {key}: {value}")

def test_evaluation_detaillee():
    """
    🔍 TEST DÉTAILLÉ AVEC SUIVI ÉTAPE PAR ÉTAPE
    """
    print(f"\n🔍 TEST DÉTAILLÉ AVEC SUIVI")
    print("=" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    # Test sur 2% seulement (20 parties)
    pourcentage_test = 0.02
    nb_parties_total = len(parties)
    nb_parties_test = int(nb_parties_total * pourcentage_test)
    parties_test = parties[-nb_parties_test:]
    
    print(f"📊 Test sur {len(parties_test)} parties")
    
    predictions_evaluees = 0
    predictions_correctes = 0
    predictions_banker = 0
    predictions_player = 0
    correctes_banker = 0
    correctes_player = 0
    ties_rencontres = 0
    erreurs = 0
    indeterminees = 0
    
    for idx, partie in enumerate(parties_test):
        print(f"\n--- PARTIE {idx+1}/{len(parties_test)} ---")
        mains = partie.get('mains', [])
        print(f"Mains: {len(mains)}")
        
        if len(mains) < 10:
            print("❌ Trop courte")
            continue
            
        # Analyser quelques mains de cette partie
        mains_analysees_partie = 0
        for i in range(10, min(20, len(mains))):  # Max 10 mains par partie
            mains_analyse = mains[:i]
            main_cible = mains[i]
            resultat_reel = main_cible.get('index3_result')
            
            if not resultat_reel:
                continue
                
            if resultat_reel == 'TIE':
                ties_rencontres += 1
                continue
                
            try:
                partie_temp = {
                    'numero': 0,
                    'mains': mains_analyse
                }
                analyse = analyseur.analyser_partie_fractale(partie_temp)
                prediction = analyse.prediction_main_suivante
                
                if prediction in ['INDETERMINE', 'ALEATOIRE', 'ATTENDRE']:
                    indeterminees += 1
                    continue
                    
                predictions_evaluees += 1
                mains_analysees_partie += 1
                
                if prediction == 'BANKER':
                    predictions_banker += 1
                    if resultat_reel == 'BANKER':
                        correctes_banker += 1
                        predictions_correctes += 1
                        print(f"  ✅ BANKER correct")
                    else:
                        print(f"  ❌ BANKER incorrect (était {resultat_reel})")
                elif prediction == 'PLAYER':
                    predictions_player += 1
                    if resultat_reel == 'PLAYER':
                        correctes_player += 1
                        predictions_correctes += 1
                        print(f"  ✅ PLAYER correct")
                    else:
                        print(f"  ❌ PLAYER incorrect (était {resultat_reel})")
                elif prediction == 'TIE':
                    # TIE predictions are rare but possible
                    if resultat_reel == 'TIE':
                        predictions_correctes += 1
                        print(f"  ✅ TIE correct")
                    else:
                        print(f"  ❌ TIE incorrect (était {resultat_reel})")
                        
            except Exception as e:
                erreurs += 1
                print(f"  ❌ Erreur: {e}")
        
        print(f"  Mains analysées: {mains_analysees_partie}")
    
    # Résultats finaux
    print(f"\n📊 RÉSULTATS FINAUX")
    print("=" * 30)
    print(f"Prédictions évaluées: {predictions_evaluees}")
    print(f"Prédictions correctes: {predictions_correctes}")
    print(f"Prédictions indéterminées: {indeterminees}")
    print(f"TIE rencontrés: {ties_rencontres}")
    print(f"Erreurs: {erreurs}")
    
    if predictions_evaluees > 0:
        taux_reussite = (predictions_correctes / predictions_evaluees) * 100
        print(f"Taux de réussite: {taux_reussite:.1f}%")
        print(f"Avantage vs hasard: {taux_reussite - 50:.1f}%")
    else:
        print("❌ Aucune prédiction évaluée")
    
    print(f"\nBANKER: {correctes_banker}/{predictions_banker}")
    print(f"PLAYER: {correctes_player}/{predictions_player}")

if __name__ == "__main__":
    try:
        test_evaluation_complete()
        test_evaluation_detaillee()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
