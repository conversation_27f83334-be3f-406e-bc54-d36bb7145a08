MÉTHODES EXTRAITES DE LA CLASSE AnalyseurEvolutionEntropique
============================================================

Extraction effectuée le: 2025-06-27 23:33:47
Fichier source: analyseur_transitions_index5.py
Nombre de méthodes: 15

================================================================================
MÉTHODE: AnalyseurEvolutionEntropique.__init__
================================================================================

    def __init__(self, dataset_path: str):
        """
        Initialise l'analyseur d'évolution entropique

        Args:
            dataset_path: Chemin vers le fichier JSON des parties
        """
        self.dataset_path = dataset_path

        # CORRECTION CRITIQUE : Utiliser le générateur BCT avec règles de transition
        print("🔄 Activation du générateur BCT avec règles INDEX1/INDEX2...")
        self.generateur_signatures = GenerateurSequencesBCT()
        self.calculateur_global = None

        # Bases de signatures précompilées
        self.base_signatures_4 = {}
        self.base_signatures_5 = {}

        # Résultats d'analyse
        self.evolutions_entropiques = {}  # partie_id -> évolution complète
        self.statistiques_globales = {}   # Analyses globales des ratios

        # Configuration des seuils de classification
        self.seuils_classification = {
            'ordre_extreme': 0.2,
            'ordre_fort': 0.4,
            'ordre_modere': 0.7,
            'equilibre_min': 0.9,
            'equilibre_max': 1.1,
            'chaos_modere': 1.4,
            'chaos_fort': 2.0,
            'chaos_extreme': 3.0
        }

        print("🎯 ANALYSEUR ÉVOLUTION ENTROPIQUE INITIALISÉ")
        print("📊 Objectif: Conversion parties INDEX5 → Évolution entropique")
        print("🔬 Analyse: Ratios L4/Global et L5/Global depuis main 5")


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique.analyser_toutes_parties_entropiques
================================================================================

    def analyser_toutes_parties_entropiques(self, nb_parties_max=None):
        """
        Analyse entropique complète de toutes les parties
        OPTIMISÉE AVEC CACHE ULTRA-RAPIDE ET MULTIPROCESSING
        Génère l'évolution des ratios pour chaque main n >= 6

        Args:
            nb_parties_max: Limite du nombre de parties à analyser (None = toutes)
        """
        print(f"\n🔥 ANALYSE ÉVOLUTION ENTROPIQUE - TOUTES PARTIES")
        print("🚀 VERSION ULTRA-OPTIMISÉE AVEC CACHE + MULTIPROCESSING")
        print("=" * 70)

        # NOUVEAU : Utiliser le système de cache ultra-optimisé
        print("📂 Chargement du dataset avec cache ultra-optimisé...")

        # Créer un analyseur temporaire pour exploiter le cache
        analyseur_cache = AnalyseurTransitionsIndex5(self.dataset_path)
        dataset = analyseur_cache._charger_avec_cache_ultra_optimise()

        parties = dataset.get('parties', [])
        if nb_parties_max:
            parties = parties[:nb_parties_max]

        print(f"📊 Parties à analyser : {len(parties):,}")
        print(f"💾 Chargement optimisé avec cache : ✅")
        print(f"🖥️ Multiprocessing 8 cœurs : ✅")
        print(f"💾 RAM disponible : 28GB")

        # Initialiser les bases de signatures
        if not self.base_signatures_4 or not self.base_signatures_5:
            self.initialiser_bases_signatures()

        # NOUVEAU : Analyser avec multiprocessing 8 cœurs
        print(f"🚀 Lancement analyse multiprocessing sur {multiprocessing.cpu_count()} cœurs...")

        parties_reussies = 0
        parties_echouees = 0
        total_mains_analysees = 0

        # Diviser les parties en chunks pour multiprocessing
        chunk_size = max(1, len(parties) // (multiprocessing.cpu_count() * 4))  # 4 chunks par cœur
        chunks = [parties[i:i + chunk_size] for i in range(0, len(parties), chunk_size)]

        print(f"📊 Traitement en {len(chunks)} chunks de ~{chunk_size:,} parties chacun")

        # Traitement multiprocessing
        with multiprocessing.Pool(processes=min(8, multiprocessing.cpu_count())) as pool:
            # Préparer les arguments pour chaque chunk
            chunk_args = [(chunk, chunk_idx) for chunk_idx, chunk in enumerate(chunks)]

            # Traiter les chunks en parallèle
            resultats_chunks = pool.starmap(self._traiter_chunk_parties, chunk_args)

        # Consolider les résultats
        for chunk_resultats in resultats_chunks:
            if chunk_resultats:
                parties_reussies += chunk_resultats['parties_reussies']
                parties_echouees += chunk_resultats['parties_echouees']
                total_mains_analysees += chunk_resultats['total_mains_analysees']

                # Fusionner les évolutions entropiques
                self.evolutions_entropiques.update(chunk_resultats['evolutions_entropiques'])

        print(f"✅ Multiprocessing terminé - {parties_reussies:,} parties traitées")

        # Calculer les statistiques globales
        print("\n📊 Calcul des statistiques globales...")
        self.statistiques_globales = self._calculer_analyses_globales()

        print(f"\n✅ ANALYSE TERMINÉE")
        print(f"   Parties réussies : {parties_reussies:,}")
        print(f"   Parties échouées : {parties_echouees:,}")
        print(f"   Mains analysées : {total_mains_analysees:,}")

        return {
            'parties_reussies': parties_reussies,
            'parties_echouees': parties_echouees,
            'total_mains_analysees': total_mains_analysees,
            'evolutions_entropiques': self.evolutions_entropiques,
            'statistiques_globales': self.statistiques_globales
        }


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique.analyser_partie_entropique
================================================================================

    def analyser_partie_entropique(self, partie: dict) -> dict:
        """
        Analyse entropique complète d'une partie
        Retourne l'évolution main par main des ratios depuis la main 6

        Args:
            partie: Dictionnaire contenant les données de la partie

        Returns:
            dict: Évolution entropique complète de la partie
        """
        partie_id = partie.get('partie_number', 'unknown')
        mains = partie.get('mains', [])

        if len(mains) < 6:
            return {'erreur': f'Partie {partie_id} trop courte ({len(mains)} mains < 6)'}

        evolution_entropique = {
            'partie_id': partie_id,
            'nb_mains': len(mains),
            'mains_analysees': [],
            'statistiques_partie': {}
        }

        # NOUVEAU : Alignement main_number = index avec main dummy vide
        # Insérer une main dummy vide pour l'alignement
        main_dummy = {'index5_combined': ''}
        mains_alignees = [main_dummy] + mains

        # Extraire la séquence complète INDEX5 avec alignement
        sequence_complete = [main['index5_combined'] for main in mains_alignees]

        # DEBUG SUPPRIMÉ : Messages d'alignement trop verbeux
        # print(f"🎯 ALIGNEMENT RÉALISÉ : {len(sequence_complete)} mains (main dummy vide)")
        # print(f"   Index 0 → main dummy (VIDE), Index 1 → main 1, Index 5 → main 5")

        # Analyser chaque main depuis la main 5 (avec nouvel alignement)
        for position_main in range(5, len(mains_alignees)):

            # EXCLUSION : Ignorer l'index 0 (main dummy)
            if position_main == 0:
                continue

            # NOUVEAU : Avec alignement main_number = index
            # 1. Extraire séquence longueur 4 [main 2,3,4,5] pour analyser main 5
            seq_4 = tuple(sequence_complete[position_main-3:position_main+1])  # Ajusté pour alignement
            signature_4 = self.base_signatures_4.get(seq_4, 0.0)

            # 2. Extraire séquence longueur 5 [main 1,2,3,4,5] pour analyser main 5
            seq_5 = tuple(sequence_complete[position_main-4:position_main+1])  # Ajusté pour alignement
            signature_5 = self.base_signatures_5.get(seq_5, 0.0)

            # 3. Calculer entropie globale [main 1 à main position_main] (ignorer main 0)
            seq_globale = sequence_complete[1:position_main+1]  # Commencer à index 1 (main 1)
            entropie_globale = self._calculer_entropie_shannon(seq_globale)

            # 4. Calculer ratios avec protection division par zéro
            if entropie_globale > 0:
                ratio_4 = signature_4 / entropie_globale
                ratio_5 = signature_5 / entropie_globale
            else:
                ratio_4 = float('inf') if signature_4 > 0 else 0.0
                ratio_5 = float('inf') if signature_5 > 0 else 0.0

            # 5. Classification des ratios
            classification_4 = self._classifier_ratio(ratio_4)
            classification_5 = self._classifier_ratio(ratio_5)

            # 6. Enregistrer l'analyse de cette main
            analyse_main = {
                'position_main': position_main,
                'sequence_4': seq_4,
                'sequence_5': seq_5,
                'signature_entropie_4': signature_4,
                'signature_entropie_5': signature_5,
                'entropie_globale': entropie_globale,
                'ratio_4_global': ratio_4,
                'ratio_5_global': ratio_5,
                'index5_reel': sequence_complete[position_main-1],  # Valeur réelle à cette main
                'classification_4': classification_4,
                'classification_5': classification_5,
                'fiabilite_4': classification_4.get('fiabilite', 33),
                'fiabilite_5': classification_5.get('fiabilite', 33)
            }

            evolution_entropique['mains_analysees'].append(analyse_main)

        # Calculer statistiques de la partie
        evolution_entropique['statistiques_partie'] = self._calculer_stats_partie(
            evolution_entropique['mains_analysees']
        )

        return evolution_entropique


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._traiter_chunk_parties
================================================================================

    def _traiter_chunk_parties(self, chunk_parties, chunk_idx):
        """
        Traite un chunk de parties en parallèle (pour multiprocessing)

        Args:
            chunk_parties: Liste des parties à traiter
            chunk_idx: Index du chunk pour suivi

        Returns:
            dict: Résultats du traitement du chunk
        """
        print(f"🔄 Chunk {chunk_idx}: Traitement de {len(chunk_parties):,} parties...")

        chunk_evolutions = {}
        parties_reussies = 0
        parties_echouees = 0
        total_mains_analysees = 0

        for i, partie in enumerate(chunk_parties):
            try:
                evolution = self.analyser_partie_entropique(partie)

                if 'erreur' not in evolution:
                    partie_id = evolution['partie_id']
                    chunk_evolutions[partie_id] = evolution
                    parties_reussies += 1
                    total_mains_analysees += len(evolution['mains_analysees'])
                else:
                    parties_echouees += 1

            except Exception as e:
                print(f"❌ Chunk {chunk_idx} - Erreur partie {i+1}: {e}")
                parties_echouees += 1

        print(f"✅ Chunk {chunk_idx}: {parties_reussies:,} parties réussies, {parties_echouees:,} échouées")

        return {
            'evolutions_entropiques': chunk_evolutions,
            'parties_reussies': parties_reussies,
            'parties_echouees': parties_echouees,
            'total_mains_analysees': total_mains_analysees
        }


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._calculer_stats_partie
================================================================================

    def _calculer_stats_partie(self, mains_analysees):
        """
        Calcule les statistiques d'une partie

        Args:
            mains_analysees: Liste des analyses de mains

        Returns:
            dict: Statistiques de la partie
        """
        if not mains_analysees:
            return {}

        # Extraire les ratios valides (non infinis)
        ratios_4 = [m['ratio_4_global'] for m in mains_analysees if not math.isinf(m['ratio_4_global'])]
        ratios_5 = [m['ratio_5_global'] for m in mains_analysees if not math.isinf(m['ratio_5_global'])]

        stats = {
            'nb_mains_analysees': len(mains_analysees),
            'nb_ratios_4_valides': len(ratios_4),
            'nb_ratios_5_valides': len(ratios_5)
        }

        if ratios_4:
            stats.update({
                'ratio_4_moyen': np.mean(ratios_4),
                'ratio_4_median': np.median(ratios_4),
                'ratio_4_min': np.min(ratios_4),
                'ratio_4_max': np.max(ratios_4),
                'ecart_type_4': np.std(ratios_4)
            })

        if ratios_5:
            stats.update({
                'ratio_5_moyen': np.mean(ratios_5),
                'ratio_5_median': np.median(ratios_5),
                'ratio_5_min': np.min(ratios_5),
                'ratio_5_max': np.max(ratios_5),
                'ecart_type_5': np.std(ratios_5)
            })

        # Corrélation entre L4 et L5 si les deux existent
        if len(ratios_4) > 1 and len(ratios_5) > 1 and len(ratios_4) == len(ratios_5):
            stats['correlation_4_5'] = np.corrcoef(ratios_4, ratios_5)[0, 1]
        else:
            stats['correlation_4_5'] = 0.0

        # Analyse des classifications
        classifications_4 = [m['classification_4']['categorie'] for m in mains_analysees]
        classifications_5 = [m['classification_5']['categorie'] for m in mains_analysees]

        stats['distribution_categories_4'] = self._compter_categories(classifications_4)
        stats['distribution_categories_5'] = self._compter_categories(classifications_5)

        return stats


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._calculer_analyses_globales
================================================================================

    def _calculer_analyses_globales(self):
        """
        Calcule les analyses statistiques globales sur tous les ratios
        """
        print("📊 Calcul des analyses globales...")

        tous_ratios_4 = []
        tous_ratios_5 = []
        toutes_fiabilites_4 = []
        toutes_fiabilites_5 = []

        # Collecter tous les ratios de toutes les parties
        for evolution in self.evolutions_entropiques.values():
            for main_data in evolution['mains_analysees']:
                if not math.isinf(main_data['ratio_4_global']):
                    tous_ratios_4.append(main_data['ratio_4_global'])
                    toutes_fiabilites_4.append(main_data['fiabilite_4'])

                if not math.isinf(main_data['ratio_5_global']):
                    tous_ratios_5.append(main_data['ratio_5_global'])
                    toutes_fiabilites_5.append(main_data['fiabilite_5'])

        analyses = {
            'nb_total_ratios_4': len(tous_ratios_4),
            'nb_total_ratios_5': len(tous_ratios_5),
            'distribution_ratios_4': self._analyser_distribution(tous_ratios_4),
            'distribution_ratios_5': self._analyser_distribution(tous_ratios_5),
            'fiabilites_4': self._analyser_distribution(toutes_fiabilites_4),
            'fiabilites_5': self._analyser_distribution(toutes_fiabilites_5)
        }

        # Corrélation globale L4/L5
        if len(tous_ratios_4) > 1 and len(tous_ratios_5) > 1:
            # Prendre les ratios correspondants (même position)
            ratios_4_alignes = []
            ratios_5_alignes = []

            for evolution in self.evolutions_entropiques.values():
                for main_data in evolution['mains_analysees']:
                    if (not math.isinf(main_data['ratio_4_global']) and
                        not math.isinf(main_data['ratio_5_global'])):
                        ratios_4_alignes.append(main_data['ratio_4_global'])
                        ratios_5_alignes.append(main_data['ratio_5_global'])

            if len(ratios_4_alignes) > 1:
                analyses['correlation_globale_4_5'] = np.corrcoef(ratios_4_alignes, ratios_5_alignes)[0, 1]
            else:
                analyses['correlation_globale_4_5'] = 0.0
        else:
            analyses['correlation_globale_4_5'] = 0.0

        # Zones de prédictibilité
        analyses['zones_predictibilite'] = self._identifier_zones_predictibles()

        return analyses


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique.generer_rapport_evolution_entropique
================================================================================

    def generer_rapport_evolution_entropique(self, filename: str):
        """
        Génère le rapport détaillé avec évolution entropique
        Format: 4 colonnes par main n depuis main 5

        Args:
            filename: Nom du fichier de rapport à générer
        """
        print(f"\n📝 GÉNÉRATION RAPPORT ÉVOLUTION ENTROPIQUE")
        print("=" * 50)

        with open(filename, 'w', encoding='utf-8') as f:
            # En-tête du rapport
            f.write("RAPPORT D'ÉVOLUTION ENTROPIQUE\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Dataset analysé: {self.dataset_path}\n")
            f.write(f"Parties analysées: {len(self.evolutions_entropiques):,}\n")

            total_mains = sum(len(p['mains_analysees']) for p in self.evolutions_entropiques.values())
            f.write(f"Mains analysées: {total_mains:,} (depuis main 5)\n")
            f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Analyse partie par partie
            for partie_id, evolution in sorted(self.evolutions_entropiques.items()):
                f.write(f"PARTIE {partie_id} - {evolution['nb_mains']} mains\n")
                f.write("=" * 60 + "\n")

                # En-tête du tableau avec les 4 colonnes demandées
                f.write(f"{'MAIN':<4} {'SIG_L4':<8} {'SIG_L5':<8} {'ENT_GLOB':<8} ")
                f.write(f"{'RATIO_L4':<8} {'RATIO_L5':<8} {'CLASS_L4':<12} {'CLASS_L5':<12} {'INDEX5_REEL':<15}\n")
                f.write("-" * 95 + "\n")

                # Données main par main
                for main_data in evolution['mains_analysees']:
                    f.write(f"{main_data['position_main']:<4} ")
                    f.write(f"{main_data['signature_entropie_4']:<8.3f} ")
                    f.write(f"{main_data['signature_entropie_5']:<8.3f} ")
                    f.write(f"{main_data['entropie_globale']:<8.3f} ")

                    # Gestion des ratios infinis
                    ratio_4_str = f"{main_data['ratio_4_global']:.3f}" if not math.isinf(main_data['ratio_4_global']) else "INF"
                    ratio_5_str = f"{main_data['ratio_5_global']:.3f}" if not math.isinf(main_data['ratio_5_global']) else "INF"

                    f.write(f"{ratio_4_str:<8} ")
                    f.write(f"{ratio_5_str:<8} ")
                    f.write(f"{main_data['classification_4']['intensite']:<12} ")
                    f.write(f"{main_data['classification_5']['intensite']:<12} ")
                    f.write(f"{main_data['index5_reel']:<15}\n")

                # Statistiques de la partie
                stats = evolution['statistiques_partie']
                f.write(f"\nSTATISTIQUES PARTIE {partie_id}:\n")
                f.write("-" * 30 + "\n")

                if 'ratio_4_moyen' in stats:
                    f.write(f"Longueur 4 - Ratio moyen: {stats['ratio_4_moyen']:.3f} ")
                    f.write(f"(σ={stats['ecart_type_4']:.3f}, min={stats['ratio_4_min']:.3f}, max={stats['ratio_4_max']:.3f})\n")

                if 'ratio_5_moyen' in stats:
                    f.write(f"Longueur 5 - Ratio moyen: {stats['ratio_5_moyen']:.3f} ")
                    f.write(f"(σ={stats['ecart_type_5']:.3f}, min={stats['ratio_5_min']:.3f}, max={stats['ratio_5_max']:.3f})\n")

                if stats.get('correlation_4_5', 0) != 0:
                    f.write(f"Corrélation L4/L5: {stats['correlation_4_5']:.3f}\n")

                # Distribution des catégories
                f.write(f"Distribution L4: {stats.get('distribution_categories_4', {})}\n")
                f.write(f"Distribution L5: {stats.get('distribution_categories_5', {})}\n\n")

            # Analyses globales
            self._ecrire_analyses_globales(f)

        print(f"✅ Rapport généré: {filename}")
        return filename


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._ecrire_analyses_globales
================================================================================

    def _ecrire_analyses_globales(self, f):
        """Écrit les analyses globales dans le rapport"""
        f.write("ANALYSES GLOBALES\n")
        f.write("=" * 30 + "\n\n")

        stats = self.statistiques_globales

        # Statistiques générales
        f.write("STATISTIQUES GÉNÉRALES:\n")
        f.write("-" * 25 + "\n")
        f.write(f"Total ratios L4 analysés: {stats['nb_total_ratios_4']:,}\n")
        f.write(f"Total ratios L5 analysés: {stats['nb_total_ratios_5']:,}\n")
        f.write(f"Corrélation globale L4/L5: {stats['correlation_globale_4_5']:.3f}\n\n")

        # Distribution des ratios L4
        if 'distribution_ratios_4' in stats and 'erreur' not in stats['distribution_ratios_4']:
            dist_4 = stats['distribution_ratios_4']
            f.write("DISTRIBUTION RATIOS LONGUEUR 4:\n")
            f.write("-" * 35 + "\n")
            f.write(f"Moyenne: {dist_4['moyenne']:.3f}\n")
            f.write(f"Médiane: {dist_4['mediane']:.3f}\n")
            f.write(f"Écart-type: {dist_4['ecart_type']:.3f}\n")
            f.write(f"Min: {dist_4['min']:.3f}, Max: {dist_4['max']:.3f}\n")
            f.write(f"Q1: {dist_4['percentile_25']:.3f}, Q3: {dist_4['percentile_75']:.3f}\n\n")

        # Distribution des ratios L5
        if 'distribution_ratios_5' in stats and 'erreur' not in stats['distribution_ratios_5']:
            dist_5 = stats['distribution_ratios_5']
            f.write("DISTRIBUTION RATIOS LONGUEUR 5:\n")
            f.write("-" * 35 + "\n")
            f.write(f"Moyenne: {dist_5['moyenne']:.3f}\n")
            f.write(f"Médiane: {dist_5['mediane']:.3f}\n")
            f.write(f"Écart-type: {dist_5['ecart_type']:.3f}\n")
            f.write(f"Min: {dist_5['min']:.3f}, Max: {dist_5['max']:.3f}\n")
            f.write(f"Q1: {dist_5['percentile_25']:.3f}, Q3: {dist_5['percentile_75']:.3f}\n\n")

        # Zones de prédictibilité
        zones = stats.get('zones_predictibilite', {})
        f.write("ZONES DE HAUTE PRÉDICTIBILITÉ:\n")
        f.write("-" * 35 + "\n")
        f.write(f"Haute fiabilité L4 (≥80%): {len(zones.get('haute_fiabilite_4', []))} occurrences\n")
        f.write(f"Haute fiabilité L5 (≥80%): {len(zones.get('haute_fiabilite_5', []))} occurrences\n")
        f.write(f"Équilibre parfait (ratio≈1): {len(zones.get('equilibre_parfait', []))} occurrences\n\n")

        # Recommandations
        f.write("RECOMMANDATIONS:\n")
        f.write("-" * 20 + "\n")

        if stats['correlation_globale_4_5'] > 0.7:
            f.write("✅ Forte corrélation L4/L5 → Les deux longueurs sont cohérentes\n")
        elif stats['correlation_globale_4_5'] < 0.3:
            f.write("⚠️ Faible corrélation L4/L5 → Comportements différents selon la longueur\n")

        nb_zones_fiables = len(zones.get('haute_fiabilite_4', [])) + len(zones.get('haute_fiabilite_5', []))
        if nb_zones_fiables > stats['nb_total_ratios_4'] * 0.1:
            f.write("🎯 Nombreuses zones de haute fiabilité → Système prédictible\n")
        else:
            f.write("⚠️ Peu de zones de haute fiabilité → Système complexe\n")


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._generer_signatures_integrees
================================================================================

    def _generer_signatures_integrees(self, longueur):
        """
        Méthode de fallback pour générer les signatures entropiques
        Utilisée si les modules spécialisés ne sont pas disponibles
        """
        print(f"🔄 Génération signatures intégrées longueur {longueur}...")

        # Générer toutes les séquences possibles de la longueur donnée
        index1_values = ['0', '1']
        index2_values = ['A', 'B', 'C']
        index3_values = ['BANKER', 'PLAYER', 'TIE']

        # Créer toutes les valeurs INDEX5 possibles
        toutes_valeurs_index5 = []
        for i1 in index1_values:
            for i2 in index2_values:
                for i3 in index3_values:
                    toutes_valeurs_index5.append(f"{i1}_{i2}_{i3}")

        signatures = {}

        # Générer toutes les séquences possibles de manière itérative (plus efficace)
        from itertools import product

        # Générer toutes les combinaisons possibles
        for sequence in product(toutes_valeurs_index5, repeat=longueur):
            # Calculer la signature entropique de cette séquence
            signature = self._calculer_entropie_shannon(list(sequence))
            # S'assurer que la signature est un nombre, pas un dictionnaire
            if isinstance(signature, dict):
                signature = signature.get('entropie', 0.0)
            signatures[sequence] = float(signature)

        print(f"✅ {len(signatures):,} signatures générées pour longueur {longueur}")
        return signatures



================================================================================
MÉTHODE: AnalyseurEvolutionEntropique.initialiser_bases_signatures
================================================================================

    def initialiser_bases_signatures(self):
        """
        Charge ou génère les bases de signatures entropiques pour L4 et L5
        """
        print("\n🔥 CHARGEMENT BASES SIGNATURES ENTROPIQUES")
        print("=" * 50)

        if self.generateur_signatures:
            # Utiliser le module spécialisé
            print("📊 Chargement signatures longueur 4...")
            self.base_signatures_4 = self.generateur_signatures.generer_toutes_sequences_longueur_4()

            print("📊 Chargement signatures longueur 5...")
            self.base_signatures_5 = self.generateur_signatures.generer_toutes_sequences_longueur_5()
        else:
            # Méthodes intégrées de fallback
            print("📊 Génération signatures intégrées...")
            self.base_signatures_4 = self._generer_signatures_integrees(4)
            self.base_signatures_5 = self._generer_signatures_integrees(5)

        print(f"✅ Bases signatures chargées :")
        print(f"   Longueur 4 : {len(self.base_signatures_4):,} séquences")
        print(f"   Longueur 5 : {len(self.base_signatures_5):,} séquences")

        return True


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._calculer_entropie_shannon
================================================================================

    def _calculer_entropie_shannon(self, sequence):
        """
        Calcule l'entropie de Shannon d'une séquence

        Args:
            sequence: Liste des valeurs INDEX5

        Returns:
            float: Entropie de Shannon en bits
        """
        if not sequence:
            return 0.0

        # Compter les occurrences
        compteurs = {}
        for valeur in sequence:
            compteurs[valeur] = compteurs.get(valeur, 0) + 1

        # Calculer l'entropie
        n = len(sequence)
        entropie = 0.0

        for count in compteurs.values():
            if count > 0:
                p = count / n
                entropie -= p * np.log2(p)

        return entropie


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._classifier_ratio
================================================================================

    def _classifier_ratio(self, ratio):
        """
        Classifie un ratio entropique selon les seuils définis

        Args:
            ratio: Ratio entropie_locale / entropie_globale

        Returns:
            dict: Classification complète du ratio
        """
        if math.isinf(ratio):
            return {
                'categorie': 'INVALIDE',
                'intensite': 'N/A',
                'tendance': 'INDETERMINEE',
                'fiabilite': 0,
                'action': 'IGNORER'
            }

        seuils = self.seuils_classification

        if ratio < seuils['ordre_extreme']:
            return {
                'categorie': 'ORDRE_EXTREME',
                'intensite': '++++',
                'tendance': 'VERS_PLUS_DE_DÉSORDRE',
                'fiabilite': min(95, (seuils['ordre_extreme'] - ratio) * 300 + 80),
                'action': 'PRÉDIRE_AVEC_CONFIANCE'
            }
        elif ratio < seuils['ordre_fort']:
            return {
                'categorie': 'ORDRE_FORT',
                'intensite': '+++',
                'tendance': 'VERS_PLUS_DE_DÉSORDRE',
                'fiabilite': min(90, (seuils['ordre_fort'] - ratio) * 200 + 70),
                'action': 'PRÉDIRE_AVEC_CONFIANCE'
            }
        elif ratio < seuils['ordre_modere']:
            return {
                'categorie': 'ORDRE_MODÉRÉ',
                'intensite': '++',
                'tendance': 'VERS_PLUS_DE_DÉSORDRE',
                'fiabilite': min(80, (seuils['ordre_modere'] - ratio) * 100 + 60),
                'action': 'PRÉDIRE_AVEC_PRUDENCE'
            }
        elif ratio < seuils['equilibre_min']:
            return {
                'categorie': 'ORDRE_LÉGER',
                'intensite': '+',
                'tendance': 'VERS_PLUS_DE_DÉSORDRE',
                'fiabilite': min(70, (seuils['equilibre_min'] - ratio) * 150 + 50),
                'action': 'SURVEILLER'
            }
        elif ratio <= seuils['equilibre_max']:
            return {
                'categorie': 'ÉQUILIBRE_PARFAIT',
                'intensite': '=',
                'tendance': 'MAINTIEN_ÉQUILIBRE',
                'fiabilite': 33,  # Équivalent au hasard
                'action': 'NE_PAS_PRÉDIRE'
            }
        elif ratio < seuils['chaos_modere']:
            return {
                'categorie': 'CHAOS_LÉGER',
                'intensite': '-',
                'tendance': 'VERS_MOINS_DE_DÉSORDRE',
                'fiabilite': min(70, (ratio - seuils['equilibre_max']) * 150 + 50),
                'action': 'SURVEILLER'
            }
        elif ratio < seuils['chaos_fort']:
            return {
                'categorie': 'CHAOS_MODÉRÉ',
                'intensite': '--',
                'tendance': 'VERS_MOINS_DE_DÉSORDRE',
                'fiabilite': min(80, (ratio - seuils['chaos_modere']) * 100 + 60),
                'action': 'PRÉDIRE_AVEC_PRUDENCE'
            }
        elif ratio < seuils['chaos_extreme']:
            return {
                'categorie': 'CHAOS_FORT',
                'intensite': '---',
                'tendance': 'VERS_MOINS_DE_DÉSORDRE',
                'fiabilite': min(90, (ratio - seuils['chaos_fort']) * 150 + 70),
                'action': 'PRÉDIRE_AVEC_CONFIANCE'
            }
        else:
            return {
                'categorie': 'CHAOS_EXTREME',
                'intensite': '----',
                'tendance': 'VERS_MOINS_DE_DÉSORDRE',
                'fiabilite': min(95, (ratio - seuils['chaos_extreme']) * 100 + 80),
                'action': 'PRÉDIRE_AVEC_CONFIANCE'
            }


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._compter_categories
================================================================================

    def _compter_categories(self, classifications):
        """Compte les occurrences de chaque catégorie"""
        compteurs = {}
        for cat in classifications:
            compteurs[cat] = compteurs.get(cat, 0) + 1
        return compteurs


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._analyser_distribution
================================================================================

    def _analyser_distribution(self, valeurs):
        """Analyse la distribution d'une liste de valeurs"""
        if not valeurs:
            return {'erreur': 'Aucune valeur'}

        return {
            'count': len(valeurs),
            'moyenne': np.mean(valeurs),
            'mediane': np.median(valeurs),
            'min': np.min(valeurs),
            'max': np.max(valeurs),
            'ecart_type': np.std(valeurs),
            'percentile_25': np.percentile(valeurs, 25),
            'percentile_75': np.percentile(valeurs, 75)
        }


================================================================================
MÉTHODE: AnalyseurEvolutionEntropique._identifier_zones_predictibles
================================================================================

    def _identifier_zones_predictibles(self):
        """Identifie les zones de haute prédictibilité"""
        zones = {
            'haute_fiabilite_4': [],
            'haute_fiabilite_5': [],
            'equilibre_parfait': [],
            'chaos_extreme': []
        }

        for partie_id, evolution in self.evolutions_entropiques.items():
            for main_data in evolution['mains_analysees']:
                # Haute fiabilité L4
                if main_data['fiabilite_4'] >= 80:
                    zones['haute_fiabilite_4'].append({
                        'partie_id': partie_id,
                        'position_main': main_data['position_main'],
                        'ratio': main_data['ratio_4_global'],
                        'fiabilite': main_data['fiabilite_4']
                    })

                # Haute fiabilité L5
                if main_data['fiabilite_5'] >= 80:
                    zones['haute_fiabilite_5'].append({
                        'partie_id': partie_id,
                        'position_main': main_data['position_main'],
                        'ratio': main_data['ratio_5_global'],
                        'fiabilite': main_data['fiabilite_5']
                    })

                # Équilibre parfait (ratio ≈ 1)
                if (0.95 <= main_data['ratio_4_global'] <= 1.05 or
                    0.95 <= main_data['ratio_5_global'] <= 1.05):
                    zones['equilibre_parfait'].append({
                        'partie_id': partie_id,
                        'position_main': main_data['position_main'],
                        'ratio_4': main_data['ratio_4_global'],
                        'ratio_5': main_data['ratio_5_global']
                    })

        return zones


