# Patterns Modernes Python 2024

## 🚀 Programmation Asynchrone Avancée

### Patterns Async/Await Modernes
```python
import asyncio
import aiohttp
import aiofiles
from typing import List, Dict, Any, Optional, AsyncGenerator
from contextlib import asynccontextmanager
from dataclasses import dataclass
from datetime import datetime
import time

@dataclass
class TaskResult:
    """Résultat d'une tâche asynchrone."""
    task_id: str
    result: Any
    duration: float
    success: bool
    error: Optional[str] = None

class AsyncTaskManager:
    """Gestionnaire de tâches asynchrones avec limitation de concurrence."""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.results: List[TaskResult] = []
    
    async def execute_task(self, task_id: str, coro) -> TaskResult:
        """Exécuter une tâche avec limitation de concurrence."""
        async with self.semaphore:
            start_time = time.time()
            try:
                result = await coro
                duration = time.time() - start_time
                task_result = TaskResult(
                    task_id=task_id,
                    result=result,
                    duration=duration,
                    success=True
                )
            except Exception as e:
                duration = time.time() - start_time
                task_result = TaskResult(
                    task_id=task_id,
                    result=None,
                    duration=duration,
                    success=False,
                    error=str(e)
                )
            
            self.results.append(task_result)
            return task_result
    
    async def execute_batch(self, tasks: Dict[str, Any]) -> List[TaskResult]:
        """Exécuter un lot de tâches en parallèle."""
        coroutines = [
            self.execute_task(task_id, coro) 
            for task_id, coro in tasks.items()
        ]
        return await asyncio.gather(*coroutines, return_exceptions=True)

# Pattern Producer/Consumer avec asyncio
class AsyncQueue:
    """Queue asynchrone avec pattern producer/consumer."""
    
    def __init__(self, maxsize: int = 100):
        self.queue = asyncio.Queue(maxsize=maxsize)
        self.producers_done = False
        self.consumers: List[asyncio.Task] = []
    
    async def producer(self, items: List[Any]):
        """Producteur d'éléments."""
        for item in items:
            await self.queue.put(item)
        
        # Signal de fin de production
        await self.queue.put(None)
        self.producers_done = True
    
    async def consumer(self, consumer_id: str, processor_func):
        """Consommateur d'éléments."""
        while True:
            item = await self.queue.get()
            
            if item is None:
                # Signal de fin
                await self.queue.put(None)  # Propager aux autres consumers
                break
            
            try:
                await processor_func(consumer_id, item)
            except Exception as e:
                print(f"Consumer {consumer_id} error: {e}")
            finally:
                self.queue.task_done()
    
    async def start_consumers(self, num_consumers: int, processor_func):
        """Démarrer les consommateurs."""
        self.consumers = [
            asyncio.create_task(self.consumer(f"consumer-{i}", processor_func))
            for i in range(num_consumers)
        ]
    
    async def wait_completion(self):
        """Attendre la fin de tous les consommateurs."""
        await asyncio.gather(*self.consumers)

# Streaming de données asynchrone
async def async_file_reader(file_path: str, chunk_size: int = 8192) -> AsyncGenerator[str, None]:
    """Lecteur de fichier asynchrone par chunks."""
    async with aiofiles.open(file_path, 'r', encoding='utf-8') as file:
        while True:
            chunk = await file.read(chunk_size)
            if not chunk:
                break
            yield chunk

async def async_http_stream(url: str, chunk_size: int = 1024) -> AsyncGenerator[bytes, None]:
    """Stream HTTP asynchrone."""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            async for chunk in response.content.iter_chunked(chunk_size):
                yield chunk

# Context Manager asynchrone
@asynccontextmanager
async def async_database_transaction():
    """Context manager pour transaction de base de données."""
    transaction = await database.begin()
    try:
        yield transaction
        await transaction.commit()
    except Exception:
        await transaction.rollback()
        raise
    finally:
        await transaction.close()

# Exemple d'utilisation complète
class DataProcessor:
    """Processeur de données asynchrone."""
    
    def __init__(self):
        self.task_manager = AsyncTaskManager(max_concurrent_tasks=5)
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Entrée du context manager."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Sortie du context manager."""
        if self.session:
            await self.session.close()
    
    async def fetch_data(self, url: str) -> Dict[str, Any]:
        """Récupérer des données depuis une URL."""
        async with self.session.get(url) as response:
            return await response.json()
    
    async def process_urls(self, urls: List[str]) -> List[TaskResult]:
        """Traiter une liste d'URLs en parallèle."""
        tasks = {
            f"url-{i}": self.fetch_data(url) 
            for i, url in enumerate(urls)
        }
        return await self.task_manager.execute_batch(tasks)

# Utilisation
async def main():
    urls = [
        "https://api.example.com/data/1",
        "https://api.example.com/data/2",
        "https://api.example.com/data/3"
    ]
    
    async with DataProcessor() as processor:
        results = await processor.process_urls(urls)
        
        for result in results:
            if result.success:
                print(f"Task {result.task_id}: Success in {result.duration:.2f}s")
            else:
                print(f"Task {result.task_id}: Failed - {result.error}")
```

## 🏗️ Dataclasses et Type Hints Avancés

### Dataclasses Modernes avec Validation
```python
from dataclasses import dataclass, field, InitVar
from typing import List, Dict, Optional, Union, Literal, TypeVar, Generic
from datetime import datetime
from enum import Enum, auto
import json

class Status(Enum):
    """Statut avec auto-génération."""
    PENDING = auto()
    PROCESSING = auto()
    COMPLETED = auto()
    FAILED = auto()

@dataclass(frozen=True, slots=True)  # Immutable et optimisé mémoire
class User:
    """Utilisateur avec dataclass moderne."""
    id: int
    email: str
    username: str
    created_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Union[str, int, bool]] = field(default_factory=dict)
    
    # Champ calculé
    display_name: str = field(init=False)
    
    def __post_init__(self):
        """Post-traitement après initialisation."""
        # Utiliser object.__setattr__ car la classe est frozen
        object.__setattr__(
            self, 
            'display_name', 
            f"{self.username} ({self.email})"
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir en dictionnaire."""
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active,
            'tags': self.tags.copy(),
            'metadata': self.metadata.copy(),
            'display_name': self.display_name
        }

@dataclass
class Task:
    """Tâche avec validation et transformation."""
    title: str
    description: str
    status: Status = Status.PENDING
    priority: Literal[1, 2, 3, 4, 5] = 3
    assigned_to: Optional[int] = None
    due_date: Optional[datetime] = None
    
    # InitVar pour paramètres d'initialisation uniquement
    validate_dates: InitVar[bool] = True
    
    # Champs calculés
    is_overdue: bool = field(init=False)
    days_remaining: Optional[int] = field(init=False)
    
    def __post_init__(self, validate_dates: bool):
        """Validation et calculs post-initialisation."""
        if validate_dates and self.due_date:
            if self.due_date <= datetime.utcnow():
                raise ValueError("Due date must be in the future")
        
        # Calculer les champs dérivés
        self.is_overdue = (
            self.due_date is not None and 
            self.due_date < datetime.utcnow() and 
            self.status != Status.COMPLETED
        )
        
        self.days_remaining = (
            (self.due_date - datetime.utcnow()).days 
            if self.due_date else None
        )

# Dataclass générique
T = TypeVar('T')

@dataclass
class Result(Generic[T]):
    """Résultat générique avec gestion d'erreurs."""
    success: bool
    data: Optional[T] = None
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    @classmethod
    def success_result(cls, data: T) -> 'Result[T]':
        """Créer un résultat de succès."""
        return cls(success=True, data=data)
    
    @classmethod
    def error_result(cls, error: str) -> 'Result[T]':
        """Créer un résultat d'erreur."""
        return cls(success=False, error=error)
    
    def unwrap(self) -> T:
        """Extraire la donnée ou lever une exception."""
        if not self.success:
            raise ValueError(f"Result is not successful: {self.error}")
        return self.data

# Dataclass avec factory complexe
@dataclass
class Configuration:
    """Configuration avec factory personnalisé."""
    database_url: str
    redis_url: str
    debug: bool = False
    log_level: str = "INFO"
    features: Dict[str, bool] = field(default_factory=lambda: {
        "feature_a": True,
        "feature_b": False,
        "feature_c": True
    })
    
    @classmethod
    def from_env(cls) -> 'Configuration':
        """Créer depuis les variables d'environnement."""
        import os
        return cls(
            database_url=os.getenv("DATABASE_URL", "sqlite:///app.db"),
            redis_url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO")
        )
    
    @classmethod
    def from_file(cls, file_path: str) -> 'Configuration':
        """Créer depuis un fichier JSON."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        return cls(**data)
```

## 🔧 Context Managers Avancés

### Context Managers Personnalisés
```python
from contextlib import contextmanager, asynccontextmanager, ExitStack
from typing import Generator, Any, Optional
import time
import logging
import tempfile
import os

@contextmanager
def timer(operation_name: str) -> Generator[Dict[str, Any], None, None]:
    """Context manager pour mesurer le temps d'exécution."""
    start_time = time.time()
    context = {"operation": operation_name, "start_time": start_time}
    
    try:
        yield context
    finally:
        end_time = time.time()
        duration = end_time - start_time
        context["end_time"] = end_time
        context["duration"] = duration
        
        logging.info(f"{operation_name} completed in {duration:.2f}s")

@contextmanager
def temporary_directory(cleanup: bool = True) -> Generator[str, None, None]:
    """Context manager pour répertoire temporaire."""
    temp_dir = tempfile.mkdtemp()
    try:
        yield temp_dir
    finally:
        if cleanup:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)

@contextmanager
def database_transaction(connection):
    """Context manager pour transaction de base de données."""
    transaction = connection.begin()
    try:
        yield transaction
        transaction.commit()
    except Exception:
        transaction.rollback()
        raise
    finally:
        transaction.close()

class ResourceManager:
    """Gestionnaire de ressources avec context manager."""
    
    def __init__(self, resource_name: str):
        self.resource_name = resource_name
        self.resource = None
        self.acquired = False
    
    def __enter__(self):
        """Acquérir la ressource."""
        print(f"Acquiring resource: {self.resource_name}")
        self.resource = f"resource_{self.resource_name}"
        self.acquired = True
        return self.resource
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Libérer la ressource."""
        if self.acquired:
            print(f"Releasing resource: {self.resource_name}")
            self.resource = None
            self.acquired = False
        
        # Gérer les exceptions
        if exc_type is not None:
            print(f"Exception occurred: {exc_type.__name__}: {exc_val}")
            # Retourner False pour propager l'exception
            return False

# Context manager avec ExitStack pour ressources multiples
def process_multiple_files(file_paths: List[str]):
    """Traiter plusieurs fichiers avec ExitStack."""
    with ExitStack() as stack:
        files = [
            stack.enter_context(open(path, 'r')) 
            for path in file_paths
        ]
        
        # Traitement des fichiers
        for i, file in enumerate(files):
            print(f"Processing file {i+1}: {file.name}")
            content = file.read()
            # Traitement du contenu...

# Exemple d'utilisation combinée
def complex_operation():
    """Opération complexe avec multiples context managers."""
    
    with timer("complex_operation"):
        with temporary_directory() as temp_dir:
            with ResourceManager("database") as db_resource:
                # Simulation d'une opération complexe
                temp_file = os.path.join(temp_dir, "temp_data.txt")
                
                with open(temp_file, 'w') as f:
                    f.write("Temporary data processing...")
                
                print(f"Using database resource: {db_resource}")
                print(f"Temporary file created: {temp_file}")
                
                # Simulation de traitement
                time.sleep(0.1)
                
                return "Operation completed successfully"

# Context manager pour retry avec backoff
@contextmanager
def retry_context(max_attempts: int = 3, delay: float = 1.0):
    """Context manager pour retry automatique."""
    for attempt in range(max_attempts):
        try:
            yield attempt + 1
            break  # Succès, sortir de la boucle
        except Exception as e:
            if attempt == max_attempts - 1:
                # Dernière tentative, re-lever l'exception
                raise
            
            print(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
            time.sleep(delay)
            delay *= 2  # Backoff exponentiel

# Utilisation du retry context
def unreliable_operation():
    """Opération qui peut échouer."""
    import random
    if random.random() < 0.7:  # 70% de chance d'échouer
        raise Exception("Random failure")
    return "Success!"

def main_with_retry():
    """Exemple d'utilisation du retry context."""
    with retry_context(max_attempts=5, delay=0.5):
        result = unreliable_operation()
        print(f"Operation result: {result}")
```

Ces patterns modernes permettent d'écrire du code Python plus robuste, maintenable et performant en utilisant les dernières fonctionnalités du langage.
