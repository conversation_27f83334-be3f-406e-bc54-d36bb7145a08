RAPPORT COMPLET DES 25 MÉTRIQUES DIFF_X
================================================================================

Analyse effectuée sur 500 mains
Date: 2025-06-26 07:44:28

ARCHITECTURE DIFF GÉNÉRALISÉE
----------------------------------------
Pour chaque métrique F et chaque main n ≥ 5 :
1. seq_L4 = [index5_{n-3}, index5_{n-2}, index5_{n-1}, index5_n]
2. seq_L5 = [index5_{n-4}, index5_{n-3}, index5_{n-2}, index5_{n-1}, index5_n]
3. seq_globale = [index5_1, index5_2, ..., index5_n]
4. signature_L4 = F(seq_L4)
5. signature_L5 = F(seq_L5)
6. signature_globale = F(seq_globale)
7. ratio_L4 = signature_L4 / signature_globale
8. ratio_L5 = signature_L5 / signature_globale
9. DIFF_F = |ratio_L4 - ratio_L5|

RÉSUMÉ DES 25 MÉTRIQUES DIFF_X
----------------------------------------

DIFF_BERNOULLI:
  Description: DIFF basé sur entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)
  Valeurs calculées: 500
  Min: 0.609840
  Max: 1.000000
  Moyenne: 0.932156

DIFF_UNIFORM:
  Description: DIFF basé sur entropie uniforme H(uniform) = log₂(n)
  Valeurs calculées: 500
  Min: 0.000000
  Max: 3.584963
  Moyenne: 2.154581

DIFF_JOINT:
  Description: DIFF basé sur entropie jointe H(X,Y)
  Valeurs calculées: 500
  Min: 1.190924
  Max: 1.985549
  Moyenne: 1.694935

DIFF_MARKOV:
  Description: DIFF basé sur entropie de Markov H(Ξ) = -∑ μ(x) P_{xy} log₂(P_{xy})
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.000000
  Moyenne: 0.751211

DIFF_METRIC:
  Description: DIFF basé sur entropie métrique h_μ(T)
  Valeurs calculées: 500
  Min: 0.135000
  Max: 0.756000
  Moyenne: 0.366800

DIFF_BERNOULLI_SHIFT:
  Description: DIFF basé sur entropie du décalage de Bernoulli B(p)
  Valeurs calculées: 500
  Min: 0.609840
  Max: 1.000000
  Moyenne: 0.932156

DIFF_BSC:
  Description: DIFF basé sur capacité canal binaire symétrique κ = 1 - h(α)
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.000000
  Moyenne: 0.244672

DIFF_ERASURE:
  Description: DIFF basé sur capacité canal effaceur κ = 1 - α
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.000000
  Moyenne: 0.460000

DIFF_HUFFMAN:
  Description: DIFF basé sur efficacité de Huffman
  Valeurs calculées: 500
  Min: 0.503258
  Max: 1.000000
  Moyenne: 0.911279

DIFF_INVERSE:
  Description: DIFF basé sur métriques d'entropie inverse (ordre vs chaos)
  Valeurs calculées: 500
  Min: 1.000000
  Max: 1.639774
  Moyenne: 1.082570

DIFF_STD:
  Description: DIFF basé sur écart-type d'entropie
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.494975
  Moyenne: 0.197990

DIFF_LOG_PRED:
  Description: DIFF basé sur formule prédiction logarithmique P(S) = 0.45 + 0.35*log(DIFF+0.01)
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.330128
  Moyenne: 0.071182

DIFF_AEP:
  Description: DIFF basé sur théorème d'équipartition asymptotique
  Valeurs calculées: 500
  Min: 0.251539
  Max: 2.736966
  Moyenne: 1.039972

DIFF_JENSEN:
  Description: DIFF basé sur inégalité de Jensen f(E[X]) ≤ E[f(X)]
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.223534
  Moyenne: 0.041045

DIFF_LOG_SUM:
  Description: DIFF basé sur inégalité log-sum
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.447067
  Moyenne: 0.082090

DIFF_CONCAVITY:
  Description: DIFF basé sur concavité entropie H(λp₁+(1-λ)p₂) ≥ λH(p₁)+(1-λ)H(p₂)
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.397313
  Moyenne: 0.084689

DIFF_SMB:
  Description: DIFF basé sur théorème Shannon-McMillan-Breiman
  Valeurs calculées: 500
  Min: 0.254099
  Max: 2.821928
  Moyenne: 1.139009

DIFF_ERGODIC:
  Description: DIFF basé sur estimation entropie ergodique
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.000000
  Moyenne: 0.751211

DIFF_CHANNEL:
  Description: DIFF basé sur théorème codage de canal
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.800000
  Moyenne: 0.226000

DIFF_ERROR:
  Description: DIFF basé sur borne probabilité d'erreur
  Valeurs calculées: 500
  Min: 0.003906
  Max: 1.000000
  Moyenne: 0.416972

DIFF_SPHERE:
  Description: DIFF basé sur borne de sphère (sphere packing)
  Valeurs calculées: 500
  Min: 2.540568
  Max: 10.000000
  Moyenne: 7.089878

DIFF_COMPREHENSIVE:
  Description: DIFF basé sur analyse complète d'entropie multi-échelle
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.000000
  Moyenne: 0.980000

DIFF_RELATIVE:
  Description: DIFF basé sur entropie relative (divergence KL) D(p||q) = ∑ p(x) log₂(p(x)/q(x))
  Valeurs calculées: 500
  Min: 0.000000
  Max: 1.909856
  Moyenne: 0.352204

DIFF_COND_MI:
  Description: DIFF basé sur information mutuelle conditionnelle I(X;Y|Z) = H(X|Z) - H(X|Y,Z)
  Valeurs calculées: 500
  Min: 0.000000
  Max: 0.380168
  Moyenne: 0.065058

DIFF_TYPICAL:
  Description: DIFF basé sur ensemble typique T(n,ε) et bornes asymptotiques
  Valeurs calculées: 500
  Min: 1.500000
  Max: 8.400000
  Moyenne: 5.110000

MÉTRIQUES RÉUSSIES: 25/25


ANALYSE DÉTAILLÉE PAR MÉTRIQUE
================================================================================

DIFF_BERNOULLI
--------------
Description: DIFF basé sur entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)

ANALYSE PAR TRANCHES:
  {'tranche': 'SIGNAL_ACCEPTABLE', 'min': 0.5, 'max': 0.7, 'count': 10, 'patterns_s': 6, 'patterns_o': 4, 'pourcentage_s': 60.0}
  {'tranche': 'SIGNAL_RISQUÉ', 'min': 0.7, 'max': 0.9, 'count': 115, 'patterns_s': 57, 'patterns_o': 58, 'pourcentage_s': 49.56521739130435}
  {'tranche': 'SIGNAL_DOUTEUX', 'min': 0.9, 'max': 1.0, 'count': 362, 'patterns_s': 180, 'patterns_o': 182, 'pourcentage_s': 49.72375690607735}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_UNIFORM
------------
Description: DIFF basé sur entropie uniforme H(uniform) = log₂(n)

ANALYSE PAR TRANCHES:
  {'tranche': 'FAIBLE_DIVERSITÉ', 'min': 0.0, 'max': 1.0, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}
  {'tranche': 'DIVERSITÉ_MODÉRÉE', 'min': 1.0, 'max': 2.0, 'count': 130, 'patterns_s': 65, 'patterns_o': 65, 'pourcentage_s': 50.0}
  {'tranche': 'FORTE_DIVERSITÉ', 'min': 2.0, 'max': 3.0, 'count': 270, 'patterns_s': 135, 'patterns_o': 135, 'pourcentage_s': 50.0}
  {'tranche': 'TRÈS_FORTE_DIVERSITÉ', 'min': 3.0, 'max': 4.0, 'count': 90, 'patterns_s': 45, 'patterns_o': 45, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_JOINT
----------
Description: DIFF basé sur entropie jointe H(X,Y)

ANALYSE PAR TRANCHES:
  {'tranche': 'FORT_COUPLAGE', 'min': 1.0, 'max': 1.5, 'count': 89, 'patterns_s': 48, 'patterns_o': 41, 'pourcentage_s': 53.93258426966292}
  {'tranche': 'TRÈS_FORT_COUPLAGE', 'min': 1.5, 'max': 2.0, 'count': 411, 'patterns_s': 202, 'patterns_o': 209, 'pourcentage_s': 49.148418491484186}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_MARKOV
-----------
Description: DIFF basé sur entropie de Markov H(Ξ) = -∑ μ(x) P_{xy} log₂(P_{xy})

ANALYSE PAR TRANCHES:
  {'tranche': 'MARKOV_STABLE', 'min': 0.0, 'max': 0.2, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}
  {'tranche': 'MARKOV_MODÉRÉ', 'min': 0.2, 'max': 0.5, 'count': 130, 'patterns_s': 65, 'patterns_o': 65, 'pourcentage_s': 50.0}
  {'tranche': 'MARKOV_INSTABLE', 'min': 0.5, 'max': 0.8, 'count': 110, 'patterns_s': 55, 'patterns_o': 55, 'pourcentage_s': 50.0}
  {'tranche': 'MARKOV_CHAOTIQUE', 'min': 0.8, 'max': 1.0, 'count': 201, 'patterns_s': 100, 'patterns_o': 101, 'pourcentage_s': 49.75124378109453}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_METRIC
-----------
Description: DIFF basé sur entropie métrique h_μ(T)

ANALYSE PAR TRANCHES:
  {'tranche': 'SYSTÈME_STABLE', 'min': 0.1, 'max': 0.3, 'count': 200, 'patterns_s': 98, 'patterns_o': 102, 'pourcentage_s': 49.0}
  {'tranche': 'SYSTÈME_MIXTE', 'min': 0.3, 'max': 0.6, 'count': 254, 'patterns_s': 128, 'patterns_o': 126, 'pourcentage_s': 50.39370078740157}
  {'tranche': 'SYSTÈME_CHAOTIQUE', 'min': 0.6, 'max': 1.0, 'count': 46, 'patterns_s': 24, 'patterns_o': 22, 'pourcentage_s': 52.17391304347826}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_BERNOULLI_SHIFT
--------------------
Description: DIFF basé sur entropie du décalage de Bernoulli B(p)

ANALYSE PAR TRANCHES:
  {'tranche': 'SHIFT_CHAOTIQUE', 'min': 0.6, 'max': 1.0, 'count': 487, 'patterns_s': 243, 'patterns_o': 244, 'pourcentage_s': 49.89733059548255}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_BSC
--------
Description: DIFF basé sur capacité canal binaire symétrique κ = 1 - h(α)

ANALYSE PAR TRANCHES:
  {'tranche': 'CANAL_EXCELLENT', 'min': 0.0, 'max': 0.2, 'count': 250, 'patterns_s': 125, 'patterns_o': 125, 'pourcentage_s': 50.0}
  {'tranche': 'CANAL_BON', 'min': 0.2, 'max': 0.5, 'count': 110, 'patterns_s': 55, 'patterns_o': 55, 'pourcentage_s': 50.0}
  {'tranche': 'CANAL_MOYEN', 'min': 0.5, 'max': 0.8, 'count': 130, 'patterns_s': 65, 'patterns_o': 65, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_ERASURE
------------
Description: DIFF basé sur capacité canal effaceur κ = 1 - α

ANALYSE PAR TRANCHES:
  {'tranche': 'EFFACEMENT_FAIBLE', 'min': 0.0, 'max': 0.2, 'count': 137, 'patterns_s': 69, 'patterns_o': 68, 'pourcentage_s': 50.36496350364964}
  {'tranche': 'EFFACEMENT_MODÉRÉ', 'min': 0.2, 'max': 0.5, 'count': 113, 'patterns_s': 56, 'patterns_o': 57, 'pourcentage_s': 49.557522123893804}
  {'tranche': 'EFFACEMENT_FORT', 'min': 0.5, 'max': 0.8, 'count': 153, 'patterns_s': 76, 'patterns_o': 77, 'pourcentage_s': 49.673202614379086}
  {'tranche': 'EFFACEMENT_CRITIQUE', 'min': 0.8, 'max': 1.0, 'count': 87, 'patterns_s': 44, 'patterns_o': 43, 'pourcentage_s': 50.57471264367817}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_HUFFMAN
------------
Description: DIFF basé sur efficacité de Huffman

ANALYSE PAR TRANCHES:
  {'tranche': 'COMPRESSION_BONNE', 'min': 0.5, 'max': 0.8, 'count': 70, 'patterns_s': 33, 'patterns_o': 37, 'pourcentage_s': 47.14285714285714}
  {'tranche': 'COMPRESSION_OPTIMALE', 'min': 0.8, 'max': 1.0, 'count': 420, 'patterns_s': 212, 'patterns_o': 208, 'pourcentage_s': 50.476190476190474}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_INVERSE
------------
Description: DIFF basé sur métriques d'entropie inverse (ordre vs chaos)

ANALYSE PAR TRANCHES:
  {'tranche': 'CHAOS_TOTAL', 'min': 0.0, 'max': 10.0, 'count': 500, 'patterns_s': 250, 'patterns_o': 250, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_STD
--------
Description: DIFF basé sur écart-type d'entropie

ANALYSE PAR TRANCHES:
  {'tranche': 'STABILITÉ_PARFAITE', 'min': 0.0, 'max': 0.05, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}
  {'tranche': 'STABILITÉ_EXCELLENTE', 'min': 0.05, 'max': 0.1, 'count': 130, 'patterns_s': 65, 'patterns_o': 65, 'pourcentage_s': 50.0}
  {'tranche': 'STABILITÉ_BONNE', 'min': 0.1, 'max': 0.2, 'count': 110, 'patterns_s': 55, 'patterns_o': 55, 'pourcentage_s': 50.0}
  {'tranche': 'INSTABILITÉ_MODÉRÉE', 'min': 0.2, 'max': 0.5, 'count': 250, 'patterns_s': 125, 'patterns_o': 125, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_LOG_PRED
-------------
Description: DIFF basé sur formule prédiction logarithmique P(S) = 0.45 + 0.35*log(DIFF+0.01)

ANALYSE PAR TRANCHES:
  {'tranche': 'PRÉDICTION_FAIBLE', 'min': 0.0, 'max': 0.3, 'count': 490, 'patterns_s': 245, 'patterns_o': 245, 'pourcentage_s': 50.0}
  {'tranche': 'PRÉDICTION_MODÉRÉE', 'min': 0.3, 'max': 0.5, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_AEP
--------
Description: DIFF basé sur théorème d'équipartition asymptotique

ANALYSE PAR TRANCHES:
  {'tranche': 'AEP_FAIBLE', 'min': 0.0, 'max': 1.0, 'count': 262, 'patterns_s': 134, 'patterns_o': 128, 'pourcentage_s': 51.14503816793893}
  {'tranche': 'AEP_MODÉRÉ', 'min': 1.0, 'max': 3.0, 'count': 238, 'patterns_s': 116, 'patterns_o': 122, 'pourcentage_s': 48.739495798319325}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_JENSEN
-----------
Description: DIFF basé sur inégalité de Jensen f(E[X]) ≤ E[f(X)]

ANALYSE PAR TRANCHES:
  {'tranche': 'JENSEN_RESPECTÉE', 'min': 0.0, 'max': 0.01, 'count': 150, 'patterns_s': 76, 'patterns_o': 74, 'pourcentage_s': 50.66666666666667}
  {'tranche': 'JENSEN_LÉGÈRE_VIOLATION', 'min': 0.01, 'max': 0.05, 'count': 203, 'patterns_s': 102, 'patterns_o': 101, 'pourcentage_s': 50.24630541871922}
  {'tranche': 'JENSEN_VIOLATION_MODÉRÉE', 'min': 0.05, 'max': 0.1, 'count': 89, 'patterns_s': 44, 'patterns_o': 45, 'pourcentage_s': 49.43820224719101}
  {'tranche': 'JENSEN_FORTE_VIOLATION', 'min': 0.1, 'max': 1.0, 'count': 58, 'patterns_s': 28, 'patterns_o': 30, 'pourcentage_s': 48.275862068965516}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_LOG_SUM
------------
Description: DIFF basé sur inégalité log-sum

ANALYSE PAR TRANCHES:
  {'tranche': 'LOG_SUM_FAIBLE', 'min': 0.0, 'max': 0.1, 'count': 353, 'patterns_s': 178, 'patterns_o': 175, 'pourcentage_s': 50.42492917847026}
  {'tranche': 'LOG_SUM_MODÉRÉ', 'min': 0.1, 'max': 0.5, 'count': 147, 'patterns_s': 72, 'patterns_o': 75, 'pourcentage_s': 48.97959183673469}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_CONCAVITY
--------------
Description: DIFF basé sur concavité entropie H(λp₁+(1-λ)p₂) ≥ λH(p₁)+(1-λ)H(p₂)

ANALYSE PAR TRANCHES:
  {'tranche': 'CONCAVITÉ_PARFAITE', 'min': 0.0, 'max': 0.01, 'count': 115, 'patterns_s': 58, 'patterns_o': 57, 'pourcentage_s': 50.43478260869565}
  {'tranche': 'CONCAVITÉ_BONNE', 'min': 0.01, 'max': 0.05, 'count': 135, 'patterns_s': 67, 'patterns_o': 68, 'pourcentage_s': 49.629629629629626}
  {'tranche': 'CONCAVITÉ_ACCEPTABLE', 'min': 0.05, 'max': 0.1, 'count': 90, 'patterns_s': 45, 'patterns_o': 45, 'pourcentage_s': 50.0}
  {'tranche': 'CONCAVITÉ_DÉGRADÉE', 'min': 0.1, 'max': 0.5, 'count': 160, 'patterns_s': 80, 'patterns_o': 80, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_SMB
--------
Description: DIFF basé sur théorème Shannon-McMillan-Breiman

ANALYSE PAR TRANCHES:
  {'tranche': 'SMB_CONVERGENCE_RAPIDE', 'min': 0.0, 'max': 1.0, 'count': 222, 'patterns_s': 114, 'patterns_o': 108, 'pourcentage_s': 51.35135135135135}
  {'tranche': 'SMB_CONVERGENCE_MODÉRÉE', 'min': 1.0, 'max': 3.0, 'count': 278, 'patterns_s': 136, 'patterns_o': 142, 'pourcentage_s': 48.92086330935252}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_ERGODIC
------------
Description: DIFF basé sur estimation entropie ergodique

ANALYSE PAR TRANCHES:
  {'tranche': 'ERGODICITÉ_FORTE', 'min': 0.0, 'max': 0.1, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}
  {'tranche': 'ERGODICITÉ_FAIBLE', 'min': 0.3, 'max': 0.6, 'count': 130, 'patterns_s': 65, 'patterns_o': 65, 'pourcentage_s': 50.0}
  {'tranche': 'NON_ERGODIQUE', 'min': 0.6, 'max': 1.0, 'count': 310, 'patterns_s': 155, 'patterns_o': 155, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_CHANNEL
------------
Description: DIFF basé sur théorème codage de canal

ANALYSE PAR TRANCHES:
  {'tranche': 'CANAL_SATURÉ', 'min': 0.0, 'max': 0.2, 'count': 265, 'patterns_s': 135, 'patterns_o': 130, 'pourcentage_s': 50.943396226415096}
  {'tranche': 'CANAL_CHARGÉ', 'min': 0.2, 'max': 0.5, 'count': 173, 'patterns_s': 86, 'patterns_o': 87, 'pourcentage_s': 49.71098265895954}
  {'tranche': 'CANAL_DISPONIBLE', 'min': 0.5, 'max': 0.8, 'count': 61, 'patterns_s': 29, 'patterns_o': 32, 'pourcentage_s': 47.540983606557376}
  {'tranche': 'CANAL_LIBRE', 'min': 0.8, 'max': 1.0, 'count': 1, 'patterns_s': 0, 'patterns_o': 1, 'pourcentage_s': 0.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_ERROR
----------
Description: DIFF basé sur borne probabilité d'erreur

ANALYSE PAR TRANCHES:
  {'tranche': 'ERREUR_NÉGLIGEABLE', 'min': 0.0, 'max': 0.01, 'count': 19, 'patterns_s': 9, 'patterns_o': 10, 'pourcentage_s': 47.368421052631575}
  {'tranche': 'ERREUR_FAIBLE', 'min': 0.01, 'max': 0.1, 'count': 123, 'patterns_s': 61, 'patterns_o': 62, 'pourcentage_s': 49.59349593495935}
  {'tranche': 'ERREUR_MODÉRÉE', 'min': 0.1, 'max': 0.5, 'count': 175, 'patterns_s': 85, 'patterns_o': 90, 'pourcentage_s': 48.57142857142857}
  {'tranche': 'ERREUR_CRITIQUE', 'min': 0.5, 'max': 1.0, 'count': 85, 'patterns_s': 44, 'patterns_o': 41, 'pourcentage_s': 51.76470588235295}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_SPHERE
-----------
Description: DIFF basé sur borne de sphère (sphere packing)

ANALYSE PAR TRANCHES:
  {'tranche': 'PACKING_MODÉRÉ', 'min': 2.0, 'max': 5.0, 'count': 137, 'patterns_s': 69, 'patterns_o': 68, 'pourcentage_s': 50.36496350364964}
  {'tranche': 'PACKING_LÂCHE', 'min': 5.0, 'max': 8.0, 'count': 172, 'patterns_s': 86, 'patterns_o': 86, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_COMPREHENSIVE
------------------
Description: DIFF basé sur analyse complète d'entropie multi-échelle

ANALYSE PAR TRANCHES:
  {'tranche': 'ANALYSE_SIMPLE', 'min': 0.0, 'max': 0.2, 'count': 10, 'patterns_s': 5, 'patterns_o': 5, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_RELATIVE
-------------
Description: DIFF basé sur entropie relative (divergence KL) D(p||q) = ∑ p(x) log₂(p(x)/q(x))

ANALYSE PAR TRANCHES:
  {'tranche': 'DIVERGENCE_FAIBLE', 'min': 0.0, 'max': 0.1, 'count': 140, 'patterns_s': 70, 'patterns_o': 70, 'pourcentage_s': 50.0}
  {'tranche': 'DIVERGENCE_MODÉRÉE', 'min': 0.1, 'max': 0.5, 'count': 236, 'patterns_s': 118, 'patterns_o': 118, 'pourcentage_s': 50.0}
  {'tranche': 'DIVERGENCE_FORTE', 'min': 0.5, 'max': 1.0, 'count': 82, 'patterns_s': 41, 'patterns_o': 41, 'pourcentage_s': 50.0}
  {'tranche': 'DIVERGENCE_TRÈS_FORTE', 'min': 1.0, 'max': 2.0, 'count': 42, 'patterns_s': 21, 'patterns_o': 21, 'pourcentage_s': 50.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_COND_MI
------------
Description: DIFF basé sur information mutuelle conditionnelle I(X;Y|Z) = H(X|Z) - H(X|Y,Z)

ANALYSE PAR TRANCHES:
  {'tranche': 'INFO_FAIBLE', 'min': 0.0, 'max': 0.1, 'count': 368, 'patterns_s': 182, 'patterns_o': 186, 'pourcentage_s': 49.45652173913043}
  {'tranche': 'INFO_MODÉRÉE', 'min': 0.1, 'max': 0.3, 'count': 112, 'patterns_s': 56, 'patterns_o': 56, 'pourcentage_s': 50.0}
  {'tranche': 'INFO_FORTE', 'min': 0.3, 'max': 0.6, 'count': 20, 'patterns_s': 12, 'patterns_o': 8, 'pourcentage_s': 60.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================

DIFF_TYPICAL
------------
Description: DIFF basé sur ensemble typique T(n,ε) et bornes asymptotiques

ANALYSE PAR TRANCHES:
  {'tranche': 'ENSEMBLE_PETIT', 'min': 0.0, 'max': 2.0, 'count': 5, 'patterns_s': 2, 'patterns_o': 3, 'pourcentage_s': 40.0}
  {'tranche': 'ENSEMBLE_MODÉRÉ', 'min': 2.0, 'max': 5.0, 'count': 220, 'patterns_s': 107, 'patterns_o': 113, 'pourcentage_s': 48.63636363636364}
  {'tranche': 'ENSEMBLE_GRAND', 'min': 5.0, 'max': 8.0, 'count': 265, 'patterns_s': 135, 'patterns_o': 130, 'pourcentage_s': 50.943396226415096}
  {'tranche': 'ENSEMBLE_TRÈS_GRAND', 'min': 8.0, 'max': 10.0, 'count': 10, 'patterns_s': 6, 'patterns_o': 4, 'pourcentage_s': 60.0}

CORRÉLATIONS:
  correlation_metrique_pattern
  moyenne_metrique
  ecart_type_metrique

============================================================
