#!/usr/bin/env python3
"""
Script de debug pour analyser la structure du dataset et des données DIFF
"""

import json

def analyser_structure_dataset():
    """Analyse la structure du dataset"""
    print("🔍 ANALYSE STRUCTURE DATASET")
    print("=" * 50)
    
    try:
        # Charger le dataset
        with open('dataset_baccarat_lupasco_20250626_044753.json', 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        print(f"✅ Dataset chargé: {type(dataset)}")

        # Vérifier si c'est une liste ou un dictionnaire
        if isinstance(dataset, list):
            print(f"   Type: Liste de {len(dataset)} parties")
            if dataset:
                premiere_partie = dataset[0]
        elif isinstance(dataset, dict):
            print(f"   Type: Dictionnaire avec {len(dataset)} clés")
            print(f"   Clés principales: {list(dataset.keys())}")

            # Chercher les parties dans le dictionnaire
            if 'parties' in dataset:
                parties = dataset['parties']
                print(f"   Parties trouvées: {len(parties)}")
                if parties:
                    premiere_partie = parties[0]
            else:
                # Prendre la première valeur qui ressemble à une partie
                for cle, valeur in dataset.items():
                    if isinstance(valeur, list) and valeur:
                        print(f"   Tentative avec clé '{cle}': {len(valeur)} éléments")
                        premiere_partie = valeur[0]
                        break
                else:
                    premiere_partie = None
        else:
            print(f"   Type inattendu: {type(dataset)}")
            premiere_partie = None

        # Analyser la première partie si trouvée
        if premiere_partie:
            print(f"\n📊 STRUCTURE PREMIÈRE PARTIE:")
            for cle, valeur in premiere_partie.items():
                if isinstance(valeur, (str, int, float)):
                    print(f"   {cle}: {valeur}")
                elif isinstance(valeur, list):
                    print(f"   {cle}: liste de {len(valeur)} éléments")
                    if valeur and isinstance(valeur[0], dict):
                        print(f"      Premier élément: {list(valeur[0].keys())}")
                else:
                    print(f"   {cle}: {type(valeur)}")

            # Analyser les mains
            if 'mains' in premiere_partie:
                mains = premiere_partie['mains']
                print(f"\n📊 STRUCTURE PREMIÈRE MAIN:")
                if mains:
                    premiere_main = mains[0]
                    for cle, valeur in premiere_main.items():
                        print(f"   {cle}: {valeur}")
        else:
            print("❌ Aucune partie trouvée dans le dataset")
                    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def analyser_structure_donnees_diff():
    """Analyse la structure des données DIFF depuis le programme principal"""
    print("\n🔍 ANALYSE STRUCTURE DONNÉES DIFF")
    print("=" * 50)
    
    try:
        # Importer le module principal pour accéder aux données
        import analyse_complete_avec_diff as main_module
        
        # Simuler l'extraction de quelques données DIFF
        print("🔄 Simulation extraction données DIFF...")
        
        # Cette partie nécessiterait d'exécuter le programme principal
        # Pour l'instant, on va juste montrer la structure attendue
        print("📊 STRUCTURE ATTENDUE DONNÉES DIFF:")
        print("   - partie_id: identifiant de la partie")
        print("   - main: numéro de la main")
        print("   - ratio_l4: ratio L4")
        print("   - ratio_l5: ratio L5") 
        print("   - diff: valeur DIFF")
        print("   - pattern: pattern S/O")
        print("   - index3: valeur INDEX3")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    analyser_structure_dataset()
    analyser_structure_donnees_diff()
