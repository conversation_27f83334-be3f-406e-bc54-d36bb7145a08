#!/usr/bin/env python3
"""
🎯 EXERCICES INTERACTIFS - THÉORIE DE L'ENTROPIE
Système d'exercices auto-corrigés avec feedback pédagogique

Auteur: Cours d'Entropie - Niveau Tous
Date: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional, Callable
import random
import json
from datetime import datetime

class ExerciceEntropie:
    """Classe de base pour les exercices d'entropie"""
    
    def __init__(self, titre: str, niveau: str, description: str):
        self.titre = titre
        self.niveau = niveau  # "débutant", "intermédiaire", "expert"
        self.description = description
        self.score = 0
        self.tentatives = 0
        self.max_tentatives = 3
        
    def generer_donnees(self) -> Dict:
        """Génère des données aléatoires pour l'exercice"""
        raise NotImplementedError
        
    def verifier_reponse(self, reponse: float, tolerance: float = 1e-6) -> <PERSON><PERSON>[bool, str]:
        """Vérifie la réponse et retourne (correct, feedback)"""
        raise NotImplementedError
        
    def afficher_aide(self) -> str:
        """Affiche une aide contextuelle"""
        raise NotImplementedError

class ExerciceShannon(ExerciceEntropie):
    """Exercice sur l'entropie de Shannon"""
    
    def __init__(self):
        super().__init__(
            "Calcul d'Entropie de Shannon",
            "débutant",
            "Calculer l'entropie H(X) = -∑ p(x) log₂ p(x) pour une distribution donnée"
        )
        
    def generer_donnees(self) -> Dict:
        """Génère une distribution de probabilité aléatoire"""
        n_events = random.randint(2, 5)
        
        # Génération de probabilités aléatoires
        raw_probs = np.random.exponential(1, n_events)
        probabilities = raw_probs / np.sum(raw_probs)
        
        # Calcul de la solution
        solution = -np.sum(probabilities * np.log2(probabilities + 1e-10))
        
        self.donnees = {
            'probabilities': probabilities,
            'n_events': n_events,
            'solution': solution
        }
        
        return self.donnees
        
    def verifier_reponse(self, reponse: float, tolerance: float = 1e-3) -> Tuple[bool, str]:
        """Vérifie le calcul d'entropie"""
        solution = self.donnees['solution']
        
        if abs(reponse - solution) <= tolerance:
            feedback = f"✅ Correct! H(X) = {solution:.4f} bits"
            return True, feedback
        else:
            feedback = f"❌ Incorrect. Votre réponse: {reponse:.4f}, Solution: {solution:.4f}"
            return False, feedback
            
    def afficher_aide(self) -> str:
        return """
💡 AIDE - Entropie de Shannon:
1. Formule: H(X) = -∑ p(x) log₂ p(x)
2. Vérifiez que ∑ p(x) = 1
3. Utilisez log₂ (logarithme base 2)
4. Attention aux cas p(x) = 0 (convention: 0 log 0 = 0)
"""

class ExerciceInformationMutuelle(ExerciceEntropie):
    """Exercice sur l'information mutuelle"""
    
    def __init__(self):
        super().__init__(
            "Information Mutuelle",
            "intermédiaire",
            "Calculer I(X;Y) = H(X) + H(Y) - H(X,Y) à partir d'une distribution jointe"
        )
        
    def generer_donnees(self) -> Dict:
        """Génère une distribution jointe 2x2"""
        # Matrice 2x2 aléatoire
        joint_matrix = np.random.exponential(1, (2, 2))
        joint_probs = joint_matrix / np.sum(joint_matrix)
        
        # Distributions marginales
        px = np.sum(joint_probs, axis=1)
        py = np.sum(joint_probs, axis=0)
        
        # Calculs d'entropie
        hx = -np.sum(px * np.log2(px + 1e-10))
        hy = -np.sum(py * np.log2(py + 1e-10))
        hxy = -np.sum(joint_probs * np.log2(joint_probs + 1e-10))
        
        # Information mutuelle
        mutual_info = hx + hy - hxy
        
        self.donnees = {
            'joint_probs': joint_probs,
            'px': px,
            'py': py,
            'hx': hx,
            'hy': hy,
            'hxy': hxy,
            'solution': mutual_info
        }
        
        return self.donnees
        
    def verifier_reponse(self, reponse: float, tolerance: float = 1e-3) -> Tuple[bool, str]:
        solution = self.donnees['solution']
        
        if abs(reponse - solution) <= tolerance:
            feedback = f"✅ Correct! I(X;Y) = {solution:.4f} bits"
            return True, feedback
        else:
            feedback = f"❌ Incorrect. I(X;Y) = {solution:.4f} bits"
            return False, feedback
            
    def afficher_aide(self) -> str:
        return """
💡 AIDE - Information Mutuelle:
1. I(X;Y) = H(X) + H(Y) - H(X,Y)
2. Ou: I(X;Y) = ∑∑ p(x,y) log₂(p(x,y)/(p(x)p(y)))
3. Calculez d'abord les marginales: p(x) = ∑_y p(x,y)
4. Puis les entropies individuelles et jointe
"""

class ExerciceDivergenceKL(ExerciceEntropie):
    """Exercice sur la divergence de Kullback-Leibler"""
    
    def __init__(self):
        super().__init__(
            "Divergence de Kullback-Leibler",
            "intermédiaire",
            "Calculer D(P||Q) = ∑ p(x) log₂(p(x)/q(x))"
        )
        
    def generer_donnees(self) -> Dict:
        """Génère deux distributions"""
        n = 4
        
        # Distribution P
        p_raw = np.random.exponential(1, n)
        p = p_raw / np.sum(p_raw)
        
        # Distribution Q (légèrement différente)
        q_raw = p_raw + np.random.normal(0, 0.1, n)
        q_raw = np.maximum(q_raw, 0.01)  # Éviter les zéros
        q = q_raw / np.sum(q_raw)
        
        # Calcul de la divergence KL
        kl_div = np.sum(p * np.log2(p / q))
        
        self.donnees = {
            'p': p,
            'q': q,
            'solution': kl_div
        }
        
        return self.donnees
        
    def verifier_reponse(self, reponse: float, tolerance: float = 1e-3) -> Tuple[bool, str]:
        solution = self.donnees['solution']
        
        if abs(reponse - solution) <= tolerance:
            feedback = f"✅ Correct! D(P||Q) = {solution:.4f} bits"
            return True, feedback
        else:
            feedback = f"❌ Incorrect. D(P||Q) = {solution:.4f} bits"
            return False, feedback
            
    def afficher_aide(self) -> str:
        return """
💡 AIDE - Divergence KL:
1. D(P||Q) = ∑ p(x) log₂(p(x)/q(x))
2. Attention: non symétrique! D(P||Q) ≠ D(Q||P)
3. Toujours ≥ 0, égale à 0 ssi P = Q
4. Vérifiez que q(x) > 0 partout où p(x) > 0
"""

class SystemeExercices:
    """Système de gestion des exercices interactifs"""
    
    def __init__(self):
        self.exercices = {
            'shannon': ExerciceShannon(),
            'mutual_info': ExerciceInformationMutuelle(),
            'kl_divergence': ExerciceDivergenceKL()
        }
        self.historique = []
        
    def lister_exercices(self) -> None:
        """Affiche la liste des exercices disponibles"""
        print("🎯 EXERCICES DISPONIBLES:")
        print("=" * 50)
        
        for key, exercice in self.exercices.items():
            niveau_emoji = {"débutant": "🟢", "intermédiaire": "🟡", "expert": "🔴"}
            emoji = niveau_emoji.get(exercice.niveau, "⚪")
            
            print(f"{emoji} {key}: {exercice.titre}")
            print(f"   Niveau: {exercice.niveau}")
            print(f"   Description: {exercice.description}")
            print()
            
    def executer_exercice(self, nom_exercice: str) -> None:
        """Exécute un exercice spécifique"""
        if nom_exercice not in self.exercices:
            print(f"❌ Exercice '{nom_exercice}' non trouvé!")
            return
            
        exercice = self.exercices[nom_exercice]
        print(f"\n🎯 {exercice.titre}")
        print("=" * 60)
        print(f"Niveau: {exercice.niveau}")
        print(f"Description: {exercice.description}")
        print()
        
        # Génération des données
        donnees = exercice.generer_donnees()
        self._afficher_donnees(exercice, donnees)
        
        # Boucle de tentatives
        for tentative in range(exercice.max_tentatives):
            print(f"\n📝 Tentative {tentative + 1}/{exercice.max_tentatives}")
            
            try:
                reponse = float(input("Votre réponse: "))
                correct, feedback = exercice.verifier_reponse(reponse)
                
                print(feedback)
                
                if correct:
                    exercice.score += (exercice.max_tentatives - tentative) * 10
                    print(f"🏆 Score: +{(exercice.max_tentatives - tentative) * 10} points")
                    break
                elif tentative < exercice.max_tentatives - 1:
                    aide = input("\n❓ Voulez-vous de l'aide? (o/n): ")
                    if aide.lower() == 'o':
                        print(exercice.afficher_aide())
                        
            except ValueError:
                print("❌ Veuillez entrer un nombre valide!")
                
        else:
            print(f"\n💡 Solution: {donnees['solution']:.4f}")
            
        # Enregistrement dans l'historique
        self.historique.append({
            'exercice': nom_exercice,
            'timestamp': datetime.now().isoformat(),
            'score': exercice.score,
            'tentatives': tentative + 1
        })
        
    def _afficher_donnees(self, exercice: ExerciceEntropie, donnees: Dict) -> None:
        """Affiche les données de l'exercice de manière formatée"""
        if isinstance(exercice, ExerciceShannon):
            print("📊 Distribution de probabilité:")
            for i, p in enumerate(donnees['probabilities']):
                print(f"   P(X={i}) = {p:.4f}")
                
        elif isinstance(exercice, ExerciceInformationMutuelle):
            print("📊 Distribution jointe P(X,Y):")
            joint = donnees['joint_probs']
            print("     Y=0    Y=1")
            for i in range(2):
                print(f"X={i}  {joint[i,0]:.3f}  {joint[i,1]:.3f}")
                
        elif isinstance(exercice, ExerciceDivergenceKL):
            print("📊 Distributions P et Q:")
            for i, (p, q) in enumerate(zip(donnees['p'], donnees['q'])):
                print(f"   P(X={i}) = {p:.4f}, Q(X={i}) = {q:.4f}")
                
    def afficher_statistiques(self) -> None:
        """Affiche les statistiques de performance"""
        if not self.historique:
            print("📊 Aucun exercice réalisé encore.")
            return
            
        print("\n📊 STATISTIQUES DE PERFORMANCE")
        print("=" * 50)
        
        total_score = sum(entry['score'] for entry in self.historique)
        nb_exercices = len(self.historique)
        
        print(f"Exercices réalisés: {nb_exercices}")
        print(f"Score total: {total_score}")
        print(f"Score moyen: {total_score/nb_exercices:.1f}")
        
        # Statistiques par exercice
        stats_par_exercice = {}
        for entry in self.historique:
            nom = entry['exercice']
            if nom not in stats_par_exercice:
                stats_par_exercice[nom] = []
            stats_par_exercice[nom].append(entry['score'])
            
        print("\n📈 Détail par exercice:")
        for nom, scores in stats_par_exercice.items():
            moyenne = np.mean(scores)
            print(f"   {nom}: {len(scores)} fois, moyenne {moyenne:.1f}")
            
    def mode_entrainement(self) -> None:
        """Mode d'entraînement interactif"""
        print("🎓 MODE ENTRAÎNEMENT - THÉORIE DE L'ENTROPIE")
        print("=" * 60)
        
        while True:
            print("\n🎯 MENU PRINCIPAL:")
            print("1. Lister les exercices")
            print("2. Faire un exercice")
            print("3. Exercice aléatoire")
            print("4. Voir les statistiques")
            print("5. Quitter")
            
            choix = input("\nVotre choix (1-5): ")
            
            if choix == '1':
                self.lister_exercices()
            elif choix == '2':
                nom = input("Nom de l'exercice: ")
                self.executer_exercice(nom)
            elif choix == '3':
                nom = random.choice(list(self.exercices.keys()))
                print(f"🎲 Exercice aléatoire sélectionné: {nom}")
                self.executer_exercice(nom)
            elif choix == '4':
                self.afficher_statistiques()
            elif choix == '5':
                print("👋 Au revoir! Continuez à explorer l'entropie!")
                break
            else:
                print("❌ Choix invalide!")

def demo_exercices():
    """Démonstration du système d'exercices"""
    print("🎯 DÉMONSTRATION - EXERCICES INTERACTIFS")
    print("=" * 60)
    
    # Création du système
    systeme = SystemeExercices()
    
    # Démonstration de chaque type d'exercice
    for nom_exercice in ['shannon', 'mutual_info', 'kl_divergence']:
        print(f"\n🔍 Démonstration: {nom_exercice}")
        print("-" * 40)
        
        exercice = systeme.exercices[nom_exercice]
        donnees = exercice.generer_donnees()
        
        systeme._afficher_donnees(exercice, donnees)
        print(f"💡 Solution: {donnees['solution']:.4f}")
        print(exercice.afficher_aide())

def creer_exercices_personnalises():
    """Créateur d'exercices personnalisés"""
    print("🛠️ CRÉATEUR D'EXERCICES PERSONNALISÉS")
    print("=" * 50)
    
    # Exemple: exercice sur l'entropie conditionnelle
    def exercice_conditionnel():
        # Génération de données
        joint = np.array([[0.3, 0.1], [0.2, 0.4]])
        px = np.sum(joint, axis=1)
        
        # Calcul H(Y|X)
        hy_given_x = 0
        for i in range(2):
            if px[i] > 0:
                py_given_xi = joint[i, :] / px[i]
                hy_given_xi = -np.sum(py_given_xi * np.log2(py_given_xi + 1e-10))
                hy_given_x += px[i] * hy_given_xi
                
        print("📊 Distribution jointe:")
        print("     Y=0    Y=1")
        for i in range(2):
            print(f"X={i}  {joint[i,0]:.1f}   {joint[i,1]:.1f}")
            
        print(f"\n🎯 Calculez H(Y|X)")
        print(f"💡 Solution: {hy_given_x:.4f} bits")
        
    exercice_conditionnel()

if __name__ == "__main__":
    print("🎓 SYSTÈME D'EXERCICES INTERACTIFS - ENTROPIE")
    print("=" * 60)
    
    choix = input("""
Choisissez un mode:
1. Mode entraînement interactif
2. Démonstration des exercices
3. Créateur d'exercices personnalisés

Votre choix (1-3): """)
    
    if choix == '1':
        systeme = SystemeExercices()
        systeme.mode_entrainement()
    elif choix == '2':
        demo_exercices()
    elif choix == '3':
        creer_exercices_personnalises()
    else:
        print("❌ Choix invalide!")
