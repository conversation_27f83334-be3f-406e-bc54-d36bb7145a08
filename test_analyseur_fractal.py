#!/usr/bin/env python3
"""
SCRIPT DE TEST POUR L'ANALYSEUR FRACTAL BACCARAT
Test des fonctionnalités principales et validation des calculs
"""

import numpy as np
import sys
import os
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat, analyser_partie_specifique

def test_calcul_hurst_manuel():
    """Test du calcul manuel de l'exposant de Hurst"""
    print("🧪 TEST CALCUL HURST MANUEL")
    print("-" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    
    # Test avec une série persistante (tendance croissante)
    serie_persistante = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
    rs_persistant = analyseur.hurst_rs_manuel(serie_persistante)
    print(f"Série persistante R/S: {rs_persistant:.4f}")
    
    # Test avec une série anti-persistante (alternance)
    serie_anti_persistante = np.array([1, -1, 1, -1, 1, -1, 1, -1])
    rs_anti_persistant = analyseur.hurst_rs_manuel(serie_anti_persistante)
    print(f"Série anti-persistante R/S: {rs_anti_persistant:.4f}")
    
    # Test avec une série aléatoire
    np.random.seed(42)
    serie_aleatoire = np.random.randn(20)
    rs_aleatoire = analyseur.hurst_rs_manuel(serie_aleatoire)
    print(f"Série aléatoire R/S: {rs_aleatoire:.4f}")
    
    print("✅ Test calcul R/S terminé\n")

def test_estimation_hurst_regression():
    """Test de l'estimation de Hurst par régression"""
    print("🧪 TEST ESTIMATION HURST PAR RÉGRESSION")
    print("-" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    
    # Test avec différents types de séries
    np.random.seed(42)
    
    # Série persistante simulée
    serie_persistante = np.cumsum(np.random.randn(50)) + np.arange(50) * 0.1
    hurst_persistant = analyseur.estimer_hurst_regression(serie_persistante)
    print(f"Série persistante - Hurst: {hurst_persistant:.4f}")
    
    # Série anti-persistante simulée
    serie_anti = []
    valeur = 0
    for i in range(50):
        if i > 0:
            # Tendance à revenir vers la moyenne
            valeur += -0.3 * valeur + np.random.randn() * 0.5
        else:
            valeur = np.random.randn()
        serie_anti.append(valeur)
    
    hurst_anti = analyseur.estimer_hurst_regression(np.array(serie_anti))
    print(f"Série anti-persistante - Hurst: {hurst_anti:.4f}")
    
    # Série aléatoire pure
    serie_aleatoire = np.random.randn(50)
    hurst_aleatoire = analyseur.estimer_hurst_regression(serie_aleatoire)
    print(f"Série aléatoire - Hurst: {hurst_aleatoire:.4f}")
    
    print("✅ Test estimation Hurst terminé\n")

def test_classification_persistance():
    """Test de la classification des types de persistance"""
    print("🧪 TEST CLASSIFICATION PERSISTANCE")
    print("-" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    
    # Test des différents seuils
    test_values = [0.3, 0.45, 0.5, 0.55, 0.7, np.nan]
    
    for val in test_values:
        classification = analyseur.classifier_persistance(val)
        print(f"H = {val} → {classification}")
    
    print("✅ Test classification terminé\n")

def test_extraction_sequences():
    """Test de l'extraction des séquences depuis une partie"""
    print("🧪 TEST EXTRACTION SÉQUENCES")
    print("-" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    
    # Créer une partie de test
    partie_test = {
        'partie_number': 1,
        'mains': [
            # Main dummy (à ignorer)
            {
                'main_number': None,
                'index3_result': '',
                'index5_combined': ''
            },
            # Mains valides
            {
                'main_number': 1,
                'index3_result': 'BANKER',
                'index5_combined': '0_A_BANKER'
            },
            {
                'main_number': 2,
                'index3_result': 'PLAYER',
                'index5_combined': '1_B_PLAYER'
            },
            {
                'main_number': 3,
                'index3_result': 'BANKER',
                'index5_combined': '0_C_BANKER'
            },
            {
                'main_number': 4,
                'index3_result': 'TIE',
                'index5_combined': '1_A_TIE'
            }
        ]
    }
    
    sequences = analyseur.extraire_sequences_partie(partie_test)
    
    print("Séquences extraites:")
    for nom, seq in sequences.items():
        print(f"  {nom}: {seq}")
    
    print("✅ Test extraction séquences terminé\n")

def test_analyse_partie_complete():
    """Test de l'analyse complète d'une partie"""
    print("🧪 TEST ANALYSE PARTIE COMPLÈTE")
    print("-" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    
    # Créer une partie de test plus longue
    mains_test = []
    
    # Main dummy
    mains_test.append({
        'main_number': None,
        'index3_result': '',
        'index5_combined': ''
    })
    
    # Générer 20 mains avec pattern
    np.random.seed(42)
    resultats = ['BANKER', 'PLAYER', 'TIE']
    categories = ['A', 'B', 'C']
    
    for i in range(1, 21):
        # Simuler une tendance persistante pour BANKER/PLAYER
        if i <= 10:
            resultat = 'BANKER' if i % 3 != 0 else 'TIE'
        else:
            resultat = 'PLAYER' if i % 3 != 0 else 'TIE'
        
        index1 = str(i % 2)
        index2 = np.random.choice(categories)
        
        mains_test.append({
            'main_number': i,
            'index3_result': resultat,
            'index5_combined': f'{index1}_{index2}_{resultat}'
        })
    
    partie_test = {
        'partie_number': 999,
        'mains': mains_test
    }
    
    # Analyser la partie
    resultat = analyseur.analyser_partie_fractale(partie_test)
    
    print("Résultats de l'analyse:")
    print(f"  Numéro partie: {resultat.numero_partie}")
    print(f"  Mains valides: {resultat.nb_mains_valides}")
    print(f"  Hurst résultats: {resultat.hurst_resultats:.4f}")
    print(f"  Type persistance: {resultat.type_persistance_resultats}")
    print(f"  Qualité estimation: {resultat.qualite_estimation}")
    print(f"  Prédiction: {resultat.prediction_main_suivante}")
    print(f"  Confiance: {resultat.confiance_prediction:.3f}")
    print(f"  Entropie Shannon: {resultat.entropie_shannon:.3f}")
    
    print("✅ Test analyse partie terminé\n")

def test_verification_dataset():
    """Vérifie que le dataset est accessible"""
    print("🧪 TEST VÉRIFICATION DATASET")
    print("-" * 40)
    
    dataset_path = "dataset_baccarat_lupasco_20250626_044753.json"
    
    if os.path.exists(dataset_path):
        print(f"✅ Dataset trouvé: {dataset_path}")
        
        try:
            analyseur = AnalyseurFractalBaccarat(dataset_path)
            dataset = analyseur.charger_dataset()
            nb_parties = len(dataset.get('parties', []))
            print(f"✅ Dataset chargé avec succès: {nb_parties} parties")
            
            # Tester l'analyse d'une partie réelle
            if nb_parties > 0:
                print("🔍 Test analyse première partie réelle...")
                premiere_partie = dataset['parties'][0]
                resultat = analyseur.analyser_partie_fractale(premiere_partie)
                print(f"   Partie {resultat.numero_partie}: {resultat.nb_mains_valides} mains, H={resultat.hurst_resultats:.4f}")
                
        except Exception as e:
            print(f"❌ Erreur lors du chargement: {e}")
    else:
        print(f"⚠️  Dataset non trouvé: {dataset_path}")
        print("   Le test d'analyse réelle ne peut pas être effectué")
    
    print("✅ Test vérification dataset terminé\n")

def main():
    """Exécute tous les tests"""
    print("🧪 SUITE DE TESTS - ANALYSEUR FRACTAL BACCARAT")
    print("=" * 60)
    
    # Exécuter tous les tests
    test_calcul_hurst_manuel()
    test_estimation_hurst_regression()
    test_classification_persistance()
    test_extraction_sequences()
    test_analyse_partie_complete()
    test_verification_dataset()
    
    print("🎯 TESTS TERMINÉS")
    print("=" * 60)
    
    # Proposer de tester une partie spécifique
    print("\n🔍 TESTS OPTIONNELS")
    print("1. Analyser une partie spécifique du dataset")
    print("2. Lancer une analyse échantillon (10 parties)")
    print("3. Quitter")
    
    choix = input("\nVotre choix (1/2/3): ").strip()
    
    if choix == "1":
        try:
            numero = int(input("Numéro de partie à analyser: "))
            analyser_partie_specifique(numero)
        except ValueError:
            print("⚠️  Numéro invalide")
    
    elif choix == "2":
        print("\n🚀 Lancement analyse échantillon...")
        try:
            analyseur = AnalyseurFractalBaccarat()
            df_resultats = analyseur.analyser_dataset_complet(nb_parties_max=10)
            print(f"✅ Analyse échantillon terminée: {len(df_resultats)} parties")
        except Exception as e:
            print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
