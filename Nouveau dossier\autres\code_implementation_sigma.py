
# ============================================================================
# SEUILS BASÉS SUR ÉCART-TYPE POUR MÉTRIQUES ENTROPIQUES
# ============================================================================
# Généré automatiquement le 2025-06-27 08:17:12
# Remplace les seuils arbitraires par des seuils statistiquement cohérents

def analyser_conditions_sigma_diff(donnees_diff):
    """
    Analyse DIFF avec seuils basés sur σ (écart-type)
    """
    conditions_s = []
    conditions_o = []
    
    # Statistiques DIFF
    moyenne_diff = 0.087173
    ecart_type_diff = 0.048142
    
    # DIFF_2.5σ_HAUT: 69.1% S
    donnees_condition = [d for d in donnees_diff if d.get('diff', 0) >= 0.207528]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_2.5σ_HAUT',
                'description': 'DIFF >= 0.207528 (moyenne + 2.5σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'exceptionnelle'
            })
    
    # DIFF_3σ_HAUT: 67.7% S
    donnees_condition = [d for d in donnees_diff if d.get('diff', 0) >= 0.231599]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_3σ_HAUT',
                'description': 'DIFF >= 0.231599 (moyenne + 3σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'extrême'
            })
    
    # DIFF_2σ_HAUT: 67.3% S
    donnees_condition = [d for d in donnees_diff if d.get('diff', 0) >= 0.183457]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_2σ_HAUT',
                'description': 'DIFF >= 0.183457 (moyenne + 2σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'très_forte'
            })
    
    # DIFF_1.5σ_HAUT: 66.8% S
    donnees_condition = [d for d in donnees_diff if d.get('diff', 0) >= 0.159386]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_1.5σ_HAUT',
                'description': 'DIFF >= 0.159386 (moyenne + 1.5σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'forte'
            })
    
    # DIFF_1σ_HAUT: 62.5% S
    donnees_condition = [d for d in donnees_diff if d.get('diff', 0) >= 0.135315]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_1σ_HAUT',
                'description': 'DIFF >= 0.135315 (moyenne + 1σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'modérée'
            })
    
    return conditions_s, conditions_o

def analyser_conditions_sigma_diff_renyi(donnees_renyi):
    """
    Analyse DIFF_RENYI avec seuils basés sur σ
    """
    conditions_s = []
    conditions_o = []
    
    # Statistiques DIFF_RENYI
    moyenne_renyi = 0.098760
    ecart_type_renyi = 0.043634
    
    # DIFF_RENYI_3σ_HAUT: 65.5% S
    donnees_condition = [d for d in donnees_renyi if d.get('diff_renyi', 0) >= 0.229663]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_RENYI_3σ_HAUT',
                'description': 'DIFF_RENYI >= 0.229663 (moyenne + 3σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'extrême'
            })
    
    # DIFF_RENYI_1σ_HAUT: 61.5% S
    donnees_condition = [d for d in donnees_renyi if d.get('diff_renyi', 0) >= 0.142394]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_RENYI_1σ_HAUT',
                'description': 'DIFF_RENYI >= 0.142394 (moyenne + 1σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'modérée'
            })
    
    # DIFF_RENYI_2σ_HAUT: 61.2% S
    donnees_condition = [d for d in donnees_renyi if d.get('diff_renyi', 0) >= 0.186029]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_RENYI_2σ_HAUT',
                'description': 'DIFF_RENYI >= 0.186029 (moyenne + 2σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'très_forte'
            })
    
    # DIFF_RENYI_2.5σ_HAUT: 61.0% S
    donnees_condition = [d for d in donnees_renyi if d.get('diff_renyi', 0) >= 0.207846]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_RENYI_2.5σ_HAUT',
                'description': 'DIFF_RENYI >= 0.207846 (moyenne + 2.5σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'exceptionnelle'
            })
    
    # DIFF_RENYI_1.5σ_HAUT: 60.9% S
    donnees_condition = [d for d in donnees_renyi if d.get('diff_renyi', 0) >= 0.164212]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_RENYI_1.5σ_HAUT',
                'description': 'DIFF_RENYI >= 0.164212 (moyenne + 1.5σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'forte'
            })
    
    return conditions_s, conditions_o

def analyser_conditions_sigma_diff_topo(donnees_topo):
    """
    Analyse DIFF_TOPO avec seuils basés sur σ
    """
    conditions_s = []
    conditions_o = []
    
    # Statistiques DIFF_TOPO
    moyenne_topo = 0.246468
    ecart_type_topo = 0.101929
    
    # DIFF_TOPO_2σ_BAS: 100.0% S
    donnees_condition = [d for d in donnees_topo if d.get('diff_topo', 0) < 0.042610]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_TOPO_2σ_BAS',
                'description': 'DIFF_TOPO < 0.042610 (moyenne - 2σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'très_forte'
            })
    
    # DIFF_TOPO_2σ_HAUT: 69.0% S
    donnees_condition = [d for d in donnees_topo if d.get('diff_topo', 0) >= 0.450327]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_TOPO_2σ_HAUT',
                'description': 'DIFF_TOPO >= 0.450327 (moyenne + 2σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'très_forte'
            })
    
    # DIFF_TOPO_2.5σ_HAUT: 67.5% S
    donnees_condition = [d for d in donnees_topo if d.get('diff_topo', 0) >= 0.501292]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_TOPO_2.5σ_HAUT',
                'description': 'DIFF_TOPO >= 0.501292 (moyenne + 2.5σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'exceptionnelle'
            })
    
    # DIFF_TOPO_3σ_HAUT: 67.1% S
    donnees_condition = [d for d in donnees_topo if d.get('diff_topo', 0) >= 0.552257]
    if len(donnees_condition) >= 50:
        patterns_s = [d for d in donnees_condition if d.get('pattern_so') == 'S']
        patterns_o = [d for d in donnees_condition if d.get('pattern_so') == 'O']
        total = len(donnees_condition)
        pct_s = (len(patterns_s) / total) * 100 if total > 0 else 0
        pct_o = (len(patterns_o) / total) * 100 if total > 0 else 0
        
        if pct_s > 55:
            conditions_s.append({
                'nom': 'DIFF_TOPO_3σ_HAUT',
                'description': 'DIFF_TOPO >= 0.552257 (moyenne + 3σ)',
                'pourcentage_s': pct_s,
                'count_s': len(patterns_s),
                'count_o': len(patterns_o),
                'force': 'extrême'
            })
    
    return conditions_s, conditions_o
