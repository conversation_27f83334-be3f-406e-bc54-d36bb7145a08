# Monitoring et Observabilité Python 2024

## 📊 Observabilité Complète avec OpenTelemetry

### Configuration OpenTelemetry Moderne
```python
from opentelemetry import trace, metrics, baggage
from opentelemetry.exporter.jaeger.thrift import <PERSON><PERSON><PERSON>Exporter
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes
import logging
import structlog
from typing import Dict, Any, Optional
import time
import psutil
import asyncio
from contextlib import asynccontextmanager

# Configuration des ressources
resource = Resource.create({
    ResourceAttributes.SERVICE_NAME: "my-python-app",
    ResourceAttributes.SERVICE_VERSION: "1.0.0",
    ResourceAttributes.SERVICE_INSTANCE_ID: "instance-1",
    ResourceAttributes.DEPLOYMENT_ENVIRONMENT: "production"
})

# Configuration du tracing
trace.set_tracer_provider(TracerProvider(resource=resource))
tracer = trace.get_tracer(__name__)

# Exporteur Jaeger pour les traces
jaeger_exporter = JaegerExporter(
    agent_host_name="localhost",
    agent_port=6831,
)

span_processor = BatchSpanProcessor(jaeger_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

# Configuration des métriques
prometheus_reader = PrometheusMetricReader()
metrics.set_meter_provider(MeterProvider(
    resource=resource,
    metric_readers=[prometheus_reader]
))

meter = metrics.get_meter(__name__)

# Métriques personnalisées
request_counter = meter.create_counter(
    name="http_requests_total",
    description="Total number of HTTP requests",
    unit="1"
)

request_duration = meter.create_histogram(
    name="http_request_duration_seconds",
    description="HTTP request duration in seconds",
    unit="s"
)

active_connections = meter.create_up_down_counter(
    name="active_connections",
    description="Number of active connections",
    unit="1"
)

memory_usage = meter.create_observable_gauge(
    name="memory_usage_bytes",
    description="Memory usage in bytes",
    unit="byte"
)

cpu_usage = meter.create_observable_gauge(
    name="cpu_usage_percent",
    description="CPU usage percentage",
    unit="percent"
)

# Callbacks pour métriques observables
def get_memory_usage(options):
    """Callback pour l'utilisation mémoire."""
    return [metrics.Observation(psutil.virtual_memory().used)]

def get_cpu_usage(options):
    """Callback pour l'utilisation CPU."""
    return [metrics.Observation(psutil.cpu_percent())]

memory_usage.set_callback(get_memory_usage)
cpu_usage.set_callback(get_cpu_usage)

# Configuration Structlog
structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.make_filtering_bound_logger(logging.INFO),
    logger_factory=structlog.WriteLoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

class PerformanceMonitor:
    """Moniteur de performance avec métriques détaillées."""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        self.active_requests = 0
    
    @asynccontextmanager
    async def track_request(self, method: str, endpoint: str):
        """Context manager pour tracker une requête."""
        start_time = time.time()
        self.active_requests += 1
        active_connections.add(1)
        
        # Ajouter des attributs au span actuel
        span = trace.get_current_span()
        span.set_attributes({
            "http.method": method,
            "http.route": endpoint,
            "request.id": f"req_{int(time.time() * 1000)}"
        })
        
        try:
            yield
            
            # Métriques de succès
            duration = time.time() - start_time
            request_counter.add(1, {
                "method": method,
                "endpoint": endpoint,
                "status": "success"
            })
            request_duration.record(duration, {
                "method": method,
                "endpoint": endpoint
            })
            
            self.request_count += 1
            
            logger.info(
                "Request completed",
                method=method,
                endpoint=endpoint,
                duration=duration,
                status="success"
            )
            
        except Exception as e:
            # Métriques d'erreur
            duration = time.time() - start_time
            request_counter.add(1, {
                "method": method,
                "endpoint": endpoint,
                "status": "error"
            })
            
            self.error_count += 1
            
            # Ajouter l'erreur au span
            span.record_exception(e)
            span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
            
            logger.error(
                "Request failed",
                method=method,
                endpoint=endpoint,
                duration=duration,
                error=str(e),
                exc_info=True
            )
            
            raise
        
        finally:
            self.active_requests -= 1
            active_connections.add(-1)
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques de performance."""
        uptime = time.time() - self.start_time
        
        return {
            "uptime_seconds": uptime,
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "active_requests": self.active_requests,
            "requests_per_second": self.request_count / uptime,
            "memory_usage_mb": psutil.virtual_memory().used / 1024 / 1024,
            "cpu_percent": psutil.cpu_percent(),
            "disk_usage_percent": psutil.disk_usage('/').percent
        }

# Instance globale du moniteur
performance_monitor = PerformanceMonitor()

# Décorateurs pour instrumentation automatique
def trace_function(operation_name: Optional[str] = None):
    """Décorateur pour tracer une fonction."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            
            with tracer.start_as_current_span(name) as span:
                span.set_attributes({
                    "function.name": func.__name__,
                    "function.module": func.__module__,
                })
                
                try:
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    
                    span.set_status(trace.Status(trace.StatusCode.OK))
                    return result
                
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    raise
        
        def sync_wrapper(*args, **kwargs):
            name = operation_name or f"{func.__module__}.{func.__name__}"
            
            with tracer.start_as_current_span(name) as span:
                span.set_attributes({
                    "function.name": func.__name__,
                    "function.module": func.__module__,
                })
                
                try:
                    result = func(*args, **kwargs)
                    span.set_status(trace.Status(trace.StatusCode.OK))
                    return result
                
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def monitor_performance(func):
    """Décorateur pour monitorer les performances."""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            
            duration = time.time() - start_time
            logger.info(
                "Function executed",
                function=func.__name__,
                duration=duration,
                status="success"
            )
            
            return result
        
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                "Function failed",
                function=func.__name__,
                duration=duration,
                error=str(e),
                exc_info=True
            )
            raise
    
    return wrapper

# Health Check avancé
class HealthChecker:
    """Vérificateur de santé avec checks personnalisés."""
    
    def __init__(self):
        self.checks = {}
    
    def register_check(self, name: str, check_func):
        """Enregistrer un check de santé."""
        self.checks[name] = check_func
    
    async def run_checks(self) -> Dict[str, Any]:
        """Exécuter tous les checks de santé."""
        results = {
            "status": "healthy",
            "timestamp": time.time(),
            "checks": {},
            "summary": {
                "total": len(self.checks),
                "passed": 0,
                "failed": 0
            }
        }
        
        for name, check_func in self.checks.items():
            try:
                if asyncio.iscoroutinefunction(check_func):
                    check_result = await check_func()
                else:
                    check_result = check_func()
                
                results["checks"][name] = {
                    "status": "pass",
                    "details": check_result
                }
                results["summary"]["passed"] += 1
                
            except Exception as e:
                results["checks"][name] = {
                    "status": "fail",
                    "error": str(e)
                }
                results["summary"]["failed"] += 1
                results["status"] = "unhealthy"
        
        return results

# Instance globale du health checker
health_checker = HealthChecker()

# Checks de santé par défaut
async def database_check():
    """Check de santé de la base de données."""
    # Simulation d'un check de base de données
    await asyncio.sleep(0.01)  # Simulation de latence
    return {"connection": "ok", "latency_ms": 10}

def memory_check():
    """Check de santé de la mémoire."""
    memory = psutil.virtual_memory()
    if memory.percent > 90:
        raise Exception(f"Memory usage too high: {memory.percent}%")
    return {"usage_percent": memory.percent, "available_gb": memory.available / 1024**3}

def disk_check():
    """Check de santé du disque."""
    disk = psutil.disk_usage('/')
    if disk.percent > 95:
        raise Exception(f"Disk usage too high: {disk.percent}%")
    return {"usage_percent": disk.percent, "free_gb": disk.free / 1024**3}

# Enregistrer les checks
health_checker.register_check("database", database_check)
health_checker.register_check("memory", memory_check)
health_checker.register_check("disk", disk_check)

# Middleware FastAPI pour observabilité
class ObservabilityMiddleware:
    """Middleware pour observabilité complète."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        method = scope["method"]
        path = scope["path"]
        
        async with performance_monitor.track_request(method, path):
            # Ajouter des headers de tracing
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    span = trace.get_current_span()
                    trace_id = format(span.get_span_context().trace_id, '032x')
                    
                    headers = list(message.get("headers", []))
                    headers.append((b"x-trace-id", trace_id.encode()))
                    message["headers"] = headers
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)

# Exemple d'utilisation avec FastAPI
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse

app = FastAPI(title="Monitored API")

# Instrumentation automatique
FastAPIInstrumentor.instrument_app(app)
SQLAlchemyInstrumentor().instrument()
RedisInstrumentor().instrument()
RequestsInstrumentor().instrument()

# Ajouter le middleware d'observabilité
app.add_middleware(ObservabilityMiddleware)

@app.get("/health")
async def health_endpoint():
    """Endpoint de santé détaillé."""
    health_status = await health_checker.run_checks()
    performance_stats = performance_monitor.get_stats()
    
    return JSONResponse(
        content={
            "health": health_status,
            "performance": performance_stats
        },
        status_code=200 if health_status["status"] == "healthy" else 503
    )

@app.get("/metrics")
async def metrics_endpoint():
    """Endpoint pour les métriques Prometheus."""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    
    return Response(
        generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )

@trace_function("business_operation")
@monitor_performance
async def example_business_function(data: Dict[str, Any]) -> Dict[str, Any]:
    """Exemple de fonction métier instrumentée."""
    
    # Ajouter du contexte au span
    span = trace.get_current_span()
    span.set_attributes({
        "business.operation": "process_data",
        "data.size": len(data),
    })
    
    # Log structuré
    logger.info(
        "Processing business data",
        operation="process_data",
        data_size=len(data)
    )
    
    # Simulation de traitement
    await asyncio.sleep(0.1)
    
    # Métriques métier
    business_operations = meter.create_counter(
        name="business_operations_total",
        description="Total business operations"
    )
    business_operations.add(1, {"operation": "process_data"})
    
    return {"status": "processed", "items": len(data)}

# Configuration pour production
def setup_production_monitoring():
    """Configuration pour environnement de production."""
    
    # Configuration logging pour production
    import logging.config
    
    LOGGING_CONFIG = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.processors.JSONRenderer(),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "json",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": "/var/log/app/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "formatter": "json",
            },
        },
        "loggers": {
            "": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": True,
            },
        },
    }
    
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Configuration des alertes
    setup_alerting()

def setup_alerting():
    """Configuration des alertes."""
    
    # Exemple d'intégration avec un système d'alertes
    class AlertManager:
        def __init__(self):
            self.thresholds = {
                "error_rate": 0.05,  # 5%
                "response_time": 2.0,  # 2 secondes
                "memory_usage": 0.85,  # 85%
            }
        
        def check_and_alert(self, metrics: Dict[str, Any]):
            """Vérifier les métriques et envoyer des alertes."""
            
            if metrics["error_rate"] > self.thresholds["error_rate"]:
                self.send_alert(
                    "High Error Rate",
                    f"Error rate: {metrics['error_rate']:.2%}"
                )
            
            if metrics["memory_usage_mb"] / 1024 > self.thresholds["memory_usage"]:
                self.send_alert(
                    "High Memory Usage",
                    f"Memory usage: {metrics['memory_usage_mb']:.0f}MB"
                )
        
        def send_alert(self, title: str, message: str):
            """Envoyer une alerte."""
            logger.critical(
                "ALERT",
                alert_title=title,
                alert_message=message,
                timestamp=time.time()
            )
            
            # Ici, intégrer avec Slack, PagerDuty, etc.

if __name__ == "__main__":
    setup_production_monitoring()
```

Cette configuration d'observabilité moderne offre une visibilité complète sur les applications Python avec tracing distribué, métriques détaillées, logging structuré et health checks automatisés.
