#!/usr/bin/env python3
"""
Script pour analyser la structure du dataset et trouver index5_result
"""

import json

def analyser_structure_dataset():
    """Analyser la structure du dataset pour trouver index5_result"""
    
    print("🔍 ANALYSE STRUCTURE DATASET POUR INDEX5_RESULT")
    print("=" * 60)
    
    try:
        # Charger le dataset
        with open('dataset_baccarat_lupasco_20250626_044753.json', 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        print(f"📊 Dataset chargé: {len(dataset)} parties")
        
        # Analyser la première partie
        if dataset and len(dataset) > 0:
            premiere_partie = dataset[0]
            print(f"\n🔍 STRUCTURE PREMIÈRE PARTIE:")
            print(f"   Clés disponibles: {list(premiere_partie.keys())}")
            
            # Analyser les mains
            if 'mains' in premiere_partie:
                mains = premiere_partie['mains']
                print(f"   Nombre de mains: {len(mains)}")

                if mains and len(mains) > 0:
                    premiere_main = mains[0]
                    print(f"\n🔍 STRUCTURE PREMIÈRE MAIN:")
                    print(f"   Clés disponibles: {list(premiere_main.keys())}")
                    
                    # Chercher index5_result
                    if 'index5_result' in premiere_main:
                        print(f"   ✅ index5_result trouvé: {premiere_main['index5_result']}")
                    else:
                        print(f"   ❌ index5_result non trouvé")
                        
                        # Chercher d'autres champs liés à INDEX5
                        for key in premiere_main.keys():
                            if 'index' in key.lower():
                                print(f"   🔍 Champ INDEX trouvé: {key} = {premiere_main[key]}")
                    
                    # Analyser quelques mains pour voir la structure
                    print(f"\n🔍 ANALYSE DES 5 PREMIÈRES MAINS:")
                    for i, main in enumerate(mains[:5]):
                        if isinstance(main, dict):
                            print(f"   Main {i+1}:")
                            for key, value in main.items():
                                if 'index' in key.lower():
                                    print(f"      {key}: {value}")
                        else:
                            print(f"   Main {i+1}: {main} (type: {type(main)})")
            
            # Analyser d'autres sections
            for key, value in premiere_partie.items():
                if key != 'mains':
                    if isinstance(value, dict):
                        print(f"\n🔍 SECTION {key.upper()}:")
                        print(f"   Clés: {list(value.keys())}")
                    elif isinstance(value, list) and value:
                        print(f"\n🔍 SECTION {key.upper()} (liste):")
                        if isinstance(value[0], dict):
                            print(f"   Premier élément clés: {list(value[0].keys())}")
                    else:
                        print(f"\n🔍 {key}: {value}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    analyser_structure_dataset()
