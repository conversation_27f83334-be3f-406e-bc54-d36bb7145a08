# Packaging et Distribution Python - Bonnes Pratiques 2024

## 📦 Configuration Moderne avec pyproject.toml

### Structure de Projet Recommandée 2024
```
my-package/
├── pyproject.toml              # Configuration principale (remplace setup.py)
├── README.md                   # Documentation utilisateur
├── LICENSE                     # Licence du projet
├── CHANGELOG.md               # Historique des versions
├── .gitignore                 # Fichiers à ignorer
├── .github/                   # CI/CD et templates
│   ├── workflows/
│   │   ├── ci.yml            # Tests et qualité
│   │   ├── release.yml       # Publication automatique
│   │   └── security.yml      # Audit de sécurité
│   ├── ISSUE_TEMPLATE/
│   └── PULL_REQUEST_TEMPLATE.md
├── docs/                      # Documentation Sphinx
│   ├── conf.py
│   ├── index.rst
│   └── api/
├── src/                       # Code source (layout src/)
│   └── my_package/
│       ├── __init__.py
│       ├── py.typed          # Marker pour type hints
│       ├── core/
│       ├── utils/
│       └── cli.py
├── tests/                     # Tests complets
│   ├── __init__.py
│   ├── conftest.py
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── scripts/                   # Scripts de développement
├── requirements/              # Dépendances par environnement
│   ├── base.txt
│   ├── dev.txt
│   └── docs.txt
└── Makefile                   # Automatisation des tâches
```

### pyproject.toml Complet et Moderne
```toml
[build-system]
requires = ["setuptools>=64", "setuptools-scm>=8"]
build-backend = "setuptools.build_meta"

[project]
name = "my-awesome-package"
description = "A modern Python package with best practices"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Your Name", email = "<EMAIL>"},
    {name = "Contributor", email = "<EMAIL>"}
]
maintainers = [
    {name = "Maintainer", email = "<EMAIL>"}
]
keywords = ["python", "package", "modern", "best-practices"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
    "Typing :: Typed"
]
requires-python = ">=3.11"
dependencies = [
    "requests>=2.31.0",
    "pydantic>=2.0.0",
    "click>=8.1.0",
    "rich>=13.0.0"
]
dynamic = ["version"]

[project.optional-dependencies]
# Dépendances de développement
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "pytest-mock>=3.11.0",
    "black>=23.7.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
    "tox>=4.11.0"
]

# Documentation
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.24.0",
    "myst-parser>=2.0.0"
]

# Performance et optimisation
performance = [
    "numba>=0.58.0",
    "numpy>=1.24.0",
    "cython>=3.0.0"
]

# Intégrations
integrations = [
    "fastapi>=0.100.0",
    "sqlalchemy>=2.0.0",
    "redis>=4.6.0"
]

# Toutes les dépendances optionnelles
all = [
    "my-awesome-package[dev,docs,performance,integrations]"
]

[project.urls]
Homepage = "https://github.com/username/my-awesome-package"
Documentation = "https://my-awesome-package.readthedocs.io/"
Repository = "https://github.com/username/my-awesome-package.git"
"Bug Tracker" = "https://github.com/username/my-awesome-package/issues"
Changelog = "https://github.com/username/my-awesome-package/blob/main/CHANGELOG.md"
Funding = "https://github.com/sponsors/username"

[project.scripts]
my-cli = "my_package.cli:main"
my-tool = "my_package.tools:run"

[project.gui-scripts]
my-gui = "my_package.gui:main"

[project.entry-points."my_package.plugins"]
plugin1 = "my_package.plugins:plugin1"
plugin2 = "my_package.plugins:plugin2"

# Configuration setuptools
[tool.setuptools]
zip-safe = false
include-package-data = true

[tool.setuptools.packages.find]
where = ["src"]
include = ["my_package*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
my_package = ["py.typed", "*.pyi", "data/*.json", "templates/*.html"]

[tool.setuptools.dynamic]
version = {attr = "my_package.__version__"}

# Configuration des outils de développement
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # Répertoires à exclure
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "N",    # pep8-naming
    "S",    # bandit
    "T20",  # flake8-print
    "PT",   # flake8-pytest-style
    "RET",  # flake8-return
    "SIM",  # flake8-simplify
    "ARG",  # flake8-unused-arguments
    "DTZ",  # flake8-datetimez
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "TRY",  # tryceratops
    "FLY",  # flynt
    "PERF", # perflint
]
ignore = [
    "E501",   # line too long (handled by black)
    "S101",   # assert usage (OK in tests)
    "PLR0913", # too many arguments
    "PLR0915", # too many statements
]

[tool.ruff.per-file-ignores]
"tests/**/*" = ["S101", "ARG001", "PLR2004"]
"__init__.py" = ["F401"]
"docs/**/*" = ["INP001"]

[tool.ruff.isort]
known-first-party = ["my_package"]
force-sort-within-sections = true

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
show_error_codes = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=my_package",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=90",
    "-ra",
    "--tb=short"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests"
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning"
]

[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__main__.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]

[tool.tox]
legacy_tox_ini = """
[tox]
envlist = py311,py312,py313,lint,type-check,docs
isolated_build = true

[testenv]
deps = 
    pytest>=7.4.0
    pytest-cov>=4.1.0
    pytest-xdist>=3.3.0
commands = pytest {posargs}

[testenv:lint]
deps = 
    black>=23.7.0
    ruff>=0.1.0
commands = 
    black --check src tests
    ruff check src tests

[testenv:type-check]
deps = 
    mypy>=1.5.0
    types-requests
commands = mypy src

[testenv:docs]
deps = 
    sphinx>=7.1.0
    sphinx-rtd-theme>=1.3.0
commands = sphinx-build -W -b html docs docs/_build/html
"""
```

### Scripts de Build et Publication
```python
# scripts/build.py
"""Script de build automatisé."""
import subprocess
import sys
from pathlib import Path

def run_command(cmd: str, check: bool = True) -> subprocess.CompletedProcess:
    """Exécuter une commande shell."""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        sys.exit(1)
    
    return result

def clean_build():
    """Nettoyer les artefacts de build."""
    print("Cleaning build artifacts...")
    
    # Supprimer les répertoires de build
    for path in ["build", "dist", "*.egg-info"]:
        run_command(f"rm -rf {path}", check=False)

def run_tests():
    """Exécuter les tests."""
    print("Running tests...")
    run_command("pytest --cov=my_package --cov-fail-under=90")

def run_linting():
    """Exécuter le linting."""
    print("Running linting...")
    run_command("black --check src tests")
    run_command("ruff check src tests")
    run_command("mypy src")

def build_package():
    """Construire le package."""
    print("Building package...")
    run_command("python -m build")

def check_package():
    """Vérifier le package construit."""
    print("Checking package...")
    run_command("twine check dist/*")

def main():
    """Script principal de build."""
    print("Starting build process...")
    
    # Étapes de build
    clean_build()
    run_linting()
    run_tests()
    build_package()
    check_package()
    
    print("Build completed successfully!")

if __name__ == "__main__":
    main()
```

### Makefile pour Automatisation
```makefile
# Makefile pour automatisation des tâches
.PHONY: help install install-dev test lint type-check format clean build publish docs

# Variables
PYTHON := python3
PIP := pip
PACKAGE_NAME := my_package

help: ## Afficher l'aide
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Installer le package en mode développement
	$(PIP) install -e .

install-dev: ## Installer avec dépendances de développement
	$(PIP) install -e ".[dev,docs]"
	pre-commit install

test: ## Exécuter les tests
	pytest --cov=$(PACKAGE_NAME) --cov-report=html --cov-report=term-missing

test-fast: ## Exécuter les tests rapides uniquement
	pytest -m "not slow" --cov=$(PACKAGE_NAME)

lint: ## Vérifier le style de code
	black --check src tests
	ruff check src tests

format: ## Formater le code
	black src tests
	ruff check --fix src tests

type-check: ## Vérification des types
	mypy src

quality: lint type-check ## Vérifier qualité complète

clean: ## Nettoyer les artefacts
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

build: clean ## Construire le package
	$(PYTHON) -m build

check-build: build ## Vérifier le package construit
	twine check dist/*

publish-test: check-build ## Publier sur TestPyPI
	twine upload --repository testpypi dist/*

publish: check-build ## Publier sur PyPI
	twine upload dist/*

docs: ## Générer la documentation
	cd docs && make html

docs-serve: docs ## Servir la documentation localement
	cd docs/_build/html && $(PYTHON) -m http.server 8000

security: ## Audit de sécurité
	bandit -r src/
	safety check

bump-patch: ## Incrémenter version patch
	bump2version patch

bump-minor: ## Incrémenter version minor
	bump2version minor

bump-major: ## Incrémenter version major
	bump2version major

release: quality test build publish ## Release complète

# Tâches de CI/CD
ci-test: ## Tests pour CI
	pytest --cov=$(PACKAGE_NAME) --cov-report=xml --junitxml=junit.xml

ci-quality: ## Qualité pour CI
	black --check src tests
	ruff check src tests
	mypy src
	bandit -r src/ -f json -o bandit-report.json

# Environnement de développement
dev-setup: ## Configuration environnement de développement
	$(PYTHON) -m venv venv
	. venv/bin/activate && pip install --upgrade pip setuptools wheel
	. venv/bin/activate && make install-dev

# Docker
docker-build: ## Construire l'image Docker
	docker build -t $(PACKAGE_NAME):latest .

docker-test: ## Tester dans Docker
	docker run --rm $(PACKAGE_NAME):latest pytest

# Monitoring et métriques
complexity: ## Analyser la complexité du code
	radon cc src/ -a
	radon mi src/

dependencies: ## Analyser les dépendances
	pipdeptree --graph-output png > dependencies.png
```

### GitHub Actions pour CI/CD
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12", "3.13"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Lint with ruff
      run: ruff check src tests
    
    - name: Format check with black
      run: black --check src tests
    
    - name: Type check with mypy
      run: mypy src
    
    - name: Test with pytest
      run: |
        pytest --cov=my_package --cov-report=xml --junitxml=junit.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: true

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Run security audit
      run: |
        pip install bandit safety
        bandit -r src/
        safety check

  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    
    - name: Build package
      run: |
        python -m pip install --upgrade pip build twine
        python -m build
        twine check dist/*
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/
```

Ces configurations modernes permettent de créer des packages Python professionnels avec toutes les bonnes pratiques de 2024.
