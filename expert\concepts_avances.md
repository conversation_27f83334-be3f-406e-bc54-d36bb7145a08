# Concepts Avancés Python - Niveau Expert

## 🧠 Métaclasses et Métaprogrammation

### Métaclasses
```python
class SingletonMeta(type):
    """Métaclasse pour implémenter le pattern Singleton"""
    _instances = {}
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]

class Database(metaclass=SingletonMeta):
    def __init__(self):
        self.connection = "Connected"

# Usage
db1 = Database()
db2 = Database()
print(db1 is db2)  # True
```

### Descripteurs
```python
class ValidatedAttribute:
    def __init__(self, validation_func, error_msg):
        self.validation_func = validation_func
        self.error_msg = error_msg
        
    def __set_name__(self, owner, name):
        self.name = name
        self.private_name = f'_{name}'
        
    def __get__(self, obj, objtype=None):
        if obj is None:
            return self
        return getattr(obj, self.private_name)
        
    def __set__(self, obj, value):
        if not self.validation_func(value):
            raise ValueError(self.error_msg)
        setattr(obj, self.private_name, value)

class Person:
    age = ValidatedAttribute(lambda x: isinstance(x, int) and x >= 0, 
                           "Age must be a non-negative integer")
    
    def __init__(self, age):
        self.age = age
```

## 🎭 Décorateurs Avancés

### Décorateur avec paramètres
```python
import functools
import time
from typing import Callable, Any

def retry(max_attempts: int = 3, delay: float = 1.0):
    """Décorateur pour retry automatique avec backoff"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        time.sleep(delay * (2 ** attempt))  # Exponential backoff
                    
            raise last_exception
        return wrapper
    return decorator

@retry(max_attempts=5, delay=0.5)
def unstable_api_call():
    # Simulation d'un appel API instable
    import random
    if random.random() < 0.7:
        raise ConnectionError("API temporarily unavailable")
    return "Success!"
```

### Décorateur de cache avancé
```python
import functools
import threading
import time
from typing import Dict, Any, Optional

class TTLCache:
    """Cache avec Time-To-Live"""
    def __init__(self, ttl: float = 300):  # 5 minutes par défaut
        self.ttl = ttl
        self.cache: Dict[str, tuple] = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key in self.cache:
                value, timestamp = self.cache[key]
                if time.time() - timestamp < self.ttl:
                    return value
                else:
                    del self.cache[key]
            return None
    
    def set(self, key: str, value: Any) -> None:
        with self.lock:
            self.cache[key] = (value, time.time())

def ttl_cache(ttl: float = 300):
    """Décorateur de cache avec TTL"""
    cache = TTLCache(ttl)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Créer une clé unique basée sur les arguments
            key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # Vérifier le cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Calculer et mettre en cache
            result = func(*args, **kwargs)
            cache.set(key, result)
            return result
        
        # Ajouter méthodes utilitaires
        wrapper.cache_clear = lambda: cache.cache.clear()
        wrapper.cache_info = lambda: len(cache.cache)
        return wrapper
    return decorator
```

## 🔄 Générateurs et Itérateurs Avancés

### Générateur avec send() et throw()
```python
def coroutine_processor():
    """Générateur avancé avec gestion d'état"""
    result = None
    while True:
        try:
            value = yield result
            if value is None:
                break
            # Traitement complexe
            result = f"Processed: {value}"
        except ValueError as e:
            result = f"Error: {e}"
        except GeneratorExit:
            print("Coroutine fermée proprement")
            break

# Usage
processor = coroutine_processor()
next(processor)  # Amorcer le générateur

print(processor.send("Hello"))  # "Processed: Hello"
print(processor.throw(ValueError, "Invalid input"))  # "Error: Invalid input"
processor.close()
```

### Pipeline de générateurs
```python
def read_large_file(filename: str):
    """Générateur pour lire un gros fichier ligne par ligne"""
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            yield line.strip()

def filter_lines(lines, pattern: str):
    """Filtre les lignes contenant un pattern"""
    for line in lines:
        if pattern in line:
            yield line

def transform_lines(lines, transform_func):
    """Applique une transformation à chaque ligne"""
    for line in lines:
        yield transform_func(line)

def batch_lines(lines, batch_size: int = 1000):
    """Groupe les lignes par batch"""
    batch = []
    for line in lines:
        batch.append(line)
        if len(batch) >= batch_size:
            yield batch
            batch = []
    if batch:
        yield batch

# Pipeline complet
def process_large_log_file(filename: str, error_pattern: str):
    """Pipeline de traitement de gros fichiers de log"""
    lines = read_large_file(filename)
    error_lines = filter_lines(lines, error_pattern)
    processed_lines = transform_lines(error_lines, str.upper)
    batches = batch_lines(processed_lines, 100)
    
    for batch in batches:
        # Traitement par batch pour optimiser la mémoire
        yield len(batch), batch[:3]  # Retourne taille et échantillon
```

## 🔧 Context Managers Avancés

### Context Manager avec gestion d'état
```python
import contextlib
import threading
import time
from typing import Optional, Any

class DatabaseTransaction:
    """Context manager pour transactions de base de données"""
    
    def __init__(self, connection, isolation_level: str = "READ_COMMITTED"):
        self.connection = connection
        self.isolation_level = isolation_level
        self.transaction = None
        self.savepoints = []
    
    def __enter__(self):
        self.transaction = self.connection.begin()
        self.connection.execute(f"SET TRANSACTION ISOLATION LEVEL {self.isolation_level}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            try:
                self.transaction.commit()
            except Exception:
                self.transaction.rollback()
                raise
        else:
            self.transaction.rollback()
        return False  # Ne pas supprimer l'exception
    
    def savepoint(self, name: str):
        """Créer un savepoint"""
        sp = self.connection.execute(f"SAVEPOINT {name}")
        self.savepoints.append((name, sp))
        return sp
    
    def rollback_to_savepoint(self, name: str):
        """Rollback vers un savepoint"""
        self.connection.execute(f"ROLLBACK TO SAVEPOINT {name}")

# Context manager pour gestion de ressources
@contextlib.contextmanager
def managed_resource(resource_factory, cleanup_func=None):
    """Context manager générique pour gestion de ressources"""
    resource = None
    try:
        resource = resource_factory()
        yield resource
    finally:
        if resource is not None and cleanup_func:
            cleanup_func(resource)

# Usage
def create_connection():
    return "database_connection"

def close_connection(conn):
    print(f"Closing {conn}")

with managed_resource(create_connection, close_connection) as conn:
    print(f"Using {conn}")
```

## 🧮 Gestion Mémoire et Optimisation

### Weak References
```python
import weakref
import gc

class Observer:
    def __init__(self, name):
        self.name = name
    
    def notify(self, message):
        print(f"{self.name} received: {message}")

class Subject:
    def __init__(self):
        self._observers = weakref.WeakSet()
    
    def attach(self, observer):
        self._observers.add(observer)
    
    def notify_all(self, message):
        # Les observers supprimés sont automatiquement retirés
        for observer in self._observers:
            observer.notify(message)

# Memory profiling
import tracemalloc

def memory_profiler(func):
    """Décorateur pour profiler l'usage mémoire"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        tracemalloc.start()
        
        result = func(*args, **kwargs)
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        print(f"{func.__name__}:")
        print(f"  Current memory: {current / 1024 / 1024:.2f} MB")
        print(f"  Peak memory: {peak / 1024 / 1024:.2f} MB")
        
        return result
    return wrapper
```

## 🔀 Programmation Asynchrone Avancée

### Async Context Managers
```python
import asyncio
import aiohttp
from typing import AsyncGenerator

class AsyncDatabasePool:
    """Pool de connexions asynchrone"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.pool = asyncio.Queue(maxsize=max_connections)
        self.created_connections = 0
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Fermer toutes les connexions
        while not self.pool.empty():
            conn = await self.pool.get()
            await conn.close()
    
    @contextlib.asynccontextmanager
    async def get_connection(self):
        """Context manager pour obtenir une connexion"""
        if self.pool.empty() and self.created_connections < self.max_connections:
            # Créer nouvelle connexion
            conn = await self._create_connection()
            self.created_connections += 1
        else:
            # Réutiliser connexion existante
            conn = await self.pool.get()
        
        try:
            yield conn
        finally:
            await self.pool.put(conn)
    
    async def _create_connection(self):
        # Simulation création connexion
        await asyncio.sleep(0.1)
        return f"connection_{self.created_connections}"

# Générateur asynchrone
async def async_data_stream(urls: list) -> AsyncGenerator[dict, None]:
    """Stream asynchrone de données depuis plusieurs URLs"""
    async with aiohttp.ClientSession() as session:
        tasks = []
        for url in urls:
            task = asyncio.create_task(fetch_data(session, url))
            tasks.append(task)
        
        for completed_task in asyncio.as_completed(tasks):
            try:
                data = await completed_task
                yield data
            except Exception as e:
                yield {"error": str(e)}

async def fetch_data(session, url):
    async with session.get(url) as response:
        return await response.json()
```

## 📊 Techniques d'Optimisation

### Slots pour économiser la mémoire
```python
class OptimizedPoint:
    """Classe optimisée avec __slots__"""
    __slots__ = ['x', 'y', '_hash']
    
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self._hash = None
    
    def __hash__(self):
        if self._hash is None:
            self._hash = hash((self.x, self.y))
        return self._hash
    
    def __eq__(self, other):
        return isinstance(other, OptimizedPoint) and self.x == other.x and self.y == other.y

# Comparaison mémoire
import sys

class RegularPoint:
    def __init__(self, x, y):
        self.x = x
        self.y = y

regular = RegularPoint(1, 2)
optimized = OptimizedPoint(1, 2)

print(f"Regular: {sys.getsizeof(regular)} bytes")
print(f"Optimized: {sys.getsizeof(optimized)} bytes")
```

Ces concepts avancés sont essentiels pour maîtriser Python au niveau expert. Ils permettent d'écrire du code plus performant, maintenable et élégant.
