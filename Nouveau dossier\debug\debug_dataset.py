#!/usr/bin/env python3
"""
Script pour analyser la structure du dataset et comprendre les IDs
"""
import json

def analyser_dataset():
    print("🔍 ANALYSE STRUCTURE DATASET")
    print("=" * 50)
    
    try:
        with open('dataset_baccarat_lupasco_20250626_044753.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Clés principales: {list(data.keys())}")
        
        parties = data.get('parties', [])
        print(f"📊 Nombre de parties: {len(parties)}")
        
        if parties:
            print("\n🔍 ANALYSE PREMIÈRE PARTIE:")
            premiere_partie = parties[0]
            print(f"📊 Clés de la partie: {list(premiere_partie.keys())}")
            
            # Analyser l'ID
            id_partie = premiere_partie.get('id', 'ABSENT')
            print(f"📊 ID de la partie: '{id_partie}' (type: {type(id_partie)})")
            
            # Analyser d'autres champs potentiels d'ID
            for cle in premiere_partie.keys():
                if 'id' in cle.lower() or 'num' in cle.lower() or 'index' in cle.lower():
                    valeur = premiere_partie[cle]
                    print(f"📊 {cle}: '{valeur}' (type: {type(valeur)})")
            
            print(f"\n📊 Échantillon première partie:")
            for cle, valeur in list(premiere_partie.items())[:5]:
                if isinstance(valeur, (str, int, float)):
                    print(f"   {cle}: {valeur}")
                else:
                    print(f"   {cle}: {type(valeur)} (longueur: {len(valeur) if hasattr(valeur, '__len__') else 'N/A'})")
        
        # Analyser plusieurs parties pour voir les variations d'ID
        print(f"\n🔍 ANALYSE IDs DE 10 PREMIÈRES PARTIES:")
        for i in range(min(10, len(parties))):
            partie = parties[i]
            id_partie = partie.get('id', 'ABSENT')
            print(f"   Partie {i}: ID = '{id_partie}'")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    analyser_dataset()
