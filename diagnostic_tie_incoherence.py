#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 DIAGNOSTIC PRÉCIS DE L'INCOHÉRENCE TIE
=========================================
Analyse mathématique détaillée du comptage TIE erroné
"""

import json
from analyseur_fractal_baccarat import AnalyseurFractalBaccarat

def diagnostic_tie_incoherence():
    """
    🔍 DIAGNOSTIC MATHÉMATIQUE PRÉCIS
    """
    print("🔍 DIAGNOSTIC PRÉCIS DE L'INCOHÉRENCE TIE")
    print("=" * 50)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    parties = dataset.get('parties', [])
    
    # Reproduire exactement les conditions de l'évaluation officielle
    pourcentage_test = 0.3  # 30% comme dans l'évaluation
    nb_parties_total = len(parties)
    nb_parties_test = int(nb_parties_total * pourcentage_test)
    parties_test = parties[-nb_parties_test:]  # 300 dernières parties
    
    print(f"📊 REPRODUCTION EXACTE DE L'ÉVALUATION OFFICIELLE")
    print(f"Parties totales: {nb_parties_total}")
    print(f"Parties de test: {len(parties_test)} (30%)")
    
    # Compteurs détaillés
    total_mains_parties = 0
    total_mains_analysables = 0
    total_mains_trop_courtes = 0
    total_tie_reels = 0
    total_banker_reels = 0
    total_player_reels = 0
    total_mains_sans_resultat = 0
    
    predictions_evaluees = 0
    ties_rencontres = 0
    
    print(f"\n🔬 ANALYSE DÉTAILLÉE PARTIE PAR PARTIE")
    
    for idx, partie in enumerate(parties_test):
        mains = partie.get('mains', [])
        total_mains_parties += len(mains)
        
        if len(mains) < 10:
            total_mains_trop_courtes += len(mains)
            continue
            
        # Compter les résultats réels dans cette partie
        tie_partie = 0
        banker_partie = 0
        player_partie = 0
        sans_resultat_partie = 0
        
        for main in mains:
            resultat = main.get('index3_result')
            if resultat == 'TIE':
                tie_partie += 1
            elif resultat == 'BANKER':
                banker_partie += 1
            elif resultat == 'PLAYER':
                player_partie += 1
            elif not resultat:
                sans_resultat_partie += 1
                
        total_tie_reels += tie_partie
        total_banker_reels += banker_partie
        total_player_reels += player_partie
        total_mains_sans_resultat += sans_resultat_partie
        
        # Simuler exactement l'évaluation
        for i in range(10, len(mains)):
            mains_analyse = mains[:i]
            main_cible = mains[i]
            resultat_reel = main_cible.get('index3_result')
            
            total_mains_analysables += 1
            
            if not resultat_reel:
                continue
                
            if resultat_reel == 'TIE':
                ties_rencontres += 1
                continue  # TIE = Remboursement, pas compté
                
            # Ici on devrait faire l'analyse, mais on compte juste
            predictions_evaluees += 1
        
        # Afficher quelques exemples
        if idx < 5:
            print(f"  Partie {idx+1}: {len(mains)} mains, TIE={tie_partie}, BANKER={banker_partie}, PLAYER={player_partie}")
    
    print(f"\n📊 RÉSULTATS MATHÉMATIQUES DÉTAILLÉS")
    print("=" * 40)
    print(f"Total mains dans les 300 parties: {total_mains_parties}")
    print(f"Total mains analysables (≥10 par partie): {total_mains_analysables}")
    print(f"Total mains trop courtes: {total_mains_trop_courtes}")
    print(f"Total mains sans résultat: {total_mains_sans_resultat}")
    
    print(f"\nRÉSULTATS RÉELS DANS LES DONNÉES:")
    print(f"TIE réels: {total_tie_reels}")
    print(f"BANKER réels: {total_banker_reels}")
    print(f"PLAYER réels: {total_player_reels}")
    
    print(f"\nCOMPTAGE SIMULATION ÉVALUATION:")
    print(f"TIE rencontrés (exclus): {ties_rencontres}")
    print(f"Prédictions évaluables: {predictions_evaluees}")
    print(f"Total: {ties_rencontres + predictions_evaluees}")
    
    # Vérifications mathématiques
    print(f"\n🔍 VÉRIFICATIONS MATHÉMATIQUES")
    print("=" * 30)
    
    proportion_tie_reel = (total_tie_reels / total_mains_parties) * 100 if total_mains_parties > 0 else 0
    proportion_tie_compte = (ties_rencontres / total_mains_analysables) * 100 if total_mains_analysables > 0 else 0
    
    print(f"Proportion TIE réelle: {proportion_tie_reel:.1f}%")
    print(f"Proportion TIE comptée: {proportion_tie_compte:.1f}%")
    
    # Comparaison avec les résultats officiels
    print(f"\n🎯 COMPARAISON AVEC RÉSULTATS OFFICIELS")
    print("=" * 40)
    print(f"TIE comptés (simulation): {ties_rencontres}")
    print(f"TIE officiels (rapport): 1,572")
    print(f"Différence: {1572 - ties_rencontres}")
    
    print(f"Prédictions évaluables (simulation): {predictions_evaluees}")
    print(f"Prédictions officielles (rapport): 15,146")
    print(f"Différence: {15146 - predictions_evaluees}")
    
    if abs(ties_rencontres - 1572) > 100:
        print("❌ INCOHÉRENCE MAJEURE dans le comptage TIE")
    else:
        print("✅ Comptage TIE cohérent")

def test_evaluation_officielle():
    """
    🧪 TEST DE L'ÉVALUATION OFFICIELLE
    """
    print(f"\n🧪 TEST DE L'ÉVALUATION OFFICIELLE")
    print("=" * 40)
    
    analyseur = AnalyseurFractalBaccarat()
    dataset = analyseur.charger_dataset()
    
    print("Lancement de l'évaluation officielle...")
    performance = analyseur.evaluer_performance_predictions_corrigee(dataset, pourcentage_test=0.3)
    
    print(f"📊 RÉSULTATS OFFICIELS:")
    for key, value in performance.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    try:
        diagnostic_tie_incoherence()
        test_evaluation_officielle()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
